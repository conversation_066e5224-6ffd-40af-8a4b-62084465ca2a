package com.example.flight_fear_wellness_app

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.speech.RecognitionListener
import android.speech.RecognizerIntent
import android.speech.SpeechRecognizer
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import java.util.*

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.example.flight_fear_wellness_app/speech"
    private val MICROPHONE_PERMISSION_CODE = 200
    private var speechRecognizer: SpeechRecognizer? = null
    private var methodChannel: MethodChannel? = null
    private var isListening = false

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
        methodChannel?.setMethodCallHandler { call, result ->
            when (call.method) {
                "initialize" -> {
                    initializeSpeechRecognizer(result)
                }
                "startListening" -> {
                    startListening(result)
                }
                "stopListening" -> {
                    stopListening(result)
                }
                "isAvailable" -> {
                    result.success(SpeechRecognizer.isRecognitionAvailable(this))
                }
                "dispose" -> {
                    disposeSpeechRecognizer(result)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun initializeSpeechRecognizer(result: MethodChannel.Result) {
        if (checkMicrophonePermission()) {
            try {
                if (speechRecognizer == null) {
                    speechRecognizer = SpeechRecognizer.createSpeechRecognizer(this)
                    speechRecognizer?.setRecognitionListener(createRecognitionListener())
                }
                result.success(SpeechRecognizer.isRecognitionAvailable(this))
            } catch (e: Exception) {
                result.error("INITIALIZATION_ERROR", "Failed to initialize speech recognizer: ${e.message}", null)
            }
        } else {
            requestMicrophonePermission()
            result.success(false)
        }
    }

    private fun startListening(result: MethodChannel.Result) {
        if (!checkMicrophonePermission()) {
            result.error("PERMISSION_DENIED", "Microphone permission not granted", null)
            return
        }

        if (!SpeechRecognizer.isRecognitionAvailable(this)) {
            result.error("NOT_AVAILABLE", "Speech recognition not available on this device", null)
            return
        }

        try {
            if (speechRecognizer == null) {
                speechRecognizer = SpeechRecognizer.createSpeechRecognizer(this)
                speechRecognizer?.setRecognitionListener(createRecognitionListener())
            }

            val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
                putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
                putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault())
                putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true)
                putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1)
                putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS, 3000)
                putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_POSSIBLY_COMPLETE_SILENCE_LENGTH_MILLIS, 3000)
            }

            speechRecognizer?.startListening(intent)
            isListening = true
            result.success(true)
        } catch (e: Exception) {
            result.error("START_ERROR", "Failed to start listening: ${e.message}", null)
        }
    }

    private fun stopListening(result: MethodChannel.Result) {
        try {
            speechRecognizer?.stopListening()
            isListening = false
            result.success(true)
        } catch (e: Exception) {
            result.error("STOP_ERROR", "Failed to stop listening: ${e.message}", null)
        }
    }

    private fun disposeSpeechRecognizer(result: MethodChannel.Result) {
        try {
            speechRecognizer?.destroy()
            speechRecognizer = null
            isListening = false
            result.success(true)
        } catch (e: Exception) {
            result.error("DISPOSE_ERROR", "Failed to dispose speech recognizer: ${e.message}", null)
        }
    }

    private fun createRecognitionListener(): RecognitionListener {
        return object : RecognitionListener {
            override fun onReadyForSpeech(params: Bundle?) {
                methodChannel?.invokeMethod("onReadyForSpeech", null)
            }

            override fun onBeginningOfSpeech() {
                methodChannel?.invokeMethod("onBeginningOfSpeech", null)
            }

            override fun onRmsChanged(rmsdB: Float) {
                // Optional: can be used for volume level indication
            }

            override fun onBufferReceived(buffer: ByteArray?) {
                // Not used
            }

            override fun onEndOfSpeech() {
                isListening = false
                methodChannel?.invokeMethod("onEndOfSpeech", null)
            }

            override fun onError(error: Int) {
                isListening = false
                val errorMessage = when (error) {
                    SpeechRecognizer.ERROR_AUDIO -> "Audio recording error"
                    SpeechRecognizer.ERROR_CLIENT -> "Client side error"
                    SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS -> "Insufficient permissions"
                    SpeechRecognizer.ERROR_NETWORK -> "Network error"
                    SpeechRecognizer.ERROR_NETWORK_TIMEOUT -> "Network timeout"
                    SpeechRecognizer.ERROR_NO_MATCH -> "No speech input"
                    SpeechRecognizer.ERROR_RECOGNIZER_BUSY -> "Recognition service busy"
                    SpeechRecognizer.ERROR_SERVER -> "Server error"
                    SpeechRecognizer.ERROR_SPEECH_TIMEOUT -> "No speech input"
                    else -> "Unknown error"
                }
                methodChannel?.invokeMethod("onError", errorMessage)
            }

            override fun onResults(results: Bundle?) {
                isListening = false
                val matches = results?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                if (!matches.isNullOrEmpty()) {
                    methodChannel?.invokeMethod("onResults", matches[0])
                }
            }

            override fun onPartialResults(partialResults: Bundle?) {
                val matches = partialResults?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                if (!matches.isNullOrEmpty()) {
                    methodChannel?.invokeMethod("onPartialResults", matches[0])
                }
            }

            override fun onEvent(eventType: Int, params: Bundle?) {
                // Not used
            }
        }
    }

    private fun checkMicrophonePermission(): Boolean {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED
    }

    private fun requestMicrophonePermission() {
        ActivityCompat.requestPermissions(this, arrayOf(Manifest.permission.RECORD_AUDIO), MICROPHONE_PERMISSION_CODE)
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == MICROPHONE_PERMISSION_CODE) {
            val granted = grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED
            methodChannel?.invokeMethod("onPermissionResult", granted)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        speechRecognizer?.destroy()
        speechRecognizer = null
    }
}
