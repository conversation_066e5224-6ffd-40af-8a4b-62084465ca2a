import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/user_subscription_model.dart';
import '../models/subscription_model.dart';
import '../providers/subscription_provider.dart';

class PaymentDetectionService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  
  static StreamSubscription<DocumentSnapshot>? _userSubscription;

  static void startListening(SubscriptionProvider subscriptionProvider) {
    final user = _auth.currentUser;
    if (user == null) return;

    _userSubscription = _firestore
        .collection('users')
        .doc(user.uid)
        .snapshots()
        .listen((snapshot) {
      if (snapshot.exists) {
        _handleUserDocumentChange(snapshot, subscriptionProvider);
      }
    });
  }

  static void stopListening() {
    _userSubscription?.cancel();
    _userSubscription = null;
  }

  static void _handleUserDocumentChange(
    DocumentSnapshot snapshot,
    SubscriptionProvider subscriptionProvider,
  ) {
    try {
      final data = snapshot.data() as Map<String, dynamic>;
      final userId = snapshot.id;

      if (data.containsKey('subscription') && data.containsKey('credits')) {
        final userSub = UserSubscriptionModel.fromMap(data, userId);
        
        if (_isNewSubscriptionActivation(userSub, subscriptionProvider.userSubscription)) {
          _handleSubscriptionActivated(userSub, subscriptionProvider);
        }
        
        subscriptionProvider.updateUserSubscription(userSub);
      }
    } catch (e) {
      print('Error handling user document change: $e');
    }
  }

  static bool _isNewSubscriptionActivation(
    UserSubscriptionModel newSub,
    UserSubscriptionModel? currentSub,
  ) {
    if (currentSub == null) return true;

    if (currentSub.subscription.plan == SubscriptionPlan.free &&
        newSub.subscription.plan != SubscriptionPlan.free) {
      return true;
    }

    if (currentSub.subscription.status != SubscriptionStatus.active &&
        newSub.subscription.status == SubscriptionStatus.active) {
      return true;
    }

    if (currentSub.subscription.plan != newSub.subscription.plan &&
        newSub.subscription.status == SubscriptionStatus.active) {
      return true;
    }

    return false;
  }

  static void _handleSubscriptionActivated(
    UserSubscriptionModel userSub,
    SubscriptionProvider subscriptionProvider,
  ) {
    print('Subscription activated: ${userSub.subscription.plan.name}');
    
  }

  static Future<UserSubscriptionModel?> checkForUpdates(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      
      if (!doc.exists) return null;
      
      final data = doc.data()!;
      return UserSubscriptionModel.fromMap(data, userId);
    } catch (e) {
      print('Error checking for subscription updates: $e');
      return null;
    }
  }

  static Timer? _pollTimer;
  
  static void startPolling(
    SubscriptionProvider subscriptionProvider, {
    Duration interval = const Duration(seconds: 30),
  }) {
    final user = _auth.currentUser;
    if (user == null) return;

    _pollTimer?.cancel();
    _pollTimer = Timer.periodic(interval, (timer) async {
      final updatedSub = await checkForUpdates(user.uid);
      if (updatedSub != null) {
        final currentSub = subscriptionProvider.userSubscription;
        
        if (_isNewSubscriptionActivation(updatedSub, currentSub)) {
          _handleSubscriptionActivated(updatedSub, subscriptionProvider);
        }
        
        subscriptionProvider.updateUserSubscription(updatedSub);
      }
    });
  }

  static void stopPolling() {
    _pollTimer?.cancel();
    _pollTimer = null;
  }

  static Future<List<Map<String, dynamic>>> getPendingPayments(String userId) async {
    try {
      final query = await _firestore
          .collection('payment_transactions')
          .where('userId', isEqualTo: userId)
          .where('status', isEqualTo: 'pending')
          .orderBy('createdAt', descending: true)
          .get();

      return query.docs.map((doc) => {
        'id': doc.id,
        ...doc.data(),
      }).toList();
    } catch (e) {
      print('Error getting pending payments: $e');
      return [];
    }
  }

  static Future<List<Map<String, dynamic>>> getRecentPayments(
    String userId, {
    int limitDays = 7,
  }) async {
    try {
      final cutoffDate = DateTime.now().subtract(Duration(days: limitDays));
      
      final query = await _firestore
          .collection('payment_transactions')
          .where('userId', isEqualTo: userId)
          .where('status', isEqualTo: 'completed')
          .where('createdAt', isGreaterThan: Timestamp.fromDate(cutoffDate))
          .orderBy('createdAt', descending: true)
          .get();

      return query.docs.map((doc) => {
        'id': doc.id,
        ...doc.data(),
      }).toList();
    } catch (e) {
      print('Error getting recent payments: $e');
      return [];
    }
  }

  static Future<bool> verifySubscriptionStatus(String subscriptionId) async {
    try {
      print('Verifying subscription status for: $subscriptionId');
      return true;
    } catch (e) {
      print('Error verifying subscription status: $e');
      return false;
    }
  }

  static Future<void> handlePaymentFailure({
    required String userId,
    required String reason,
  }) async {
    try {
      await _firestore.collection('payment_failures').add({
        'userId': userId,
        'reason': reason,
        'timestamp': Timestamp.fromDate(DateTime.now()),
      });

      
      print('Payment failure logged for user: $userId, reason: $reason');
    } catch (e) {
      print('Error handling payment failure: $e');
    }
  }

  static Future<Map<String, dynamic>> getUserPaymentStats(String userId) async {
    try {
      final query = await _firestore
          .collection('payment_transactions')
          .where('userId', isEqualTo: userId)
          .get();

      final transactions = query.docs.map((doc) => doc.data()).toList();
      
      final totalPayments = transactions.length;
      final successfulPayments = transactions
          .where((t) => t['status'] == 'completed')
          .length;
      final totalAmount = transactions
          .where((t) => t['status'] == 'completed')
          .fold(0.0, (sum, t) => sum + (t['amount'] ?? 0.0));

      return {
        'totalPayments': totalPayments,
        'successfulPayments': successfulPayments,
        'failedPayments': totalPayments - successfulPayments,
        'totalAmount': totalAmount,
        'successRate': totalPayments > 0 ? (successfulPayments / totalPayments) * 100 : 0.0,
      };
    } catch (e) {
      print('Error getting user payment stats: $e');
      return {
        'totalPayments': 0,
        'successfulPayments': 0,
        'failedPayments': 0,
        'totalAmount': 0.0,
        'successRate': 0.0,
      };
    }
  }
}
