import 'package:flutter/material.dart';
import '../models/user_memory_model.dart';
import '../services/user_memory_service.dart';

/// A simple widget that shows users how ALORA is learning about them
/// Can be integrated into existing screens like profile or settings
class MemoryInsightsWidget extends StatefulWidget {
  final String userId;
  final bool showTitle;

  const MemoryInsightsWidget({
    super.key,
    required this.userId,
    this.showTitle = true,
  });

  @override
  State<MemoryInsightsWidget> createState() => _MemoryInsightsWidgetState();
}

class _MemoryInsightsWidgetState extends State<MemoryInsightsWidget> {
  final UserMemoryService _memoryService = UserMemoryService();
  UserMemoryModel? _userMemory;

  @override
  void initState() {
    super.initState();
    _loadMemoryInsights();
  }

  Future<void> _loadMemoryInsights() async {
    try {
      final response = await _memoryService.getUserMemory(widget.userId);
      if (response.success && mounted) {
        setState(() {
          _userMemory = response.data;
        });
      }
    } catch (e) {
      // Silently handle errors - will show empty state
      debugPrint('Memory insights error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Don't show loading state, just show empty state if no data
    if (_userMemory == null) {
      return _buildEmptyState();
    }

    final insights = _generateInsights(_userMemory!);

    if (insights.isEmpty) {
      return _buildEmptyState();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.showTitle) ...[
              Row(
                children: [
                  Icon(
                    Icons.psychology,
                    color: Theme.of(context).primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'How ALORA Knows You',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
            ],
            ...insights.map((insight) => _buildInsightItem(insight)),
            const SizedBox(height: 8),
            Text(
              'ALORA learns from your conversations to provide better support.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.psychology_outlined,
              size: 24,
              color: Colors.grey[400],
            ),
            
          ],
        ),
      ),
    );
  }

  Widget _buildInsightItem(MemoryInsight insight) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            insight.icon,
            size: 16,
            color: insight.color,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              insight.text,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  List<MemoryInsight> _generateInsights(UserMemoryModel memory) {
    final insights = <MemoryInsight>[];

    // Personal insights
    if (memory.personalInfo.preferredName != null) {
      insights.add(MemoryInsight(
        icon: Icons.person,
        color: Colors.blue,
        text: 'I know you prefer to be called ${memory.personalInfo.preferredName}',
      ));
    }

    if (memory.personalInfo.phobias.isNotEmpty) {
      final count = memory.personalInfo.phobias.length;
      insights.add(MemoryInsight(
        icon: Icons.warning_amber,
        color: Colors.orange,
        text: 'I understand your $count main fear${count > 1 ? 's' : ''} and triggers',
      ));
    }

    if (memory.personalInfo.copingMechanisms.isNotEmpty) {
      final count = memory.personalInfo.copingMechanisms.length;
      insights.add(MemoryInsight(
        icon: Icons.self_improvement,
        color: Colors.green,
        text: 'I know $count coping strateg${count > 1 ? 'ies' : 'y'} that work for you',
      ));
    }

    // Aviation insights
    if (memory.aviationData.flightFears.isNotEmpty) {
      final topFear = memory.aviationData.flightFears.entries
          .reduce((a, b) => a.value > b.value ? a : b);
      insights.add(MemoryInsight(
        icon: Icons.flight,
        color: Colors.red,
        text: 'I know ${topFear.key} is your biggest flight concern',
      ));
    }

    if (memory.aviationData.upcomingFlights.isNotEmpty) {
      final flight = memory.aviationData.upcomingFlights.first;
      insights.add(MemoryInsight(
        icon: Icons.flight_takeoff,
        color: Colors.purple,
        text: 'I\'m aware of your upcoming flight to ${flight.arrivalAirport}',
      ));
    }

    if (memory.aviationData.successfulCopingStrategies.isNotEmpty) {
      insights.add(MemoryInsight(
        icon: Icons.check_circle,
        color: Colors.green,
        text: 'I remember what strategies helped you on previous flights',
      ));
    }

    // Progress insights
    if (memory.therapyProgress.goals.isNotEmpty) {
      final activeGoals = memory.therapyProgress.goals
          .where((goal) => !goal.isCompleted)
          .length;
      if (activeGoals > 0) {
        insights.add(MemoryInsight(
          icon: Icons.flag,
          color: Colors.indigo,
          text: 'I\'m tracking your progress on $activeGoals therapy goal${activeGoals > 1 ? 's' : ''}',
        ));
      }
    }

    if (memory.therapyProgress.milestones.isNotEmpty) {
      insights.add(MemoryInsight(
        icon: Icons.star,
        color: Colors.amber,
        text: 'I celebrate your ${memory.therapyProgress.milestones.length} achievement${memory.therapyProgress.milestones.length > 1 ? 's' : ''}',
      ));
    }

    // Behavioral insights
    if (memory.behavioralPatterns.preferredCopingMethods.isNotEmpty) {
      insights.add(MemoryInsight(
        icon: Icons.psychology,
        color: Colors.teal,
        text: 'I understand your preferred ways of managing anxiety',
      ));
    }

    return insights.take(5).toList(); // Limit to 5 insights to avoid overwhelming
  }
}

class MemoryInsight {
  final IconData icon;
  final Color color;
  final String text;

  MemoryInsight({
    required this.icon,
    required this.color,
    required this.text,
  });
}
