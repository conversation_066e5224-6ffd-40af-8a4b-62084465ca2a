import 'package:cloud_firestore/cloud_firestore.dart';
import 'memory_insights_model.dart';

/// Model for export history and progress tracking
class ExportSummary {
  final String id;
  final String userId;
  final DateTime exportDate;
  final String exportType; // 'pdf' or 'json'
  final MemoryInsights insights;
  final ProgressComparison? progressComparison;
  final String status; // 'generating', 'ready', 'downloaded'
  final String? filePath;

  const ExportSummary({
    required this.id,
    required this.userId,
    required this.exportDate,
    required this.exportType,
    required this.insights,
    this.progressComparison,
    required this.status,
    this.filePath,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'exportDate': Timestamp.fromDate(exportDate),
      'exportType': exportType,
      'insights': insights.toMap(),
      'progressComparison': progressComparison?.toMap(),
      'status': status,
      'filePath': filePath,
    };
  }

  factory ExportSummary.fromMap(Map<String, dynamic> map) {
    return ExportSummary(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      exportDate: (map['exportDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      exportType: map['exportType'] ?? 'pdf',
      insights: MemoryInsights.fromMap(map['insights'] ?? {}),
      progressComparison: map['progressComparison'] != null 
          ? ProgressComparison.fromMap(map['progressComparison'])
          : null,
      status: map['status'] ?? 'generating',
      filePath: map['filePath'],
    );
  }

  ExportSummary copyWith({
    String? id,
    String? userId,
    DateTime? exportDate,
    String? exportType,
    MemoryInsights? insights,
    ProgressComparison? progressComparison,
    String? status,
    String? filePath,
  }) {
    return ExportSummary(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      exportDate: exportDate ?? this.exportDate,
      exportType: exportType ?? this.exportType,
      insights: insights ?? this.insights,
      progressComparison: progressComparison ?? this.progressComparison,
      status: status ?? this.status,
      filePath: filePath ?? this.filePath,
    );
  }
}

/// Model for comparing progress between exports
class ProgressComparison {
  final double anxietyLevelChange;
  final int sessionCountChange;
  final double copingSuccessRateChange;
  final int goalsCompletedChange;
  final List<String> newStrengths;
  final List<String> improvedAreas;
  final List<String> concernAreas;
  final String overallTrend; // 'improving', 'stable', 'declining'
  final String summaryMessage;

  const ProgressComparison({
    required this.anxietyLevelChange,
    required this.sessionCountChange,
    required this.copingSuccessRateChange,
    required this.goalsCompletedChange,
    required this.newStrengths,
    required this.improvedAreas,
    required this.concernAreas,
    required this.overallTrend,
    required this.summaryMessage,
  });

  Map<String, dynamic> toMap() {
    return {
      'anxietyLevelChange': anxietyLevelChange,
      'sessionCountChange': sessionCountChange,
      'copingSuccessRateChange': copingSuccessRateChange,
      'goalsCompletedChange': goalsCompletedChange,
      'newStrengths': newStrengths,
      'improvedAreas': improvedAreas,
      'concernAreas': concernAreas,
      'overallTrend': overallTrend,
      'summaryMessage': summaryMessage,
    };
  }

  factory ProgressComparison.fromMap(Map<String, dynamic> map) {
    return ProgressComparison(
      anxietyLevelChange: (map['anxietyLevelChange'] ?? 0.0).toDouble(),
      sessionCountChange: map['sessionCountChange'] ?? 0,
      copingSuccessRateChange: (map['copingSuccessRateChange'] ?? 0.0).toDouble(),
      goalsCompletedChange: map['goalsCompletedChange'] ?? 0,
      newStrengths: List<String>.from(map['newStrengths'] ?? []),
      improvedAreas: List<String>.from(map['improvedAreas'] ?? []),
      concernAreas: List<String>.from(map['concernAreas'] ?? []),
      overallTrend: map['overallTrend'] ?? 'stable',
      summaryMessage: map['summaryMessage'] ?? '',
    );
  }
}
