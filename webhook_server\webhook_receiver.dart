import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:http/http.dart' as http;

/// Production-ready webhook receiver for LemonSqueezy, Stripe, and PayPal
/// Deploy this as a standalone Dart server on Heroku, Railway, or similar platform
class WebhookReceiver {
  static const int PORT = 8080;
  
  // Environment variables - set these in your deployment platform
  static String get lemonSqueezySecret => Platform.environment['LEMONSQUEEZY_WEBHOOK_SECRET'] ?? '';
  static String get stripeSecret => Platform.environment['STRIPE_WEBHOOK_SECRET'] ?? '';
  static String get paypalWebhookId => Platform.environment['PAYPAL_WEBHOOK_ID'] ?? '';
  static String get firebaseUrl => Platform.environment['FIREBASE_DATABASE_URL'] ?? '';
  
  late HttpServer _server;

  Future<void> start() async {
    try {
      _server = await HttpServer.bind(InternetAddress.anyIPv4, PORT);
      print('🚀 Webhook server started on port $PORT');
      print('📡 Firebase URL: $firebaseUrl');
      
      await for (HttpRequest request in _server) {
        _handleRequest(request);
      }
    } catch (error) {
      print('❌ Error starting webhook server: $error');
      rethrow;
    }
  }

  Future<void> _handleRequest(HttpRequest request) async {
    // Enable CORS for all requests
    request.response.headers.set('Access-Control-Allow-Origin', '*');
    request.response.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    request.response.headers.set('Access-Control-Allow-Headers', 'Content-Type, X-Signature, Stripe-Signature');

    try {
      if (request.method == 'OPTIONS') {
        request.response.statusCode = 200;
        await request.response.close();
        return;
      }

      if (request.method == 'GET') {
        await _handleHealthCheck(request);
        return;
      }

      if (request.method == 'POST') {
        await _handleWebhook(request);
        return;
      }

      request.response.statusCode = 404;
      request.response.write('Not Found');
      await request.response.close();
      
    } catch (error) {
      print('❌ Error handling request: $error');
      request.response.statusCode = 500;
      request.response.write('Internal Server Error');
      await request.response.close();
    }
  }

  Future<void> _handleHealthCheck(HttpRequest request) async {
    final response = {
      'status': 'healthy',
      'timestamp': DateTime.now().toIso8601String(),
      'service': 'Multi-Provider Webhook Receiver',
      'version': '1.0.0',
      'endpoints': [
        '/lemonsqueezy-webhook',
        '/stripe-webhook', 
        '/paypal-webhook'
      ],
    };

    request.response.headers.contentType = ContentType.json;
    request.response.write(json.encode(response));
    await request.response.close();
  }

  Future<void> _handleWebhook(HttpRequest request) async {
    final path = request.uri.path;
    
    switch (path) {
      case '/lemonsqueezy-webhook':
        await _handleLemonSqueezyWebhook(request);
        break;
      case '/stripe-webhook':
        await _handleStripeWebhook(request);
        break;
      case '/paypal-webhook':
        await _handlePayPalWebhook(request);
        break;
      default:
        request.response.statusCode = 404;
        request.response.write('Webhook endpoint not found');
        await request.response.close();
    }
  }

  Future<void> _handleLemonSqueezyWebhook(HttpRequest request) async {
    try {
      final bodyBytes = await request.fold<List<int>>(<int>[], (previous, element) => previous..addAll(element));
      final bodyString = utf8.decode(bodyBytes);
      
      if (bodyString.isEmpty) {
        request.response.statusCode = 400;
        request.response.write('Empty request body');
        await request.response.close();
        return;
      }

      final webhookData = json.decode(bodyString);
      final signature = request.headers.value('X-Signature');
      
      if (!_verifyLemonSqueezySignature(bodyString, signature)) {
        print('❌ Invalid LemonSqueezy signature');
        request.response.statusCode = 401;
        request.response.write('Invalid signature');
        await request.response.close();
        return;
      }

      final eventType = webhookData['meta']?['event_name'] as String?;
      if (eventType == null) {
        request.response.statusCode = 400;
        request.response.write('Missing event_name');
        await request.response.close();
        return;
      }

      await _sendToFirebase('lemonsqueezy', eventType, webhookData, signature);
      
      print('✅ LemonSqueezy webhook processed: $eventType');
      request.response.statusCode = 200;
      request.response.headers.contentType = ContentType.json;
      request.response.write(json.encode({
        'status': 'success',
        'provider': 'lemonsqueezy',
        'event': eventType,
        'timestamp': DateTime.now().toIso8601String(),
      }));
      await request.response.close();

    } catch (error) {
      print('❌ LemonSqueezy webhook error: $error');
      request.response.statusCode = 500;
      request.response.write('Webhook processing failed');
      await request.response.close();
    }
  }

  Future<void> _handleStripeWebhook(HttpRequest request) async {
    try {
      final bodyBytes = await request.fold<List<int>>(<int>[], (previous, element) => previous..addAll(element));
      final bodyString = utf8.decode(bodyBytes);
      
      final webhookData = json.decode(bodyString);
      final signature = request.headers.value('Stripe-Signature');
      
      if (!_verifyStripeSignature(bodyString, signature)) {
        print('❌ Invalid Stripe signature');
        request.response.statusCode = 401;
        request.response.write('Invalid signature');
        await request.response.close();
        return;
      }

      final eventType = webhookData['type'] as String?;
      if (eventType == null) {
        request.response.statusCode = 400;
        request.response.write('Missing event type');
        await request.response.close();
        return;
      }

      await _sendToFirebase('stripe', eventType, webhookData, signature);
      
      print('✅ Stripe webhook processed: $eventType');
      request.response.statusCode = 200;
      request.response.headers.contentType = ContentType.json;
      request.response.write(json.encode({
        'status': 'success',
        'provider': 'stripe',
        'event': eventType,
        'timestamp': DateTime.now().toIso8601String(),
      }));
      await request.response.close();

    } catch (error) {
      print('❌ Stripe webhook error: $error');
      request.response.statusCode = 500;
      request.response.write('Webhook processing failed');
      await request.response.close();
    }
  }

  Future<void> _handlePayPalWebhook(HttpRequest request) async {
    try {
      final bodyBytes = await request.fold<List<int>>(<int>[], (previous, element) => previous..addAll(element));
      final bodyString = utf8.decode(bodyBytes);
      
      final webhookData = json.decode(bodyString);
      final eventType = webhookData['event_type'] as String?;
      
      if (eventType == null) {
        request.response.statusCode = 400;
        request.response.write('Missing event_type');
        await request.response.close();
        return;
      }

      // PayPal webhook verification is more complex, simplified for now
      await _sendToFirebase('paypal', eventType, webhookData, 'paypal-webhook');
      
      print('✅ PayPal webhook processed: $eventType');
      request.response.statusCode = 200;
      request.response.headers.contentType = ContentType.json;
      request.response.write(json.encode({
        'status': 'success',
        'provider': 'paypal',
        'event': eventType,
        'timestamp': DateTime.now().toIso8601String(),
      }));
      await request.response.close();

    } catch (error) {
      print('❌ PayPal webhook error: $error');
      request.response.statusCode = 500;
      request.response.write('Webhook processing failed');
      await request.response.close();
    }
  }

  bool _verifyLemonSqueezySignature(String payload, String? signature) {
    if (signature == null || signature.isEmpty || lemonSqueezySecret.isEmpty) {
      return false;
    }

    try {
      final key = utf8.encode(lemonSqueezySecret);
      final bytes = utf8.encode(payload);
      final hmac = Hmac(sha256, key);
      final digest = hmac.convert(bytes);
      final expectedSignature = 'sha256=${digest.toString()}';

      return signature == expectedSignature;
    } catch (error) {
      print('❌ LemonSqueezy signature verification error: $error');
      return false;
    }
  }

  bool _verifyStripeSignature(String payload, String? signature) {
    if (signature == null || signature.isEmpty || stripeSecret.isEmpty) {
      return false;
    }

    try {
      // Simplified Stripe signature verification
      // In production, you'd parse the timestamp and verify it's recent
      final elements = signature.split(',');
      String? v1Signature;
      
      for (final element in elements) {
        if (element.startsWith('v1=')) {
          v1Signature = element.substring(3);
          break;
        }
      }

      if (v1Signature == null) return false;

      final key = utf8.encode(stripeSecret);
      final bytes = utf8.encode(payload);
      final hmac = Hmac(sha256, key);
      final digest = hmac.convert(bytes);
      
      return digest.toString() == v1Signature;
    } catch (error) {
      print('❌ Stripe signature verification error: $error');
      return false;
    }
  }

  Future<void> _sendToFirebase(String provider, String eventType, Map<String, dynamic> data, String? signature) async {
    if (firebaseUrl.isEmpty) {
      print('⚠️ Firebase URL not configured, skipping Firebase write');
      return;
    }

    try {
      final eventData = {
        'eventType': eventType,
        'data': data,
        'timestamp': {'.sv': 'timestamp'},
        'processed': false,
        'signature': signature,
        'receivedAt': DateTime.now().toIso8601String(),
      };

      final url = '$firebaseUrl/webhooks/$provider.json';
      final response = await http.post(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(eventData),
      );

      if (response.statusCode == 200) {
        print('✅ Sent $provider webhook to Firebase: $eventType');
      } else {
        print('❌ Failed to send to Firebase: ${response.statusCode} ${response.body}');
      }
    } catch (error) {
      print('❌ Firebase write error: $error');
    }
  }

  Future<void> stop() async {
    await _server.close();
    print('🛑 Webhook server stopped');
  }
}

void main() async {
  final receiver = WebhookReceiver();
  
  // Handle graceful shutdown
  ProcessSignal.sigint.watch().listen((signal) async {
    print('🛑 Received SIGINT, shutting down...');
    await receiver.stop();
    exit(0);
  });

  try {
    await receiver.start();
  } catch (error) {
    print('💥 Fatal error: $error');
    exit(1);
  }
}
