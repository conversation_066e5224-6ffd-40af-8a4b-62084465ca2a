import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/user_memory_model.dart';
import '../models/api_response.dart';

/// Service for managing user memory and context storage
class UserMemoryService {
  static const String _userMemoryCollection = 'user_memory';
  static const String _memoryAnalysisCollection = 'memory_analysis';
  
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Map<String, UserMemoryModel> _memoryCache = {};
  final Map<String, StreamSubscription> _memorySubscriptions = {};

  /// Get user memory with caching
  Future<ApiResponse<UserMemoryModel>> getUserMemory(String userId) async {
    try {
      // Return cached version if available
      if (_memoryCache.containsKey(userId)) {
        return ApiResponse.success(_memoryCache[userId]!);
      }

      final doc = await _firestore
          .collection(_userMemoryCollection)
          .doc(userId)
          .get();

      UserMemoryModel memory;
      if (doc.exists && doc.data() != null) {
        memory = UserMemoryModel.fromMap(doc.data()!, userId);
      } else {
        memory = UserMemoryModel.empty(userId);
        await _createUserMemory(memory);
      }

      _memoryCache[userId] = memory;
      return ApiResponse.success(memory);
    } catch (e) {
      debugPrint('Error getting user memory: $e');
      return ApiResponse.error('Failed to get user memory: ${e.toString()}');
    }
  }

  /// Create new user memory document
  Future<ApiResponse<void>> _createUserMemory(UserMemoryModel memory) async {
    try {
      await _firestore
          .collection(_userMemoryCollection)
          .doc(memory.userId)
          .set(memory.toMap());
      
      return ApiResponse.success(null);
    } catch (e) {
      debugPrint('Error creating user memory: $e');
      return ApiResponse.error('Failed to create user memory: ${e.toString()}');
    }
  }

  /// Update user memory
  Future<ApiResponse<void>> updateUserMemory(UserMemoryModel memory) async {
    try {
      final updatedMemory = memory.copyWith(lastUpdated: DateTime.now());
      
      await _firestore
          .collection(_userMemoryCollection)
          .doc(memory.userId)
          .set(updatedMemory.toMap(), SetOptions(merge: true));

      _memoryCache[memory.userId] = updatedMemory;
      return ApiResponse.success(null);
    } catch (e) {
      debugPrint('Error updating user memory: $e');
      return ApiResponse.error('Failed to update user memory: ${e.toString()}');
    }
  }

  /// Update specific personal information
  Future<ApiResponse<void>> updatePersonalInfo(
    String userId,
    PersonalInformation personalInfo,
  ) async {
    try {
      final memoryResponse = await getUserMemory(userId);
      if (!memoryResponse.success) {
        return ApiResponse.error(memoryResponse.error!);
      }

      final updatedMemory = memoryResponse.data!.copyWith(
        personalInfo: personalInfo,
      );

      return await updateUserMemory(updatedMemory);
    } catch (e) {
      debugPrint('Error updating personal info: $e');
      return ApiResponse.error('Failed to update personal info: ${e.toString()}');
    }
  }

  /// Update aviation data
  Future<ApiResponse<void>> updateAviationData(
    String userId,
    AviationData aviationData,
  ) async {
    try {
      final memoryResponse = await getUserMemory(userId);
      if (!memoryResponse.success) {
        return ApiResponse.error(memoryResponse.error!);
      }

      final updatedMemory = memoryResponse.data!.copyWith(
        aviationData: aviationData,
      );

      return await updateUserMemory(updatedMemory);
    } catch (e) {
      debugPrint('Error updating aviation data: $e');
      return ApiResponse.error('Failed to update aviation data: ${e.toString()}');
    }
  }

  /// Add flight experience
  Future<ApiResponse<void>> addFlightExperience(
    String userId,
    FlightExperience experience,
  ) async {
    try {
      final memoryResponse = await getUserMemory(userId);
      if (!memoryResponse.success) {
        return ApiResponse.error(memoryResponse.error!);
      }

      final currentAviationData = memoryResponse.data!.aviationData;
      final updatedFlightHistory = [...currentAviationData.flightHistory, experience];
      
      final updatedAviationData = currentAviationData.copyWith(
        flightHistory: updatedFlightHistory,
      );

      final updatedMemory = memoryResponse.data!.copyWith(
        aviationData: updatedAviationData,
      );

      return await updateUserMemory(updatedMemory);
    } catch (e) {
      debugPrint('Error adding flight experience: $e');
      return ApiResponse.error('Failed to add flight experience: ${e.toString()}');
    }
  }

  /// Add upcoming flight
  Future<ApiResponse<void>> addUpcomingFlight(
    String userId,
    UpcomingFlight flight,
  ) async {
    try {
      final memoryResponse = await getUserMemory(userId);
      if (!memoryResponse.success) {
        return ApiResponse.error(memoryResponse.error!);
      }

      final currentAviationData = memoryResponse.data!.aviationData;
      final updatedUpcomingFlights = [...currentAviationData.upcomingFlights, flight];
      
      final updatedAviationData = currentAviationData.copyWith(
        upcomingFlights: updatedUpcomingFlights,
      );

      final updatedMemory = memoryResponse.data!.copyWith(
        aviationData: updatedAviationData,
      );

      return await updateUserMemory(updatedMemory);
    } catch (e) {
      debugPrint('Error adding upcoming flight: $e');
      return ApiResponse.error('Failed to add upcoming flight: ${e.toString()}');
    }
  }

  /// Update behavioral patterns
  Future<ApiResponse<void>> updateBehavioralPatterns(
    String userId,
    BehavioralPatterns patterns,
  ) async {
    try {
      final memoryResponse = await getUserMemory(userId);
      if (!memoryResponse.success) {
        return ApiResponse.error(memoryResponse.error!);
      }

      final updatedMemory = memoryResponse.data!.copyWith(
        behavioralPatterns: patterns,
      );

      return await updateUserMemory(updatedMemory);
    } catch (e) {
      debugPrint('Error updating behavioral patterns: $e');
      return ApiResponse.error('Failed to update behavioral patterns: ${e.toString()}');
    }
  }

  /// Update therapy progress
  Future<ApiResponse<void>> updateTherapyProgress(
    String userId,
    TherapyProgress progress,
  ) async {
    try {
      final memoryResponse = await getUserMemory(userId);
      if (!memoryResponse.success) {
        return ApiResponse.error(memoryResponse.error!);
      }

      final updatedMemory = memoryResponse.data!.copyWith(
        therapyProgress: progress,
      );

      return await updateUserMemory(updatedMemory);
    } catch (e) {
      debugPrint('Error updating therapy progress: $e');
      return ApiResponse.error('Failed to update therapy progress: ${e.toString()}');
    }
  }

  /// Add therapy goal
  Future<ApiResponse<void>> addTherapyGoal(
    String userId,
    TherapyGoal goal,
  ) async {
    try {
      final memoryResponse = await getUserMemory(userId);
      if (!memoryResponse.success) {
        return ApiResponse.error(memoryResponse.error!);
      }

      final currentProgress = memoryResponse.data!.therapyProgress;
      final updatedGoals = [...currentProgress.goals, goal];
      
      final updatedProgress = currentProgress.copyWith(goals: updatedGoals);
      final updatedMemory = memoryResponse.data!.copyWith(therapyProgress: updatedProgress);

      return await updateUserMemory(updatedMemory);
    } catch (e) {
      debugPrint('Error adding therapy goal: $e');
      return ApiResponse.error('Failed to add therapy goal: ${e.toString()}');
    }
  }

  /// Add therapy milestone
  Future<ApiResponse<void>> addTherapyMilestone(
    String userId,
    TherapyMilestone milestone,
  ) async {
    try {
      final memoryResponse = await getUserMemory(userId);
      if (!memoryResponse.success) {
        return ApiResponse.error(memoryResponse.error!);
      }

      final currentProgress = memoryResponse.data!.therapyProgress;
      final updatedMilestones = [...currentProgress.milestones, milestone];
      
      final updatedProgress = currentProgress.copyWith(milestones: updatedMilestones);
      final updatedMemory = memoryResponse.data!.copyWith(therapyProgress: updatedProgress);

      return await updateUserMemory(updatedMemory);
    } catch (e) {
      debugPrint('Error adding therapy milestone: $e');
      return ApiResponse.error('Failed to add therapy milestone: ${e.toString()}');
    }
  }

  /// Get memory stream for real-time updates
  Stream<UserMemoryModel> getUserMemoryStream(String userId) {
    // Cancel existing subscription if any
    _memorySubscriptions[userId]?.cancel();

    return _firestore
        .collection(_userMemoryCollection)
        .doc(userId)
        .snapshots()
        .map((doc) {
      if (doc.exists && doc.data() != null) {
        final memory = UserMemoryModel.fromMap(doc.data()!, userId);
        _memoryCache[userId] = memory;
        return memory;
      } else {
        final emptyMemory = UserMemoryModel.empty(userId);
        _createUserMemory(emptyMemory);
        return emptyMemory;
      }
    });
  }

  /// Clear user memory cache
  void clearCache(String userId) {
    _memoryCache.remove(userId);
    _memorySubscriptions[userId]?.cancel();
    _memorySubscriptions.remove(userId);
  }

  /// Clear all caches
  void clearAllCaches() {
    _memoryCache.clear();
    for (final subscription in _memorySubscriptions.values) {
      subscription.cancel();
    }
    _memorySubscriptions.clear();
  }

  /// Delete user memory (GDPR compliance)
  Future<ApiResponse<void>> deleteUserMemory(String userId) async {
    try {
      await _firestore
          .collection(_userMemoryCollection)
          .doc(userId)
          .delete();

      clearCache(userId);
      return ApiResponse.success(null);
    } catch (e) {
      debugPrint('Error deleting user memory: $e');
      return ApiResponse.error('Failed to delete user memory: ${e.toString()}');
    }
  }

  /// Export user memory data (GDPR compliance)
  Future<ApiResponse<Map<String, dynamic>>> exportUserMemory(String userId) async {
    try {
      final memoryResponse = await getUserMemory(userId);
      if (!memoryResponse.success) {
        return ApiResponse.error(memoryResponse.error!);
      }

      return ApiResponse.success(memoryResponse.data!.toMap());
    } catch (e) {
      debugPrint('Error exporting user memory: $e');
      return ApiResponse.error('Failed to export user memory: ${e.toString()}');
    }
  }
}
