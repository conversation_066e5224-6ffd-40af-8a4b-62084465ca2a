import 'package:flutter/material.dart';
import '../utils/theme.dart';

class EnhancedEmailField extends StatefulWidget {
  final String? label;
  final String? hint;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final IconData? prefixIcon;
  final TextInputAction textInputAction;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;

  const EnhancedEmailField({
    super.key,
    this.label,
    this.hint,
    this.controller,
    this.validator,
    this.prefixIcon,
    this.textInputAction = TextInputAction.next,
    this.onChanged,
    this.onSubmitted,
  });

  @override
  State<EnhancedEmailField> createState() => _EnhancedEmailFieldState();
}

class _EnhancedEmailFieldState extends State<EnhancedEmailField> {
  bool _hasFocus = false;
  String _currentEmail = '';
  bool _isValidFormat = false;

  @override
  void initState() {
    super.initState();
    _currentEmail = widget.controller?.text ?? '';
    _validateEmailFormat(_currentEmail);
  }

  void _validateEmailFormat(String email) {
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    setState(() {
      _isValidFormat = emailRegex.hasMatch(email) && email.isNotEmpty;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
        ],
        Focus(
          onFocusChange: (hasFocus) {
            setState(() {
              _hasFocus = hasFocus;
            });
          },
          child: TextFormField(
            controller: widget.controller,
            validator: widget.validator,
            keyboardType: TextInputType.emailAddress,
            textInputAction: widget.textInputAction,
            autocorrect: false,
            onChanged: (value) {
              setState(() {
                _currentEmail = value;
              });
              _validateEmailFormat(value);
              widget.onChanged?.call(value);
            },
            onFieldSubmitted: widget.onSubmitted,
            style: Theme.of(context).textTheme.bodyLarge,
            decoration: InputDecoration(
              hintText: widget.hint,
              prefixIcon: widget.prefixIcon != null
                  ? Icon(
                      widget.prefixIcon,
                      color: _hasFocus ? AppTheme.primaryColor : AppTheme.textSecondary,
                    )
                  : null,
              suffixIcon: _currentEmail.isNotEmpty
                  ? Icon(
                      _isValidFormat ? Icons.check_circle : Icons.error,
                      color: _isValidFormat ? Colors.green : Colors.red,
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.errorColor, width: 2),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.errorColor, width: 2),
              ),
              filled: true,
              fillColor: _hasFocus ? Colors.blue.shade50 : Colors.grey.shade50,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
          ),
        ),
        if (_currentEmail.isNotEmpty && (_hasFocus || !_isValidFormat))
          Container(
            margin: const EdgeInsets.only(top: 8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: _isValidFormat ? Colors.green.shade50 : Colors.orange.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _isValidFormat ? Colors.green.shade200 : Colors.orange.shade200,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  _isValidFormat ? Icons.check_circle_outline : Icons.info_outline,
                  size: 16,
                  color: _isValidFormat ? Colors.green.shade700 : Colors.orange.shade700,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _isValidFormat 
                        ? 'Valid email format'
                        : 'Please enter a valid email address',
                    style: TextStyle(
                      fontSize: 12,
                      color: _isValidFormat ? Colors.green.shade700 : Colors.orange.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
