import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/privacy_service.dart';
import '../services/user_memory_service.dart';

class PrivacySettingsScreen extends StatefulWidget {
  const PrivacySettingsScreen({super.key});

  @override
  State<PrivacySettingsScreen> createState() => _PrivacySettingsScreenState();
}

class _PrivacySettingsScreenState extends State<PrivacySettingsScreen> {
  final PrivacyService _privacyService = PrivacyService();
  final UserMemoryService _memoryService = UserMemoryService();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Privacy & Data'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader('Your Data'),
            _buildInfoCard(
              'ALORA learns from your conversations to provide personalized support. '
              'This helps us understand your specific fears, triggers, and what works best for you.',
            ),
            const SizedBox(height: 24),
            
            _buildSectionHeader('Data Controls'),
            _buildActionCard(
              icon: Icons.download,
              title: 'Export My Data',
              subtitle: 'Download your wellness report as PDF or JSON',
              onTap: _showExportOptions,
            ),
            _buildActionCard(
              icon: Icons.delete_forever,
              title: 'Delete My Data',
              subtitle: 'Permanently remove your personal data',
              onTap: _showDeleteConfirmation,
              isDestructive: true,
            ),
            
            const SizedBox(height: 24),
            _buildSectionHeader('How We Use Your Data'),
            _buildDataUsageInfo(),
            
            const SizedBox(height: 24),
            _buildSectionHeader('Your Rights'),
            _buildRightsInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
          color: Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  Widget _buildInfoCard(String text) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: Theme.of(context).primaryColor,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                text,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Card(
      child: ListTile(
        leading: Icon(
          icon,
          color: isDestructive ? Colors.red : Theme.of(context).primaryColor,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isDestructive ? Colors.red : null,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.chevron_right),
        onTap: _isLoading ? null : onTap,
      ),
    );
  }

  Widget _buildDataUsageInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDataUsageItem(
              'Conversation Context',
              'We remember your fears, triggers, and what helps you cope to provide personalized support.',
            ),
            const Divider(),
            _buildDataUsageItem(
              'Progress Tracking',
              'We track your therapy goals and milestones to celebrate your progress.',
            ),
            const Divider(),
            _buildDataUsageItem(
              'Flight Information',
              'We remember your flight experiences and trips to provide relevant support.',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataUsageItem(String title, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
    );
  }

  Widget _buildRightsInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'You have the right to:',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildRightItem('Access your personal data'),
            _buildRightItem('See information'),
            _buildRightItem('Delete your personal data'),
            _buildRightItem('Export your data in a readable format'),
          ],
        ),
      ),
    );
  }

  Widget _buildRightItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            Icons.check_circle_outline,
            size: 16,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 8),
          Expanded(child: Text(text)),
        ],
      ),
    );
  }

  void _showExportOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Choose Export Format',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 20),

            ListTile(
              leading: const Icon(Icons.picture_as_pdf, color: Colors.red),
              title: const Text('Wellness Report (PDF)'),
              subtitle: const Text('Professional report with insights and progress tracking'),
              onTap: () {
                Navigator.pop(context);
                _exportData('pdf');
              },
            ),

            ListTile(
              leading: const Icon(Icons.code, color: Colors.blue),
              title: const Text('Raw Data (JSON)'),
              subtitle: const Text('Technical format for developers or data analysis'),
              onTap: () {
                Navigator.pop(context);
                _exportData('json');
              },
            ),

            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  Future<void> _exportData(String format) async {
    setState(() => _isLoading = true);

    try {
      final user = FirebaseAuth.instance.currentUser;
      final userId = user?.uid;

      if (userId == null) {
        _showErrorDialog('User not authenticated');
        return;
      }

      final formatName = format == 'pdf' ? 'wellness report' : 'data export';

      // Show progress dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Text('Generating your $formatName...'),
            ],
          ),
        ),
      );

      final result = await _privacyService.exportUserMemoryData(userId, format: format);

      // Close progress dialog
      if (mounted) Navigator.of(context).pop();

      if (result.success) {
        // Show share dialog
        final shareResult = await _privacyService.shareExportedData(result.data!);

        if (shareResult.success) {
          _showSuccessDialog(
            '${format.toUpperCase()} Export Successful',
            format == 'pdf'
              ? 'Your wellness report has been generated with intelligent insights and progress tracking. '
                'The PDF includes your personal summary, flight anxiety data, and behavioral patterns.'
              : 'Your personal data has been exported in JSON format. '
                'This technical format contains all your raw data for analysis.',
          );
        } else {
          // Fallback: show file location if sharing fails
          _showSuccessDialog(
            '${format.toUpperCase()} Export Complete',
            'Your $formatName has been generated successfully.\n\nFile saved to: ${result.data!}\n\n'
            'You can find this file in your device\'s file manager.',
          );
        }
      } else {
        _showErrorDialog(result.error!);
      }
    } catch (e) {
      // Close progress dialog if still open
      if (mounted) Navigator.of(context).pop();
      _showErrorDialog('Failed to export data: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _showDeleteConfirmation() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete All Data'),
        content: const Text(
          'This will permanently delete all your personal data, including:\n\n'
          '• Conversation history\n'
          '• Personal preferences\n'
          '• Therapy progress\n'
          '• Flight information\n\n'
          'This action cannot be undone. Are you sure?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _deleteData();
    }
  }

  Future<void> _deleteData() async {
    setState(() => _isLoading = true);

    try {
      final user = FirebaseAuth.instance.currentUser;
      final userId = user?.uid;

      if (userId == null) {
        _showErrorDialog('User not authenticated');
        return;
      }

      final result = await _privacyService.deleteUserMemoryData(userId);
      
      if (result.success) {
        _showSuccessDialog(
          'Data Deleted',
          'All your personal data has been permanently deleted.',
        );
      } else {
        _showErrorDialog(result.error!);
      }
    } catch (e) {
      _showErrorDialog('Failed to delete data: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSuccessDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
