import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../services/subscription_service.dart';
import '../../services/secure_api_service.dart';

class ClientSetupWizard extends StatefulWidget {
  const ClientSetupWizard({super.key});

  @override
  State<ClientSetupWizard> createState() => _ClientSetupWizardState();
}

class _ClientSetupWizardState extends State<ClientSetupWizard> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  
  final _lemonsqueezyStoreIdController = TextEditingController();
  final _lemonsqueezyApiKeyController = TextEditingController();
  final _openaiApiKeyController = TextEditingController();
  final _elevenLabsApiKeyController = TextEditingController();
  
  bool _isLoading = false;
  Map<String, bool> _testResults = {};

  @override
  void dispose() {
    _pageController.dispose();
    _lemonsqueezyStoreIdController.dispose();
    _lemonsqueezyApiKeyController.dispose();
    _openaiApiKeyController.dispose();
    _elevenLabsApiKeyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Setup Wizard'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                for (int i = 0; i < 4; i++) ...[
                  Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: i <= _currentPage ? Colors.indigo : Colors.grey.shade300,
                    ),
                    child: Center(
                      child: Text(
                        '${i + 1}',
                        style: TextStyle(
                          color: i <= _currentPage ? Colors.white : Colors.grey.shade600,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  if (i < 3)
                    Expanded(
                      child: Container(
                        height: 2,
                        color: i < _currentPage ? Colors.indigo : Colors.grey.shade300,
                      ),
                    ),
                ],
              ],
            ),
          ),
          
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (page) => setState(() => _currentPage = page),
              children: [
                _buildWelcomePage(),
                _buildLemonSqueezySetupPage(),
                _buildAPIKeysSetupPage(),
                _buildTestAndFinishPage(),
              ],
            ),
          ),
          
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (_currentPage > 0)
                  TextButton(
                    onPressed: _previousPage,
                    child: const Text('Previous'),
                  )
                else
                  const SizedBox(),
                
                ElevatedButton(
                  onPressed: _isLoading ? null : _nextPage,
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(_currentPage == 3 ? 'Finish Setup' : 'Next'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomePage() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.rocket_launch,
            size: 80,
            color: Colors.indigo,
          ),
          const SizedBox(height: 24),
          Text(
            'Welcome to Flight Fear Wellness',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            'This wizard will help you configure your subscription system with LemonSqueezy payments and AI services.',
            style: Theme.of(context).textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'What you\'ll need:',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                _buildRequirementItem('LemonSqueezy store ID and API key'),
                _buildRequirementItem('OpenAI API key'),
                _buildRequirementItem('ElevenLabs API key'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequirementItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(Icons.check_circle, color: Colors.green, size: 16),
          const SizedBox(width: 8),
          Expanded(child: Text(text)),
        ],
      ),
    );
  }

  Widget _buildLemonSqueezySetupPage() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'LemonSqueezy Configuration',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Configure your LemonSqueezy payment processing. You can find these values in your LemonSqueezy dashboard.',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 24),
          
          _buildTextField(
            controller: _lemonsqueezyStoreIdController,
            label: 'Store ID',
            hint: 'Enter your LemonSqueezy store ID',
            icon: Icons.store,
          ),
          const SizedBox(height: 16),
          
          _buildTextField(
            controller: _lemonsqueezyApiKeyController,
            label: 'API Key',
            hint: 'Enter your LemonSqueezy API key',
            icon: Icons.key,
            obscureText: true,
          ),
          const SizedBox(height: 24),
          
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.amber.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.amber.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.info, color: Colors.amber.shade700),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Make sure to create your subscription products in LemonSqueezy before proceeding.',
                    style: TextStyle(color: Colors.amber.shade700),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAPIKeysSetupPage() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'AI Services Configuration',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Configure your AI service API keys for chat and voice features.',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 24),
          
          _buildTextField(
            controller: _openaiApiKeyController,
            label: 'OpenAI API Key',
            hint: 'sk-...',
            icon: Icons.chat,
            obscureText: true,
          ),
          const SizedBox(height: 16),
          
          _buildTextField(
            controller: _elevenLabsApiKeyController,
            label: 'ElevenLabs API Key',
            hint: 'Enter your ElevenLabs API key',
            icon: Icons.record_voice_over,
            obscureText: true,
          ),
          const SizedBox(height: 24),
          
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.security, color: Colors.green.shade700),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'All API keys are encrypted and stored securely on the device.',
                    style: TextStyle(color: Colors.green.shade700),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestAndFinishPage() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Test Configuration',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Let\'s test your configuration to make sure everything is working correctly.',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 24),
          
          if (_testResults.isNotEmpty) ...[
            _buildTestResult('LemonSqueezy', _testResults['lemonsqueezy'] ?? false),
            _buildTestResult('OpenAI', _testResults['openai'] ?? false),
            _buildTestResult('ElevenLabs', _testResults['elevenlabs'] ?? false),
            const SizedBox(height: 24),
          ],
          
          if (_testResults.values.every((result) => result)) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 32),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Setup Complete!',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.green.shade700,
                          ),
                        ),
                        Text(
                          'Your subscription system is ready to use.',
                          style: TextStyle(color: Colors.green.shade600),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    bool obscureText = false,
  }) {
    return TextField(
      controller: controller,
      obscureText: obscureText,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        suffixIcon: obscureText
            ? IconButton(
                icon: Icon(Icons.copy),
                onPressed: () {
                  Clipboard.setData(ClipboardData(text: controller.text));
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('$label copied to clipboard')),
                  );
                },
              )
            : null,
      ),
    );
  }

  Widget _buildTestResult(String service, bool success) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: success ? Colors.green.shade50 : Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: success ? Colors.green.shade200 : Colors.red.shade200,
        ),
      ),
      child: Row(
        children: [
          Icon(
            success ? Icons.check_circle : Icons.error,
            color: success ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 12),
          Text(
            service,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: success ? Colors.green.shade700 : Colors.red.shade700,
            ),
          ),
          const Spacer(),
          Text(
            success ? 'Connected' : 'Failed',
            style: TextStyle(
              color: success ? Colors.green.shade600 : Colors.red.shade600,
            ),
          ),
        ],
      ),
    );
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextPage() async {
    if (_currentPage < 3) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      await _finishSetup();
    }
  }

  Future<void> _finishSetup() async {
    setState(() => _isLoading = true);
    
    try {
      await _testConfiguration();
      
      if (_testResults.values.every((result) => result)) {
        await SubscriptionService.initializeForClient(
          lemonsqueezyStoreId: _lemonsqueezyStoreIdController.text,
          lemonsqueezyApiKey: _lemonsqueezyApiKeyController.text,
          openaiApiKey: _openaiApiKeyController.text,
          elevenLabsApiKey: _elevenLabsApiKeyController.text,
        );
        
        Navigator.of(context).pushReplacementNamed('/home');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Setup failed: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testConfiguration() async {
    final apiService = SecureAPIService();
    final connectivity = await apiService.testAPIConnectivity();
    
    setState(() {
      _testResults = {
        'lemonsqueezy': _lemonsqueezyStoreIdController.text.isNotEmpty && 
                      _lemonsqueezyApiKeyController.text.isNotEmpty,
        'openai': connectivity['openai'] ?? false,
        'elevenlabs': connectivity['elevenlabs'] ?? false,
      };
    });
  }
}
