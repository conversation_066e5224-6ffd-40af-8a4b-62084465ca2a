import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart' show timeDilation;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:async';

import '../../blocs/auth/auth_bloc.dart';
import '../../blocs/auth/auth_state.dart';
import '../../services/app_preloader_service.dart';
import '../auth/login_screen.dart';
import '../main_app_screen.dart';



class FearFlightSplashScreen extends StatefulWidget {
  final VoidCallback? onLoadingComplete;

  const FearFlightSplashScreen({
    super.key,
    this.onLoadingComplete,
  });

  @override
  State<FearFlightSplashScreen> createState() => _FearFlightSplashScreenState();
}

class _FearFlightSplashScreenState extends State<FearFlightSplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _cloudAnimation;
  late Animation<double> _progressAnimation;

  String _currentLoadingText = "Initializing Alora...";
  double _loadingProgress = 0.0;
  bool _isLoadingComplete = false;

  final _preloaderService = AppPreloaderService();

  @override
  void initState() {
    super.initState();

    timeDilation = 1.0;

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 8),
    );

    _fadeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.4, curve: Curves.easeInOut),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.5, end: 1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.1, 0.6, curve: Curves.elasticOut),
      ),
    );

    _cloudAnimation = Tween<double>(begin: -1.5, end: 2.5).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.linear,
      ),
    );

    _progressAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    _controller.forward();
    _initializeBackendServices();
  }

  Future<void> _initializeBackendServices() async {
    try {
      await _preloaderService.initializeAllServices(
        onProgress: (message, progress) {
          _updateLoadingState(message, progress);
        },
      );

      setState(() {
        _isLoadingComplete = true;
        _currentLoadingText = "Welcome to your journey with Alora!";
      });

      await Future.delayed(const Duration(milliseconds: 1500));
      widget.onLoadingComplete?.call();

    } catch (e) {
      _updateLoadingState("Finalizing setup...", 1.0);
      await Future.delayed(const Duration(milliseconds: 2000));
      widget.onLoadingComplete?.call();
    }
  }

  void _updateLoadingState(String text, double progress) {
    if (mounted) {
      setState(() {
        _currentLoadingText = text;
        _loadingProgress = progress;
      });
    }
  }



  void _navigateToNextScreen() {
    if (!mounted) return;

    final authBloc = context.read<AuthBloc>();
    if (authBloc.state.status == AuthStatus.authenticated) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const MainAppScreen()),
      );
    } else {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const LoginScreen()),
      );
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF1E3A8A),
                  Color(0xFF3B82F6),
                  Color(0xFF60A5FA),
                ],
              ),
            ),
          ),

          AnimatedBuilder(
            animation: _cloudAnimation,
            builder: (context, child) {
              return Stack(
                children: [
                  _buildGeometricShape(0.1, 0.15, 60, _cloudAnimation.value * 0.3),
                  _buildGeometricShape(0.8, 0.25, 40, _cloudAnimation.value * 0.5),
                  _buildGeometricShape(0.2, 0.7, 50, _cloudAnimation.value * 0.4),
                ],
              );
            },
          ),

          Center(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white.withValues(alpha: 0.15),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                          width: 2,
                        ),
                      ),
                      child: const Icon(
                        Icons.psychology,
                        size: 60,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 30),
                    const Text(
                      'Alora',
                      style: TextStyle(
                        fontSize: 48,
                        fontWeight: FontWeight.w300,
                        color: Colors.white,
                        letterSpacing: 4,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Hi! I am Alora',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                        fontWeight: FontWeight.w400,
                        letterSpacing: 1,
                      ),
                    ),
                    const SizedBox(height: 40),
                    AnimatedSwitcher(
                      duration: const Duration(milliseconds: 500),
                      child: Text(
                        _currentLoadingText,
                        key: ValueKey(_currentLoadingText),
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 20),
                    Container(
                      width: 200,
                      height: 6,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(3),
                        color: Colors.white.withValues(alpha: 0.3),
                      ),
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        width: 200 * _loadingProgress,
                        height: 6,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(3),
                          gradient: LinearGradient(
                            colors: _isLoadingComplete
                                ? [Colors.green.shade300, Colors.green.shade500]
                                : [const Color(0xFF5D69BE), const Color(0xFFC89BEB)],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          Positioned(
            bottom: 50,
            left: 20,
            right: 20,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Text(
                _isLoadingComplete
                    ? "Ready to begin your transformation"
                    : "Preparing your personalized therapy experience",
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.white70,
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGeometricShape(double left, double top, double size, double position) {
    return Positioned(
      left: MediaQuery.of(context).size.width * (left + position),
      top: MediaQuery.of(context).size.height * top,
      child: Opacity(
        opacity: 0.1,
        child: Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.white,
              width: 2,
            ),
          ),
        ),
      ),
    );
  }
}

