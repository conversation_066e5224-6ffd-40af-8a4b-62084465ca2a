import 'package:flutter/material.dart';

class CreditUsageWidget extends StatelessWidget {
  final int chatUsed;
  final int chatLimit;
  final int voiceUsed;
  final int voiceLimit;
  final int daysRemaining;

  const CreditUsageWidget({
    super.key,
    required this.chatUsed,
    required this.chatLimit,
    required this.voiceUsed,
    required this.voiceLimit,
    required this.daysRemaining,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Credit Usage',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _getDaysRemainingColor().withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '$daysRemaining days left',
                  style: TextStyle(
                    color: _getDaysRemainingColor(),
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          _buildCreditSection(
            context,
            'Chat Messages',
            Icons.chat_bubble_outline,
            chatUsed,
            chatLimit,
            Colors.blue,
          ),
          const SizedBox(height: 20),

          _buildCreditSection(
            context,
            'Voice Messages',
            Icons.record_voice_over,
            voiceUsed,
            voiceLimit,
            Colors.purple,
          ),
        ],
      ),
    );
  }

  Widget _buildCreditSection(
    BuildContext context,
    String title,
    IconData icon,
    int used,
    int limit,
    Color color,
  ) {
    final percentage = limit > 0 ? (used / limit).clamp(0.0, 1.0) : 0.0;
    final remaining = (limit - used).clamp(0, limit);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        Container(
          height: 8,
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(4),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: percentage,
            child: Container(
              decoration: BoxDecoration(
                color: _getProgressColor(percentage, color),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),

        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '$remaining remaining',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '$used / ${_formatLimit(limit)} used',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),

        if (percentage >= 0.8) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _getWarningColor(percentage).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  percentage >= 0.95 ? Icons.warning : Icons.info_outline,
                  color: _getWarningColor(percentage),
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  percentage >= 0.95 
                      ? 'Almost out of credits!'
                      : 'Running low on credits',
                  style: TextStyle(
                    color: _getWarningColor(percentage),
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Color _getProgressColor(double percentage, Color baseColor) {
    if (percentage >= 0.95) {
      return Colors.red;
    } else if (percentage >= 0.8) {
      return Colors.orange;
    } else {
      return baseColor;
    }
  }

  Color _getWarningColor(double percentage) {
    if (percentage >= 0.95) {
      return Colors.red;
    } else {
      return Colors.orange;
    }
  }

  Color _getDaysRemainingColor() {
    if (daysRemaining <= 3) {
      return Colors.red;
    } else if (daysRemaining <= 7) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  String _formatLimit(int limit) {
    if (limit >= 20000) {
      return '20K';
    } else if (limit >= 1000) {
      return '${(limit / 1000).toStringAsFixed(0)}K';
    } else {
      return limit.toString();
    }
  }
}
