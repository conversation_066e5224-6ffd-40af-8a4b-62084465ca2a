org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=false

# Network configuration for better connectivity
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true

# Increase timeout for network operations
systemProp.http.connectionTimeout=60000
systemProp.http.socketTimeout=60000
systemProp.https.connectionTimeout=60000
systemProp.https.socketTimeout=60000
