import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flight_fear_wellness_app/models/chat_message.dart';
import 'package:flight_fear_wellness_app/services/crisis_intervention_service.dart';



@immutable
abstract class ChatEvent extends Equatable {
  const ChatEvent();

  @override
  List<Object> get props => [];
}

class SendMessageEvent extends ChatEvent {
  final String message;
  final String userId;

  const SendMessageEvent({
    required this.message,
    required this.userId,
  });

  @override
  List<Object> get props => [message, userId];
}

class LoadChatHistoryEvent extends ChatEvent {
  final String userId;

  const LoadChatHistoryEvent(this.userId);

  @override
  List<Object> get props => [userId];
}

class ClearChatHistoryEvent extends ChatEvent {
  final String userId;

  const ClearChatHistoryEvent(this.userId);

  @override
  List<Object> get props => [userId];
}

class GenerateAIResponseEvent extends ChatEvent {
  final String userId;
  final String message;

  const GenerateAIResponseEvent({
    required this.userId,
    required this.message,
  });

  @override
  List<Object> get props => [userId, message];
}

class ChatMessagesUpdated extends ChatEvent {
  final List<ChatMessage> messages;

  const ChatMessagesUpdated(this.messages);

  @override
  List<Object> get props => [messages];
}

class TriggerCrisisInterventionEvent extends ChatEvent {
  final CrisisInterventionType type;
  final String userId;
  final String moodLevel;

  const TriggerCrisisInterventionEvent({
    required this.type,
    required this.userId,
    required this.moodLevel,
  });

  @override
  List<Object> get props => [type, userId, moodLevel];
}

class ReturnFromBreathingExerciseEvent extends ChatEvent {
  final String userId;
  final String exerciseType;

  const ReturnFromBreathingExerciseEvent({
    required this.userId,
    required this.exerciseType,
  });

  @override
  List<Object> get props => [userId, exerciseType];
}

class OfferDebateModeEvent extends ChatEvent {
  final String userId;
  final String topic;

  const OfferDebateModeEvent({
    required this.userId,
    required this.topic,
  });

  @override
  List<Object> get props => [userId, topic];
}

class CloseCrisisInterventionEvent extends ChatEvent {
  final String userId;

  const CloseCrisisInterventionEvent({
    required this.userId,
  });

  @override
  List<Object> get props => [userId];
}