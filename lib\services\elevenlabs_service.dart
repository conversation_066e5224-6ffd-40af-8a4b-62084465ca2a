import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flight_fear_wellness_app/utils/constants.dart';
import 'package:path_provider/path_provider.dart';

// Purpose: Handle text-to-speech conversion using ElevenLabs API
class ElevenLabsService {
  final Dio _dio = Dio();

  // Purpose: Initialize HTTP client with optimized timeout settings for voice generation
  ElevenLabsService() {
    _dio.options.connectTimeout = const Duration(seconds: 3);
    _dio.options.receiveTimeout = const Duration(seconds: 8);
  }

  // Purpose: Convert text to speech and return local file path of generated audio
  Future<String> textToSpeech(String text) async {
    try {
      final response = await _dio.post(
        '${AppConstants.elevenLabsBaseUrl}/text-to-speech/${AppConstants.defaultVoiceId}',
        options: Options(
          headers: {
            'xi-api-key': AppConstants.elevenLabsApiKey,
            'Content-Type': 'application/json',
          },
          responseType: ResponseType.bytes,
        ),
        data: jsonEncode({
          'text': text,
          'voice_settings': AppConstants.voiceSettings,
        }),
      );

      final tempDir = await getTemporaryDirectory();
      final filePath = '${tempDir.path}/tts_${DateTime.now().millisecondsSinceEpoch}.mp3';
      final file = File(filePath);
      await file.writeAsBytes(response.data);

      return filePath;
    } catch (e) {
      throw Exception('Failed to convert text to speech: $e');
    }
  }
}