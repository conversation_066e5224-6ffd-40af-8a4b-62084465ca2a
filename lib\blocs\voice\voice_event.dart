import 'package:equatable/equatable.dart';
import 'package:flight_fear_wellness_app/services/crisis_intervention_service.dart';
import 'package:flutter/material.dart';

@immutable
abstract class VoiceEvent extends Equatable {
  const VoiceEvent();

  @override
  List<Object> get props => [];
}

class StartRecordingEvent extends VoiceEvent {
  final String userId;

  const StartRecordingEvent(this.userId);
}

class StopRecordingEvent extends VoiceEvent {
  final String userId;

  const StopRecordingEvent(this.userId);

  @override
  List<Object> get props => [userId];
}

class ProcessVoiceInputEvent extends VoiceEvent {
  final String userId;
  final String input;

  const ProcessVoiceInputEvent({required this.userId, required this.input});
}

class PlayAudioEvent extends VoiceEvent {
  final String filePath;

  const PlayAudioEvent(this.filePath);
}

class StopAudioEvent extends VoiceEvent {}

class StartTextAnimationEvent extends VoiceEvent {
  final String text;
  final String audioFilePath;

  const StartTextAnimationEvent({required this.text, required this.audioFilePath});

  @override
  List<Object> get props => [text, audioFilePath];
}

class UpdateDisplayedTextEvent extends VoiceEvent {
  final int wordIndex;

  const UpdateDisplayedTextEvent(this.wordIndex);

  @override
  List<Object> get props => [wordIndex];
}

class StopTextAnimationEvent extends VoiceEvent {}

class ResetVoiceSessionEvent extends VoiceEvent {}

class UpdateSpeechResultEvent extends VoiceEvent {
  final String result;

  const UpdateSpeechResultEvent(this.result);

  @override
  List<Object> get props => [result];
}

class SpeechErrorEvent extends VoiceEvent {
  final String error;

  const SpeechErrorEvent(this.error);

  @override
  List<Object> get props => [error];
}

class TriggerVoiceCrisisInterventionEvent extends VoiceEvent {
  final CrisisInterventionType type;
  final String userId;
  final String moodLevel;

  const TriggerVoiceCrisisInterventionEvent({
    required this.type,
    required this.userId,
    required this.moodLevel,
  });

  @override
  List<Object> get props => [type, userId, moodLevel];
}

class CloseVoiceCrisisInterventionEvent extends VoiceEvent {
  final String userId;

  const CloseVoiceCrisisInterventionEvent({
    required this.userId,
  });

  @override
  List<Object> get props => [userId];
}

class ReturnFromVoiceBreathingExerciseEvent extends VoiceEvent {
  final String userId;
  final String exerciseType;

  const ReturnFromVoiceBreathingExerciseEvent({
    required this.userId,
    required this.exerciseType,
  });

  @override
  List<Object> get props => [userId, exerciseType];
}