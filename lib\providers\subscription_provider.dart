import 'package:flutter/foundation.dart';
import '../models/user_subscription_model.dart';
import '../models/subscription_model.dart';
import '../models/credit_model.dart';
import '../services/subscription_service.dart';
import '../utils/subscription_constants.dart';
import '../services/payment_detection_service.dart';
import '../services/usage_analytics_service.dart';
import '../services/notification_service.dart';
import '../models/notification_model.dart';

class SubscriptionProvider extends ChangeNotifier {
  final SubscriptionService _subscriptionService = SubscriptionService();
  final NotificationService _notificationService = NotificationService();

  UserSubscriptionModel? _userSubscription;
  bool _isLoading = false;
  String? _error;
  bool _isInitialized = false;

  UserSubscriptionModel? get userSubscription => _userSubscription;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isInitialized => _isInitialized;

  bool get hasActiveSubscription => _userSubscription?.isSubscriptionActive ?? false;
  bool get needsUpgrade => _userSubscription?.needsUpgrade ?? true;
  SubscriptionPlan get currentPlan => _userSubscription?.subscription.plan ?? SubscriptionPlan.free;
  String get planName => _userSubscription?.planName ?? 'Free Plan';
  double get planPrice => _userSubscription?.planPrice ?? 0.0;
  int get daysRemaining => _userSubscription?.daysRemaining ?? 0;

  int get chatCreditsRemaining => _userSubscription?.credits.chatRemaining ?? 0;
  int get voiceCreditsRemaining => _userSubscription?.credits.voiceRemaining ?? 0;
  int get chatCreditsUsed => _userSubscription?.credits.chatUsed ?? 0;
  int get voiceCreditsUsed => _userSubscription?.credits.voiceUsed ?? 0;
  int get chatCreditsLimit => _userSubscription?.credits.chatLimit ?? 0;
  int get voiceCreditsLimit => _userSubscription?.credits.voiceLimit ?? 0;

  double get chatUsagePercentage => _userSubscription?.credits.chatUsagePercentage ?? 0.0;
  double get voiceUsagePercentage => _userSubscription?.credits.voiceUsagePercentage ?? 0.0;

  bool get isNearChatLimit => _userSubscription?.credits.isNearChatLimit ?? false;
  bool get isNearVoiceLimit => _userSubscription?.credits.isNearVoiceLimit ?? false;
  bool get hasChatCredits => _userSubscription?.canUseChat ?? false;
  bool get hasVoiceCredits => _userSubscription?.canUseVoice ?? false;

  Future<void> initialize() async {
    if (_isInitialized) {
      print('SubscriptionProvider: Already initialized');
      return;
    }

    print('SubscriptionProvider: Starting initialization');
    _setLoading(true);

    try {
      print('SubscriptionProvider: Loading user subscription');
      await _loadUserSubscription();

      print('SubscriptionProvider: Migrating existing user if needed');
      await _subscriptionService.migrateExistingUser();

      print('SubscriptionProvider: Starting payment detection');
      PaymentDetectionService.startListening(this);

      _isInitialized = true;
      _clearError();
      print('SubscriptionProvider: Initialization completed successfully');

      startListening();

      _subscriptionService.setCreditsUpdatedCallback(() {
        print('SubscriptionProvider: Received credit update callback, force refreshing');
        forceRefresh();
      });

      _checkPlanExpiration();

    } catch (e) {
      print('SubscriptionProvider: Initialization failed: $e');
      _setError('Failed to initialize subscription: $e');

      _isInitialized = true;
    } finally {
      _setLoading(false);
    }
  }

  void updateUserSubscription(UserSubscriptionModel userSubscription) {
    _userSubscription = userSubscription;
    notifyListeners();
  }

  Future<UpgradeSuggestion?> checkUpgradeSuggestions() async {
    if (_userSubscription == null) return null;

    try {
      return await UsageAnalyticsService.analyzeUsagePatterns(_userSubscription!);
    } catch (e) {
      print('Error checking upgrade suggestions: $e');
      return null;
    }
  }

  Future<void> trackActivity(String activityType, {Map<String, dynamic>? metadata}) async {
    if (_userSubscription == null) return;

    try {
      await UsageAnalyticsService.trackActivity(
        userId: _userSubscription!.user.id,
        activityType: activityType,
        metadata: metadata,
      );
    } catch (e) {
      print('Error tracking activity: $e');
    }
  }

  Future<void> _loadUserSubscription() async {
    try {
      _userSubscription = await _subscriptionService.getCurrentUserSubscription();
      notifyListeners();
    } catch (e) {
      throw Exception('Failed to load subscription data: $e');
    }
  }

  Future<void> refresh() async {
    _setLoading(true);
    try {
      await _loadUserSubscription();
      await _subscriptionService.updateLastActive();
      _clearError();
    } catch (e) {
      _setError('Failed to refresh subscription: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> forceRefresh() async {
    print('SubscriptionProvider: Force refreshing subscription data');
    try {
      final updatedSubscription = await _subscriptionService.getCurrentUserSubscription();
      if (updatedSubscription != null) {
        _userSubscription = updatedSubscription;
        notifyListeners();
        print('SubscriptionProvider: Force refresh completed - Chat: ${updatedSubscription.credits.chatRemaining}, Voice: ${updatedSubscription.credits.voiceRemaining}');
      }
    } catch (e) {
      print('SubscriptionProvider: Force refresh failed: $e');
    }
  }

  void updateCreditsDirectly(Map<String, dynamic> creditsData) {
    if (_userSubscription != null) {
      try {
        print('SubscriptionProvider: Directly updating credits - Chat: ${creditsData['chat']?['remaining'] ?? 'N/A'}, Voice: ${creditsData['voice']?['remaining'] ?? 'N/A'}');

        final oldCredits = _userSubscription!.credits;
        final updatedCredits = CreditModel.fromMap(creditsData);
        final updatedSubscription = _userSubscription!.copyWith(
          credits: updatedCredits,
        );

        _userSubscription = updatedSubscription;
        notifyListeners();
        print('SubscriptionProvider: Direct credit update completed - New Chat: ${updatedCredits.chatRemaining}, New Voice: ${updatedCredits.voiceRemaining}');

        // Check for credit warnings and create notifications
        _checkAndCreateCreditNotifications(oldCredits, updatedCredits);
      } catch (e) {
        print('SubscriptionProvider: Error in direct credit update: $e');
        forceRefresh();
      }
    } else {
      print('SubscriptionProvider: Cannot update credits - no user subscription loaded');
    }
  }

  Future<bool> canUseChat() async {
    if (_userSubscription == null) await _loadUserSubscription();
    return _userSubscription?.canUseChat ?? false;
  }

  Future<bool> canUseVoice() async {
    if (_userSubscription == null) await _loadUserSubscription();
    return _userSubscription?.canUseVoice ?? false;
  }

  void useChatCredit() {
    if (_userSubscription != null) {
      _userSubscription = _userSubscription!.useChatCredit();
      notifyListeners();
    }
  }

  void useVoiceCredit() {
    if (_userSubscription != null) {
      _userSubscription = _userSubscription!.useVoiceCredit();
      notifyListeners();
    }
  }

  Future<void> _checkAndCreateCreditNotifications(CreditModel oldCredits, CreditModel newCredits) async {
    final userId = _userSubscription?.user.id;
    if (userId == null) return;

    // Check for chat credit warnings (when reaching 80% usage)
    final oldChatUsage = oldCredits.chatUsagePercentage;
    final newChatUsage = newCredits.chatUsagePercentage;

    if (oldChatUsage < 0.8 && newChatUsage >= 0.8 && newCredits.chatRemaining > 0) {
      await _notificationService.sendCreditWarningNotification(
        userId,
        'chat',
        newCredits.chatRemaining,
        newCredits.chatLimit,
      );
    }

    // Check for voice credit warnings (when reaching 80% usage)
    final oldVoiceUsage = oldCredits.voiceUsagePercentage;
    final newVoiceUsage = newCredits.voiceUsagePercentage;

    if (oldVoiceUsage < 0.8 && newVoiceUsage >= 0.8 && newCredits.voiceRemaining > 0) {
      await _notificationService.sendCreditWarningNotification(
        userId,
        'voice',
        newCredits.voiceRemaining,
        newCredits.voiceLimit,
      );
    }

    // Check for complete depletion notifications
    if (oldCredits.chatRemaining > 0 && newCredits.chatRemaining == 0) {
      await _notificationService.createNotification(
        NotificationFactory.createCreditWarningNotification(
          userId,
          'chat',
          0,
          newCredits.chatLimit,
        ),
      );
    }

    if (oldCredits.voiceRemaining > 0 && newCredits.voiceRemaining == 0) {
      await _notificationService.createNotification(
        NotificationFactory.createCreditWarningNotification(
          userId,
          'voice',
          0,
          newCredits.voiceLimit,
        ),
      );
    }
  }

  List<Map<String, dynamic>> getAvailablePlans() {
    return SubscriptionPlan.values.map((plan) {
      final config = SubscriptionConstants.getPlanConfig(plan);
      final isCurrentPlan = plan == currentPlan;
      final canUpgrade = SubscriptionConstants.canUpgradeTo(currentPlan, plan);

      return {
        'plan': plan,
        'id': config['id'],
        'name': config['name'],
        'price': config['price'],
        'chatCredits': config['chatCredits'],
        'voiceCredits': config['voiceCredits'],
        'features': config['features'],
        'popular': config['popular'],
        'color': config['color'],
        'available': config['available'],
        'isCurrentPlan': isCurrentPlan,
        'canUpgrade': canUpgrade,
        'description': config['description'],
      };
    }).toList();
  }

  Map<String, dynamic> getSubscriptionStatus() {
    return {
      'plan': currentPlan.name,
      'planName': planName,
      'price': planPrice,
      'isActive': hasActiveSubscription,
      'needsUpgrade': needsUpgrade,
      'daysRemaining': daysRemaining,
      'chatCredits': chatCreditsRemaining,
      'voiceCredits': voiceCreditsRemaining,
      'chatUsed': chatCreditsUsed,
      'voiceUsed': voiceCreditsUsed,
      'chatLimit': chatCreditsLimit,
      'voiceLimit': voiceCreditsLimit,
      'chatUsagePercentage': chatUsagePercentage,
      'voiceUsagePercentage': voiceUsagePercentage,
      'isNearChatLimit': isNearChatLimit,
      'isNearVoiceLimit': isNearVoiceLimit,
    };
  }

  String getUpgradeUrl(SubscriptionPlan plan) {
    return _subscriptionService.getLemonSqueezyUpgradeUrl(plan.name);
  }

  Future<bool> isExpired() async {
    return await _subscriptionService.isSubscriptionExpired();
  }

  String? getUsageWarning() {
    if (!hasActiveSubscription) {
      return SubscriptionConstants.expiredSubscriptionMessage;
    }
    
    if (isNearChatLimit && isNearVoiceLimit) {
      return 'You\'re running low on both chat and voice credits. Consider upgrading your plan.';
    } else if (isNearChatLimit) {
      return 'You\'re running low on chat credits. Consider upgrading your plan.';
    } else if (isNearVoiceLimit) {
      return 'You\'re running low on voice credits. Consider upgrading your plan.';
    }
    
    if (!hasChatCredits && !hasVoiceCredits) {
      return SubscriptionConstants.noCreditsMessage;
    } else if (!hasChatCredits) {
      return 'You\'ve used all your chat credits. Upgrade to continue chatting.';
    } else if (!hasVoiceCredits) {
      return 'You\'ve used all your voice credits. Upgrade to continue using voice features.';
    }
    
    return null;
  }

  void startListening() {
    print('SubscriptionProvider: Starting real-time subscription stream');

    _subscriptionService.subscriptionStream().listen(
      (subscription) {
        print('SubscriptionProvider: Received subscription update from stream');
        if (subscription != null) {
          final oldPlan = _userSubscription?.subscription.plan;
          final newPlan = subscription.subscription.plan;
          final planChanged = oldPlan != null && oldPlan != newPlan;

          print('SubscriptionProvider: Updating subscription - Plan: ${newPlan.name}, Chat: ${subscription.credits.chatRemaining}, Voice: ${subscription.credits.voiceRemaining}');

          _userSubscription = subscription;
          notifyListeners();

          if (planChanged) {
            print('SubscriptionProvider: Plan changed from ${oldPlan.name} to ${newPlan.name} - likely admin update');
          }

          print('SubscriptionProvider: UI notified of subscription changes');
        } else {
          print('SubscriptionProvider: Received null subscription from stream');
        }
      },
      onError: (error) {
        print('SubscriptionProvider: Subscription stream error: $error');
        _setError('Subscription stream error: $error');
      },
    );
  }

  Future<void> refreshSubscriptionData() async {
    print('SubscriptionProvider: Manually refreshing subscription data');
    try {
      final updatedSubscription = await _subscriptionService.getCurrentUserSubscription();
      if (updatedSubscription != null) {
        final oldPlan = _userSubscription?.subscription.plan;
        _userSubscription = updatedSubscription;
        notifyListeners();

        final newPlan = updatedSubscription.subscription.plan;
        print('SubscriptionProvider: Manual refresh completed - Plan: ${newPlan.name}');

        if (oldPlan != null && oldPlan != newPlan) {
          print('SubscriptionProvider: Plan changed during refresh from ${oldPlan.name} to ${newPlan.name}');
        }
      }
    } catch (e) {
      print('SubscriptionProvider: Manual refresh failed: $e');
      _setError('Failed to refresh subscription: $e');
    }
  }

  void simulateExpiredPlan() {
    if (_userSubscription != null) {
      final expiredSubscription = _userSubscription!.subscription.copyWith(
        status: SubscriptionStatus.expired,
        endDate: DateTime.now().subtract(const Duration(days: 1)),
      );
      _userSubscription = _userSubscription!.copyWith(subscription: expiredSubscription);
      notifyListeners();
      print('SubscriptionProvider: Simulated expired plan for testing');
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void _checkPlanExpiration() {
    if (_userSubscription == null) return;

    final subscription = _userSubscription!.subscription;

    if (subscription.isExpired) {
      print('SubscriptionProvider: Subscription has expired - Plan: ${subscription.plan.name}');
    } else if (subscription.daysRemaining <= 2 && subscription.daysRemaining > 0) {
      print('SubscriptionProvider: Subscription expiring soon - ${subscription.daysRemaining} days remaining');
    }
  }

}
