import 'package:flutter/material.dart';

class AnimatedVoiceText extends StatefulWidget {
  final String fullText;
  final String displayedText;
  final bool isAnimating;
  final VoidCallback? onAnimationComplete;
  final TextStyle? textStyle;

  const AnimatedVoiceText({
    super.key,
    required this.fullText,
    required this.displayedText,
    required this.isAnimating,
    this.onAnimationComplete,
    this.textStyle,
  });

  @override
  State<AnimatedVoiceText> createState() => _AnimatedVoiceTextState();
}

class _AnimatedVoiceTextState extends State<AnimatedVoiceText>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeIn),
    );
  }

  @override
  void didUpdateWidget(AnimatedVoiceText oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.displayedText != oldWidget.displayedText) {
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isAnimating && widget.displayedText.isEmpty && widget.fullText.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                widget.isAnimating ? Icons.record_voice_over : Icons.check_circle,
                color: widget.isAnimating ? Colors.blue.shade600 : Colors.green.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                widget.isAnimating ? 'Alora is speaking...' : 'Alora said:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: widget.isAnimating ? Colors.blue.shade700 : Colors.green.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          AnimatedBuilder(
            animation: _fadeAnimation,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    style: widget.textStyle ?? const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      height: 1.4,
                      color: Colors.black87,
                    ),
                    children: _buildTextSpans(),
                  ),
                ),
              );
            },
          ),
          if (widget.isAnimating)
            const SizedBox(height: 8),
          if (widget.isAnimating)
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                for (int i = 0; i < 3; i++)
                  AnimatedContainer(
                    duration: Duration(milliseconds: 300 + (i * 100)),
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    width: 4,
                    height: widget.isAnimating ? 20 : 4,
                    decoration: BoxDecoration(
                      color: Colors.blue.shade400,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
              ],
            ),
        ],
      ),
    );
  }

  List<TextSpan> _buildTextSpans() {
    final textToShow = widget.displayedText.isNotEmpty ? widget.displayedText : widget.fullText;

    return [
      TextSpan(
        text: textToShow,
        style: widget.textStyle ?? const TextStyle(
          color: Colors.black87,
          fontWeight: FontWeight.w500,
        ),
      ),
    ];
  }
}
