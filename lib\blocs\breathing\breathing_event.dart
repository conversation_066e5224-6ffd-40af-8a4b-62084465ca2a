import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'breathing_state.dart';

@immutable
abstract class BreathingEvent extends Equatable {
  const BreathingEvent();

  @override
  List<Object> get props => [];
}

class StartBreathingExercise extends BreathingEvent {
  final BreathingPattern pattern;

  const StartBreathingExercise({this.pattern = BreathingPattern.fourSevenEight});

  @override
  List<Object> get props => [pattern];
}

class PauseBreathingExercise extends BreathingEvent {}

class ResumeBreathingExercise extends BreathingEvent {}

class ResetBreathingExercise extends BreathingEvent {}

class BreathingCycleTick extends BreathingEvent {}

class PreparationCountdownTick extends BreathingEvent {}

class SelectBreathingPattern extends BreathingEvent {
  final BreathingPattern pattern;

  const SelectBreathingPattern(this.pattern);

  @override
  List<Object> get props => [pattern];
}