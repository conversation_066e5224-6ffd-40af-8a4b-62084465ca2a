import 'package:equatable/equatable.dart';
import 'package:flight_fear_wellness_app/models/chat_message.dart';
import 'package:flight_fear_wellness_app/services/crisis_intervention_service.dart';

enum ChatStatus { initial, loading, success, error }

class ChatState extends Equatable {
  final ChatStatus status;
  final List<ChatMessage> messages;
  final String? error;
  final bool isGeneratingResponse;
  final List<CrisisInterventionOption> crisisOptions;
  final String? currentMoodLevel;
  final bool shouldOfferDebate;
  final String? debateTopic;

  const ChatState({
    this.status = ChatStatus.initial,
    this.messages = const [],
    this.error,
    this.isGeneratingResponse = false,
    this.crisisOptions = const [],
    this.currentMoodLevel,
    this.shouldOfferDebate = false,
    this.debateTopic,
  });

  ChatState copyWith({
    ChatStatus? status,
    List<ChatMessage>? messages,
    String? error,
    bool? isGeneratingResponse,
    List<CrisisInterventionOption>? crisisOptions,
    String? currentMoodLevel,
    bool? shouldOfferDebate,
    String? debateTopic,
  }) {
    return ChatState(
      status: status ?? this.status,
      messages: messages ?? this.messages,
      error: error ?? this.error,
      isGeneratingResponse: isGeneratingResponse ?? this.isGeneratingResponse,
      crisisOptions: crisisOptions ?? this.crisisOptions,
      currentMoodLevel: currentMoodLevel ?? this.currentMoodLevel,
      shouldOfferDebate: shouldOfferDebate ?? this.shouldOfferDebate,
      debateTopic: debateTopic ?? this.debateTopic,
    );
  }

  @override
  List<Object?> get props => [
    status,
    messages,
    error,
    isGeneratingResponse,
    crisisOptions,
    currentMoodLevel,
    shouldOfferDebate,
    debateTopic
  ];
}