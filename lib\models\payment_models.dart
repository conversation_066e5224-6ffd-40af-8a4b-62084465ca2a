import 'package:equatable/equatable.dart';
import 'subscription_model.dart';

enum PaymentProvider { lemonsqueezy, stripe, paypal }

enum PaymentStatus { 
  pending, 
  processing, 
  completed, 
  failed, 
  cancelled, 
  refunded 
}

enum ProviderSubscriptionStatus {
  active,
  cancelled,
  expired,
  pastDue,
  unpaid,
  trialing
}

class PaymentConfig extends Equatable {
  final PaymentProvider provider;
  final String apiKey;
  final String? secretKey;
  final String? storeId;
  final String? webhookSecret;
  final bool isTestMode;
  final Map<SubscriptionPlan, String> planIds;

  const PaymentConfig({
    required this.provider,
    required this.apiKey,
    this.secretKey,
    this.storeId,
    this.webhookSecret,
    this.isTestMode = true,
    required this.planIds,
  });

  @override
  List<Object?> get props => [
        provider,
        apiKey,
        secretKey,
        storeId,
        webhookSecret,
        isTestMode,
        planIds,
      ];
}

class PaymentIntent extends Equatable {
  final String id;
  final PaymentProvider provider;
  final SubscriptionPlan plan;
  final double amount;
  final String currency;
  final String customerEmail;
  final String? customerId;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;

  const PaymentIntent({
    required this.id,
    required this.provider,
    required this.plan,
    required this.amount,
    required this.currency,
    required this.customerEmail,
    this.customerId,
    this.metadata = const {},
    required this.createdAt,
  });

  @override
  List<Object?> get props => [
        id,
        provider,
        plan,
        amount,
        currency,
        customerEmail,
        customerId,
        metadata,
        createdAt,
      ];
}

class PaymentResult extends Equatable {
  final bool success;
  final String? paymentId;
  final String? subscriptionId;
  final String? customerId;
  final PaymentStatus status;
  final String? error;
  final Map<String, dynamic> metadata;

  const PaymentResult({
    required this.success,
    this.paymentId,
    this.subscriptionId,
    this.customerId,
    required this.status,
    this.error,
    this.metadata = const {},
  });

  factory PaymentResult.success({
    required String paymentId,
    String? subscriptionId,
    String? customerId,
    Map<String, dynamic> metadata = const {},
  }) {
    return PaymentResult(
      success: true,
      paymentId: paymentId,
      subscriptionId: subscriptionId,
      customerId: customerId,
      status: PaymentStatus.completed,
      metadata: metadata,
    );
  }

  factory PaymentResult.failure({
    required String error,
    PaymentStatus status = PaymentStatus.failed,
  }) {
    return PaymentResult(
      success: false,
      error: error,
      status: status,
    );
  }

  @override
  List<Object?> get props => [
        success,
        paymentId,
        subscriptionId,
        customerId,
        status,
        error,
        metadata,
      ];
}

class WebhookEvent extends Equatable {
  final String id;
  final PaymentProvider provider;
  final String eventType;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final String? signature;

  const WebhookEvent({
    required this.id,
    required this.provider,
    required this.eventType,
    required this.data,
    required this.timestamp,
    this.signature,
  });

  @override
  List<Object?> get props => [
        id,
        provider,
        eventType,
        data,
        timestamp,
        signature,
      ];
}

class PaymentCustomer extends Equatable {
  final String id;
  final String email;
  final String name;
  final PaymentProvider provider;
  final String? providerCustomerId;
  final Map<String, dynamic> metadata;

  const PaymentCustomer({
    required this.id,
    required this.email,
    required this.name,
    required this.provider,
    this.providerCustomerId,
    this.metadata = const {},
  });

  @override
  List<Object?> get props => [
        id,
        email,
        name,
        provider,
        providerCustomerId,
        metadata,
      ];
}

class ProviderSubscription extends Equatable {
  final String id;
  final String customerId;
  final SubscriptionPlan plan;
  final ProviderSubscriptionStatus status;
  final DateTime currentPeriodStart;
  final DateTime currentPeriodEnd;
  final bool cancelAtPeriodEnd;
  final double amount;
  final String currency;
  final PaymentProvider provider;
  final Map<String, dynamic> metadata;

  const ProviderSubscription({
    required this.id,
    required this.customerId,
    required this.plan,
    required this.status,
    required this.currentPeriodStart,
    required this.currentPeriodEnd,
    this.cancelAtPeriodEnd = false,
    required this.amount,
    required this.currency,
    required this.provider,
    this.metadata = const {},
  });

  @override
  List<Object?> get props => [
        id,
        customerId,
        plan,
        status,
        currentPeriodStart,
        currentPeriodEnd,
        cancelAtPeriodEnd,
        amount,
        currency,
        provider,
        metadata,
      ];
}

class PaymentInvoice extends Equatable {
  final String id;
  final String customerId;
  final String? subscriptionId;
  final double amount;
  final String currency;
  final PaymentStatus status;
  final DateTime createdAt;
  final DateTime? paidAt;
  final String? paymentMethodId;
  final PaymentProvider provider;
  final Map<String, dynamic> metadata;

  const PaymentInvoice({
    required this.id,
    required this.customerId,
    this.subscriptionId,
    required this.amount,
    required this.currency,
    required this.status,
    required this.createdAt,
    this.paidAt,
    this.paymentMethodId,
    required this.provider,
    this.metadata = const {},
  });

  @override
  List<Object?> get props => [
        id,
        customerId,
        subscriptionId,
        amount,
        currency,
        status,
        createdAt,
        paidAt,
        paymentMethodId,
        provider,
        metadata,
      ];
}
