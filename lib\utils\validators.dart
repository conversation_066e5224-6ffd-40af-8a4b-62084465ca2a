import '../widgets/password_requirements_widget.dart';

class Validators {
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }

    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }

    if (value.contains('..') || value.startsWith('.') || value.endsWith('.')) {
      return 'Invalid email format';
    }

    final domain = value.split('@').last.toLowerCase();
    final invalidDomains = ['test.com', 'example.com', 'fake.com', 'invalid.com'];
    if (invalidDomains.contains(domain)) {
      return 'Please use a valid email address';
    }

    return null;
  }

  static String? validateEmailForLogin(String? value) {
    final basicValidation = validateEmail(value);
    if (basicValidation != null) return basicValidation;

    return null;
  }

  static String? validateEmailForSignup(String? value) {
    final basicValidation = validateEmail(value);
    if (basicValidation != null) return basicValidation;

    return null;
  }

  static String? validatePassword(String? value) {
    return PasswordRequirementsWidget.validatePassword(value);
  }
  
  static String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Name is required';
    }
    
    if (value.length < 2) {
      return 'Name must be at least 2 characters long';
    }
    
    return null;
  }
  
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }
}