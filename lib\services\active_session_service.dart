import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/widgets.dart';
import 'dart:async';

class ActiveSessionService extends WidgetsBindingObserver {
  static final ActiveSessionService _instance = ActiveSessionService._internal();
  factory ActiveSessionService() => _instance;
  ActiveSessionService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  DocumentReference? _userStatusFirestoreRef;
  Timer? _heartbeatTimer;
  bool _isSessionActive = false;
  DateTime? _lastActivityTime;

  Future<void> startSession() async {
    final user = _auth.currentUser;
    if (user == null) return;

    final uid = user.uid;
    _userStatusFirestoreRef = _firestore.doc('/status/$uid');
    _isSessionActive = true;
    _lastActivityTime = DateTime.now();

    WidgetsBinding.instance.addObserver(this);

    await _setOnlineStatus(user);

    _startHeartbeat();
  }

  Future<void> _setOnlineStatus(User user) async {
    if (_userStatusFirestoreRef == null) return;

    try {
      await _userStatusFirestoreRef!.set({
        'state': 'online',
        'last_changed': FieldValue.serverTimestamp(),
        'userId': user.uid,
        'email': user.email,
        'last_activity': FieldValue.serverTimestamp(),
        'session_start': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('Error setting online status: $e');
    }
  }

  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_isSessionActive && _userStatusFirestoreRef != null) {
        _updateLastActivity();
      }
    });
  }

  Future<void> _updateLastActivity() async {
    if (_userStatusFirestoreRef == null) return;

    try {
      await _userStatusFirestoreRef!.update({
        'last_activity': FieldValue.serverTimestamp(),
      });
      _lastActivityTime = DateTime.now();
    } catch (e) {
      print('Error updating last activity: $e');
    }
  }

  Future<void> endSession() async {
    final user = _auth.currentUser;
    if (user == null) return;

    _isSessionActive = false;

    try {
      _heartbeatTimer?.cancel();

      WidgetsBinding.instance.removeObserver(this);

      if (_userStatusFirestoreRef != null) {
        await _userStatusFirestoreRef!.set({
          'state': 'offline',
          'last_changed': FieldValue.serverTimestamp(),
          'userId': user.uid,
          'email': user.email,
          'session_end': FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      print('Error ending session: $e');
    }

    _userStatusFirestoreRef = null;
    _heartbeatTimer = null;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        if (_userStatusFirestoreRef != null) {
          _isSessionActive = true;
          _updateLastActivity();
          _startHeartbeat();
        }
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
        _isSessionActive = false;
        _heartbeatTimer?.cancel();
        _setOfflineStatus();
        break;
      case AppLifecycleState.hidden:
        _isSessionActive = false;
        break;
    }
  }

  Future<void> _setOfflineStatus() async {
    if (_userStatusFirestoreRef == null) return;

    try {
      final user = _auth.currentUser;
      if (user != null) {
        await _userStatusFirestoreRef!.set({
          'state': 'offline',
          'last_changed': FieldValue.serverTimestamp(),
          'userId': user.uid,
          'email': user.email,
          'last_activity': _lastActivityTime != null
              ? Timestamp.fromDate(_lastActivityTime!)
              : FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      print('Error setting offline status: $e');
    }
  }

  static Future<int> getActiveUserCount() async {
    try {
      final firestore = FirebaseFirestore.instance;
      final now = DateTime.now();
      final staleThreshold = now.subtract(const Duration(minutes: 2));

      final onlineUsersSnapshot = await firestore
          .collection('status')
          .where('state', isEqualTo: 'online')
          .get();

      int activeCount = 0;
      for (final doc in onlineUsersSnapshot.docs) {
        final data = doc.data();
        final lastActivity = data['last_activity'];

        if (lastActivity != null) {
          DateTime lastActivityTime;
          if (lastActivity is Timestamp) {
            lastActivityTime = lastActivity.toDate();
          } else if (lastActivity is DateTime) {
            lastActivityTime = lastActivity;
          } else {
            continue;
          }

          if (lastActivityTime.isAfter(staleThreshold)) {
            activeCount++;
          }
        }
      }

      return activeCount;
    } catch (e) {
      print('Error getting active user count: $e');
      return 0;
    }
  }

  static Future<int> getTotalUserCount() async {
    try {
      final firestore = FirebaseFirestore.instance;

      final usersSnapshot = await firestore
          .collection('users')
          .get();

      return usersSnapshot.docs.length;
    } catch (e) {
      print('Error getting total user count: $e');
      return 0;
    }
  }

  static Stream<List<Map<String, dynamic>>> getOnlineUsersStream() {
    final firestore = FirebaseFirestore.instance;

    return firestore
        .collection('status')
        .where('state', isEqualTo: 'online')
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => {
              'userId': doc.id,
              ...doc.data(),
            }).toList());
  }

  void dispose() {
    _isSessionActive = false;
    _heartbeatTimer?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    _userStatusFirestoreRef = null;
    _heartbeatTimer = null;
  }

  void markUserActive() {
    if (_isSessionActive) {
      _updateLastActivity();
    }
  }

  static Future<void> cleanupStaleSessions() async {
    try {
      final firestore = FirebaseFirestore.instance;
      final now = DateTime.now();
      final staleThreshold = now.subtract(const Duration(minutes: 5));

      final onlineUsersSnapshot = await firestore
          .collection('status')
          .where('state', isEqualTo: 'online')
          .get();

      final batch = firestore.batch();
      int cleanedCount = 0;

      for (final doc in onlineUsersSnapshot.docs) {
        final data = doc.data();
        final lastActivity = data['last_activity'];

        if (lastActivity != null) {
          DateTime lastActivityTime;
          if (lastActivity is Timestamp) {
            lastActivityTime = lastActivity.toDate();
          } else if (lastActivity is DateTime) {
            lastActivityTime = lastActivity;
          } else {
            continue;
          }

          if (lastActivityTime.isBefore(staleThreshold)) {
            batch.update(doc.reference, {
              'state': 'offline',
              'last_changed': FieldValue.serverTimestamp(),
              'cleanup_reason': 'stale_session',
            });
            cleanedCount++;
          }
        }
      }

      if (cleanedCount > 0) {
        await batch.commit();
        print('Cleaned up $cleanedCount stale sessions');
      }
    } catch (e) {
      print('Error cleaning up stale sessions: $e');
    }
  }
}
