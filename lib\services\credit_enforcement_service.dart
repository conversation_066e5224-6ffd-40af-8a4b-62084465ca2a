import 'package:flutter/material.dart';
import '../models/subscription_model.dart';
import '../models/user_subscription_model.dart';
import '../widgets/subscription/credit_warning_dialog.dart';
import '../widgets/subscription/upgrade_required_dialog.dart';
import '../screens/subscription/subscription_screen.dart';

class CreditEnforcementService {
  static const double _warningThreshold = 0.8;

  static bool canAccessChat(UserSubscriptionModel? userSubscription) {
    if (userSubscription == null) return false;

    if (userSubscription.subscription.plan == SubscriptionPlan.free) {
      if (userSubscription.subscription.isExpired) {
        return false;
      }
      return userSubscription.credits.hasChatCredits;
    }

    if (!userSubscription.isSubscriptionActive) return false;

    return userSubscription.credits.hasChatCredits;
  }

  static bool canAccessVoice(UserSubscriptionModel? userSubscription) {
    if (userSubscription == null) return false;

    if (userSubscription.subscription.plan == SubscriptionPlan.free) {
      if (userSubscription.subscription.isExpired) {
        return false;
      }
      return userSubscription.credits.hasVoiceCredits;
    }

    if (!userSubscription.isSubscriptionActive) return false;

    return userSubscription.credits.hasVoiceCredits;
  }

  static bool hasFreePlanReachedLimit(UserSubscriptionModel? userSubscription) {
    if (userSubscription == null) return true;

    if (userSubscription.subscription.plan != SubscriptionPlan.free) return false;

    if (userSubscription.subscription.isExpired) {
      return true;
    }

    if (!userSubscription.credits.hasChatCredits && !userSubscription.credits.hasVoiceCredits) {
      return true;
    }

    return false;
  }

  static String getFreePlanLimitMessage(UserSubscriptionModel? userSubscription) {
    if (userSubscription == null) return 'Please upgrade to continue using the app.';

    if (userSubscription.subscription.plan != SubscriptionPlan.free) {
      return 'Please upgrade to continue using the app.';
    }

    if (userSubscription.subscription.isExpired) {
      return 'Your 7-day free trial has ended. Please upgrade to continue.';
    }

    final hasChat = userSubscription.credits.hasChatCredits;
    final hasVoice = userSubscription.credits.hasVoiceCredits;

    if (!hasChat && !hasVoice) {
      return 'You have used all your free credits. Please upgrade to continue.';
    } else if (!hasChat) {
      return 'You have used all your chat credits. You have ${userSubscription.credits.voiceRemaining} voice credits remaining.';
    } else if (!hasVoice) {
      return 'You have used all your voice credits. You have ${userSubscription.credits.chatRemaining} chat credits remaining.';
    }

    return 'Please upgrade to continue using the app.';
  }

  static Future<void> enforceFeatureAccess({
    required BuildContext context,
    required UserSubscriptionModel? userSubscription,
    required String featureType,
  }) async {
    if (userSubscription == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Loading subscription data, please try again...'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    final canAccess = featureType == 'chat'
        ? canAccessChat(userSubscription)
        : canAccessVoice(userSubscription);

    if (!canAccess) {
      if (userSubscription.subscription.plan == SubscriptionPlan.free) {
        final message = getFreePlanLimitMessage(userSubscription);

        if (userSubscription.subscription.isExpired) {
          _showCustomUpgradeDialog(
            context: context,
            title: 'Free Trial Ended',
            message: message,
            onUpgrade: () => _navigateToSubscription(context),
          );
        } else {
          UpgradeRequiredDialog.showForOutOfCredits(
            context: context,
            creditType: featureType,
            onUpgrade: () => _navigateToSubscription(context),
          );
        }
      } else if (!userSubscription.isSubscriptionActive) {
        UpgradeRequiredDialog.showForExpiredSubscription(
          context: context,
          onUpgrade: () => _navigateToSubscription(context),
        );
      } else {
        UpgradeRequiredDialog.showForOutOfCredits(
          context: context,
          creditType: featureType,
          onUpgrade: () => _navigateToSubscription(context),
        );
      }
    }
  }

  static Future<void> checkAndShowWarning({
    required BuildContext context,
    required UserSubscriptionModel? userSubscription,
  }) async {
    if (userSubscription == null) return;

    final credits = userSubscription.credits;
    
    if (credits.chatUsagePercentage >= _warningThreshold &&
        credits.chatUsagePercentage < 1.0) {
      await _showCreditWarning(
        context: context,
        title: 'Chat Credits Running Low',
        message: 'You have used ${(credits.chatUsagePercentage * 100).round()}% of your chat credits. Consider upgrading to avoid interruption.',
        onUpgrade: () => _navigateToSubscription(context),
      );
    }

    if (credits.voiceUsagePercentage >= _warningThreshold &&
        credits.voiceUsagePercentage < 1.0) {
      await _showCreditWarning(
        context: context,
        title: 'Voice Credits Running Low',
        message: 'You have used ${(credits.voiceUsagePercentage * 100).round()}% of your voice credits. Consider upgrading to avoid interruption.',
        onUpgrade: () => _navigateToSubscription(context),
      );
    }
  }

  static bool isPowerUser(UserSubscriptionModel? userSubscription) {
    if (userSubscription == null) return false;
    if (userSubscription.subscription.plan != SubscriptionPlan.basic) return false;

    final credits = userSubscription.credits;
    final daysUsed = 30 - userSubscription.daysRemaining;
    
    if (daysUsed < 7) return false;

    final dailyChatUsage = credits.chatUsed / daysUsed;
    final dailyVoiceUsage = credits.voiceUsed / daysUsed;

    const powerUserChatThreshold = 650;
    const powerUserVoiceThreshold = 4;

    return dailyChatUsage > powerUserChatThreshold || 
           dailyVoiceUsage > powerUserVoiceThreshold;
  }

  static Future<void> showPowerUserSuggestion({
    required BuildContext context,
    required UserSubscriptionModel userSubscription,
  }) async {
    if (!isPowerUser(userSubscription)) return;

    await _showCreditWarning(
      context: context,
      title: 'Premium Plan Recommended',
      message: 'Based on your usage patterns, you might benefit from our Premium plan with higher limits and better value.',
      onUpgrade: () => _navigateToSubscription(context),
    );
  }

  static Future<void> _showCreditWarning({
    required BuildContext context,
    required String title,
    required String message,
    VoidCallback? onUpgrade,
  }) async {
    if (context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => CreditWarningDialog(
          title: title,
          message: message,
          onUpgrade: onUpgrade,
        ),
      );
    }
  }

  static Future<void> _showCustomUpgradeDialog({
    required BuildContext context,
    required String title,
    required String message,
    VoidCallback? onUpgrade,
  }) async {
    if (context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Later'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onUpgrade?.call();
              },
              child: const Text('Upgrade Now'),
            ),
          ],
        ),
      );
    }
  }

  static void _navigateToSubscription(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SubscriptionScreen(),
      ),
    );
  }

  static void _showUpgradeDialog(BuildContext context) {
    UpgradeRequiredDialog.showForFreePlan(
      context: context,
      onUpgrade: () => _navigateToSubscription(context),
    );
  }

  static Future<bool> blockFeatureAccess({
    required BuildContext context,
    required UserSubscriptionModel? userSubscription,
    required String featureType,
  }) async {
    final canAccess = featureType == 'chat' 
        ? canAccessChat(userSubscription)
        : canAccessVoice(userSubscription);

    if (!canAccess) {
      await enforceFeatureAccess(
        context: context,
        userSubscription: userSubscription,
        featureType: featureType,
      );
      return false;
    }

    return true;
  }
}
