import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../providers/subscription_provider.dart';
import '../../models/subscription_model.dart';
import '../../models/payment_models.dart';
import '../../utils/subscription_constants.dart';
import '../../widgets/custom_button.dart';
import '../../services/payment_config_service.dart';
import '../../services/invoice_service.dart';
import '../../services/usage_analytics_service.dart';
import '../../services/dynamic_plan_service.dart';
import '../../utils/admin_guard.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../utils/theme.dart';

class SubscriptionScreen extends StatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  State<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen> {
  UpgradeSuggestion? _upgradeSuggestion;
  PaymentProvider? _selectedProvider;
  List<PaymentProvider> _availableProviders = [];
  final DynamicPlanService _planService = DynamicPlanService();

  // Real-time plan configurations
  Map<SubscriptionPlan, Map<String, dynamic>> _currentPlanConfigs = {};

  @override
  void initState() {
    super.initState();
    _initializeProviders();
    _initializeScreen();
  }

  Future<void> _initializeScreen() async {
    // Initialize dynamic plans first
    await _initializeDynamicPlans();

    // Then initialize the subscription provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SubscriptionProvider>().initialize();
      _checkUpgradeSuggestions();
    });
  }

  Future<void> _initializeDynamicPlans() async {
    try {
      await _planService.initialize();
      _currentPlanConfigs = _planService.getAllPlanConfigs();

      // Listen for real-time plan configuration updates
      _planService.planConfigStream.listen((updatedConfigs) {
        if (mounted) {
          setState(() {
            _currentPlanConfigs = updatedConfigs;
          });
          debugPrint('SubscriptionScreen: Received real-time plan configuration update');
        }
      });
    } catch (e) {
      debugPrint('SubscriptionScreen: Failed to initialize dynamic plans: $e');
    }
  }

  void _initializeProviders() {
    _availableProviders = PaymentConfigService.getAvailableProviders();
    if (_availableProviders.isNotEmpty) {
      _selectedProvider = PaymentConfigService.getPrimaryProvider() ?? _availableProviders.first;
    }
  }

  Future<void> _checkUpgradeSuggestions() async {
    final subscriptionProvider = context.read<SubscriptionProvider>();
    final suggestion = await subscriptionProvider.checkUpgradeSuggestions();

    if (suggestion != null && mounted) {
      setState(() {
        _upgradeSuggestion = suggestion;
      });
    }
  }

  Future<void> _handleUpgrade(SubscriptionPlan plan) async {
    final subscriptionProvider = context.read<SubscriptionProvider>();
    final userSub = subscriptionProvider.userSubscription;

    if (userSub == null) return;

    try {
      final checkoutUrl = PaymentConfigService.getCheckoutUrl(
        plan: plan,
        customerEmail: userSub.user.email,
        preferredProvider: _selectedProvider,
      );

      if (checkoutUrl == null) {
        throw Exception('No payment providers configured');
      }

      final invoice = await InvoiceService.generateInvoice(
        user: userSub.user,
        plan: plan,
        checkoutUrl: checkoutUrl,
      );

      final uri = Uri.parse(checkoutUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw Exception('Could not launch checkout URL');
      }

      await subscriptionProvider.trackActivity('upgrade_attempt', metadata: {
        'plan': plan.name,
        'invoiceId': invoice.id,
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Redirecting to payment...'),
            backgroundColor: AppTheme.primaryColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to start checkout: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8)),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Subscription Plans', style: TextStyle(fontWeight: FontWeight.w600)),
        centerTitle: true,
        elevation: 0,
      ),
      body: Consumer<SubscriptionProvider>(
        builder: (context, subscriptionProvider, child) {
          if (subscriptionProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (subscriptionProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading subscription data',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    subscriptionProvider.error!,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  CustomButton(
                    text: 'Retry',
                    onPressed: () => subscriptionProvider.refresh(),
                    icon: Icons.refresh,
                  ),
                ],
              ),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCurrentPlanCard(subscriptionProvider),
                const SizedBox(height: 24),

                if (subscriptionProvider.hasActiveSubscription)
                  _buildCreditUsageCard(subscriptionProvider),
                const SizedBox(height: 24),

                if (_upgradeSuggestion != null)
                  _buildUpgradeSuggestionCard(_upgradeSuggestion!),
                if (_upgradeSuggestion != null)
                  const SizedBox(height: 24),

                if (_availableProviders.length > 1) ...[
                  _buildProviderSelection(),
                  const SizedBox(height: 24),
                ],

                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  child: Text(
                    'Available Plans',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                ...subscriptionProvider.getAvailablePlans().map(
                  (planData) => Padding(
                    padding: const EdgeInsets.only(bottom: 20),
                    child: _buildPlanCard(
                      planData['plan'] as SubscriptionPlan,
                      planData['name'] as String? ?? '',
                      _safeToDouble(planData['price']),
                      (planData['features'] as List<dynamic>?)?.cast<String>() ?? <String>[],
                      planData['isCurrentPlan'] as bool? ?? false,
                      planData['canUpgrade'] as bool? ?? false,
                      _safeToInt(planData['chatCredits']),
                      _safeToInt(planData['voiceCredits']),
                      planData['popular'] as bool? ?? false,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

   Widget _buildCurrentPlanCard(SubscriptionProvider provider) {
    final isFreePlan = provider.currentPlan == SubscriptionPlan.free;
    
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Current Plan',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textSecondary,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'ACTIVE',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Icon(
                Icons.workspace_premium_rounded,
                color: AppTheme.primaryColor,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                provider.planName,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            provider.planPrice > 0 
                ? '\$${provider.planPrice.toStringAsFixed(0)}/month'
                : '0.0',
            style: TextStyle(
              fontSize: 18,
              color: AppTheme.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (provider.daysRemaining > 0) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.calendar_today_rounded,
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    '${provider.daysRemaining} days remaining',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCreditUsageCard(SubscriptionProvider provider) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Credit Usage',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 24),
          
          _buildUsageProgress(
            'Chat Messages',
            Icons.chat_bubble_outline_rounded,
            provider.chatCreditsUsed,
            provider.chatCreditsLimit,
            AppTheme.primaryColor,
          ),
          const SizedBox(height: 20),
          
          _buildUsageProgress(
            'Voice Messages',
            Icons.mic_outlined,
            provider.voiceCreditsUsed,
            provider.voiceCreditsLimit,
            Colors.purple,
          ),
        ],
      ),
    );
  }

  Widget _buildUsageProgress(String title, IconData icon, int used, int total, Color color) {
    final percentage = total > 0 ? used / total : 0.0;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppTheme.textSecondary,
              ),
            ),
            const Spacer(),
            Text(
              '$used / $total',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppTheme.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: percentage,
          minHeight: 8,
          borderRadius: BorderRadius.circular(10),
          backgroundColor: color.withOpacity(0.1),
          color: color,
        ),
      ],
    );
  }

  Widget _buildPlanCard(
    SubscriptionPlan plan,
    String name,
    double price,
    List<String> features,
    bool isCurrentPlan,
    bool canUpgrade,
    int chatCredits,
    int voiceCredits,
    bool isPopular,
  ) {
    final cardColor = isCurrentPlan ? AppTheme.primaryColor : Colors.white;
    final textColor = isCurrentPlan ? Colors.white : AppTheme.textPrimary;
    final secondaryTextColor = isCurrentPlan ? Colors.white.withOpacity(0.8) : AppTheme.textSecondary;
    final buttonColor = isCurrentPlan ? Colors.white : AppTheme.primaryColor;
    final buttonTextColor = isCurrentPlan ? AppTheme.primaryColor : Colors.white;

    return Container(
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          if (!isCurrentPlan)
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
        ],
        border: isCurrentPlan 
            ? null 
            : Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        children: [
          if (isPopular && !isCurrentPlan)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 6),
              decoration: BoxDecoration(
                color: Colors.orange,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Center(
                child: Text(
                  'MOST POPULAR',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1.2,
                  ),
                ),
              ),
            ),
          
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      name,
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.w700,
                        color: textColor,
                      ),
                    ),
                    Text(
                      price > 0 ? '\$$price/month' : 'Free',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.w700,
                        color: textColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                
                if (chatCredits > 0 || voiceCredits > 0)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: Row(
                      children: [
                        if (chatCredits > 0)
                          Row(
                            children: [
                              Icon(Icons.chat_bubble_outline_rounded, 
                                  size: 16, color: secondaryTextColor),
                              const SizedBox(width: 4),
                              Text(
                                '$chatCredits chats',
                                style: TextStyle(color: secondaryTextColor),
                              ),
                            ],
                          ),
                        if (voiceCredits > 0 && chatCredits > 0)
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: Container(
                              width: 4,
                              height: 4,
                              decoration: BoxDecoration(
                                color: secondaryTextColor,
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                        if (voiceCredits > 0)
                          Row(
                            children: [
                              Icon(Icons.mic_outlined, 
                                  size: 16, color: secondaryTextColor),
                              const SizedBox(width: 4),
                              Text(
                                '$voiceCredits voice',
                                style: TextStyle(color: secondaryTextColor),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                
                ...features.map((feature) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.check_circle_rounded,
                        size: 20,
                        color: isCurrentPlan
                            ? Colors.white
                            : AppTheme.primaryColor,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          feature,
                          style: TextStyle(
                            fontSize: 16,
                            color: textColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
                
                const SizedBox(height: 24),
                
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: canUpgrade && !isCurrentPlan
                        ? () => _handlePlanSelection(plan)
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: buttonColor,
                      foregroundColor: buttonTextColor,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      isCurrentPlan 
                          ? 'Current Plan' 
                          : canUpgrade ? 'Upgrade Now' : 'Not Available',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  Widget _buildUpgradeSuggestionCard(UpgradeSuggestion suggestion) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.05),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.orange.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.rocket_launch_rounded,
                color: Colors.orange,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Recommended Upgrade',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.orange.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            suggestion.reason,
            style: const TextStyle(
              fontSize: 16,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => _handleUpgrade(suggestion.suggestedPlan),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'Upgrade to ${suggestion.suggestedPlan.name}',
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handlePlanSelection(SubscriptionPlan plan) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Upgrade to ${SubscriptionConstants.getPlanName(plan)}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('You\'re about to upgrade to the ${SubscriptionConstants.getPlanName(plan)}.'),
            const SizedBox(height: 16),
            Text('Price: \$${SubscriptionConstants.getPlanPrice(plan).toStringAsFixed(0)}/month'),
            Text('Chat Credits: ${SubscriptionConstants.getChatLimit(plan)}'),
            Text('Voice Credits: ${SubscriptionConstants.getVoiceLimit(plan)}'),
            const SizedBox(height: 16),
            const Text(
              'You will be redirected to LemonSqueezy to complete your payment.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _proceedToPayment(plan);
            },
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  void _proceedToPayment(SubscriptionPlan plan) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please log in to upgrade your subscription')),
      );
      return;
    }

    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Preparing checkout...'),
            ],
          ),
        ),
      );

      final checkoutUrl = PaymentConfigService.getCheckoutUrl(
        plan: plan,
        customerEmail: user.email!,
        preferredProvider: _selectedProvider,
      );

      if (mounted) {
        Navigator.of(context).pop();
      }

      if (checkoutUrl != null) {
        final uri = Uri.parse(checkoutUrl);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
          _showPaymentInstructions(plan);
        } else {
          throw Exception('Could not launch checkout URL');
        }
      } else {
        throw Exception('No payment providers configured');
      }
    } catch (e) {
      if (mounted && Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }

      if (mounted) {
        if (e.toString().contains('No payment providers configured') ||
            e.toString().contains('not configured')) {
          _showPaymentNotConfiguredError();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Payment failed: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _showPaymentInstructions(SubscriptionPlan plan) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Complete Your Payment'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.payment, color: Colors.blue, size: 48),
            const SizedBox(height: 16),
            Text('You\'ve been redirected to complete your payment for ${SubscriptionConstants.getPlanName(plan)}.'),
            const SizedBox(height: 16),
            const Text('After completing payment, return to the app and your subscription will be activated automatically.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _checkPaymentStatus();
            },
            child: const Text('Check Payment Status'),
          ),
        ],
      ),
    );
  }

  void _checkPaymentStatus() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Checking payment status...'),
            ],
          ),
        ),
      );

      final subscriptionProvider = context.read<SubscriptionProvider>();
      await subscriptionProvider.refresh();

      if (mounted) {
        Navigator.of(context).pop();

        final userSub = subscriptionProvider.userSubscription;
        if (userSub != null && userSub.subscription.plan != SubscriptionPlan.free) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Payment successful! Welcome to ${userSub.subscription.plan.name} plan!'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Payment not found. Please complete payment or try again.'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error checking payment: $e')),
        );
      }
    }
  }

  void _showPaymentNotConfiguredError() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Payment System Unavailable'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.error_outline, color: Colors.orange, size: 48),
            SizedBox(height: 16),
            Text('Payment processing is currently unavailable. The payment system has not been configured yet.'),
            SizedBox(height: 16),
            Text('Please contact support or try again later.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
          if (AdminGuard.isCurrentUserAdmin())
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _navigateToAdminPanel();
              },
              child: const Text('Configure Payments'),
            ),
        ],
      ),
    );
  }

  void _navigateToAdminPanel() {
    Navigator.of(context).pushReplacementNamed('/admin');
  }

  Widget _buildProviderSelection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.payment, color: AppTheme.primaryColor, size: 24),
              const SizedBox(width: 12),
              const Text(
                'Choose Payment Method',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...(_availableProviders.map((provider) => _buildProviderOption(provider))),
        ],
      ),
    );
  }

  Widget _buildProviderOption(PaymentProvider provider) {
    final isSelected = _selectedProvider == provider;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedProvider = provider;
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
              width: isSelected ? 2 : 1,
            ),
            color: isSelected ? AppTheme.primaryColor.withValues(alpha: 0.05) : Colors.transparent,
          ),
          child: Row(
            children: [
              _getProviderIcon(provider),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getProviderDisplayName(provider),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: isSelected ? AppTheme.primaryColor : AppTheme.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getProviderDescription(provider),
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _getProviderIcon(PaymentProvider provider) {
    switch (provider) {
      case PaymentProvider.lemonsqueezy:
        return Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.yellow.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(Icons.shopping_cart, color: Colors.orange.shade700, size: 24),
        );
      case PaymentProvider.stripe:
        return Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(Icons.credit_card, color: Colors.blue.shade700, size: 24),
        );
      case PaymentProvider.paypal:
        return Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(Icons.account_balance_wallet, color: Colors.blue.shade800, size: 24),
        );
    }
  }

  String _getProviderDisplayName(PaymentProvider provider) {
    switch (provider) {
      case PaymentProvider.lemonsqueezy:
        return 'LemonSqueezy';
      case PaymentProvider.stripe:
        return 'Credit Card (Stripe)';
      case PaymentProvider.paypal:
        return 'PayPal';
    }
  }

  String _getProviderDescription(PaymentProvider provider) {
    switch (provider) {
      case PaymentProvider.lemonsqueezy:
        return 'Secure checkout with LemonSqueezy';
      case PaymentProvider.stripe:
        return 'Pay with credit or debit card';
      case PaymentProvider.paypal:
        return 'Pay with your PayPal account';
    }
  }

  Widget _buildCurrentSubscriptionCard(SubscriptionProvider provider) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(SubscriptionConstants.getPlanColor(provider.currentPlan)),
            Color(SubscriptionConstants.getPlanColor(provider.currentPlan)).withValues(alpha: 0.7),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Current Plan',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (provider.hasActiveSubscription)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'ACTIVE',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            provider.planName,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            provider.planPrice > 0 
                ? '\$${provider.planPrice.toStringAsFixed(0)}/month'
                : 'Free',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (provider.daysRemaining > 0) ...[
            const SizedBox(height: 8),
            Text(
              '${provider.daysRemaining} days remaining',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.9),
                fontSize: 14,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildWarningCard(String warning) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        border: Border.all(color: Colors.orange.shade200),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            color: Colors.orange.shade600,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              warning,
              style: TextStyle(
                color: Colors.orange.shade800,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for safe type conversion
  double _safeToDouble(dynamic value) {
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  int _safeToInt(dynamic value) {
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }
}