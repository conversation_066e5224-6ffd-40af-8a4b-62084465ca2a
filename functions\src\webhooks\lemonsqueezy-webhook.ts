import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import * as crypto from 'crypto';

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

interface LemonSqueezyWebhookData {
  meta: {
    event_name: string;
    custom_data?: {
      user_id?: string;
      plan?: string;
    };
  };
  data: {
    id: string;
    type: string;
    attributes: {
      store_id: number;
      customer_id: number;
      order_id: number;
      order_item_id: number;
      product_id: number;
      variant_id: number;
      product_name: string;
      variant_name: string;
      user_name: string;
      user_email: string;
      status: string;
      status_formatted: string;
      card_brand: string;
      card_last_four: string;
      pause: any;
      cancelled: boolean;
      trial_ends_at: string | null;
      billing_anchor: number;
      urls: {
        update_payment_method: string;
        customer_portal: string;
      };
      renews_at: string;
      ends_at: string | null;
      created_at: string;
      updated_at: string;
      test_mode: boolean;
    };
  };
}

// LemonSqueezy webhook secret (set in Firebase Functions config)
const WEBHOOK_SECRET = functions.config().lemonsqueezy?.webhook_secret || process.env.LEMONSQUEEZY_WEBHOOK_SECRET;

/**
 * Verify LemonSqueezy webhook signature
 */
function verifyWebhookSignature(payload: string, signature: string): boolean {
  if (!WEBHOOK_SECRET) {
    console.error('LemonSqueezy webhook secret not configured');
    return false;
  }

  const hmac = crypto.createHmac('sha256', WEBHOOK_SECRET);
  hmac.update(payload, 'utf8');
  const digest = hmac.digest('hex');
  const expectedSignature = `sha256=${digest}`;

  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignature)
  );
}

/**
 * Map LemonSqueezy variant ID to subscription plan
 */
function mapVariantToPlan(variantId: number): string {
  // These will be configured based on your LemonSqueezy setup
  const variantMapping: { [key: number]: string } = {
    // Add your actual variant IDs here
    // Example:
    // 12345: 'basic',
    // 12346: 'premium',
  };

  return variantMapping[variantId] || 'basic';
}

/**
 * Get credit limits for subscription plan
 */
function getCreditLimits(plan: string): { chatLimit: number; voiceLimit: number } {
  switch (plan) {
    case 'basic':
      return { chatLimit: 13000, voiceLimit: 80 };
    case 'premium':
      return { chatLimit: 20000, voiceLimit: 300 };
    default:
      return { chatLimit: 25, voiceLimit: 5 }; // free plan
  }
}

/**
 * Handle subscription created event
 */
async function handleSubscriptionCreated(data: LemonSqueezyWebhookData): Promise<void> {
  const { attributes, id } = data.data;
  const userId = data.meta.custom_data?.user_id;

  if (!userId) {
    console.error('No user_id found in webhook custom_data');
    return;
  }

  const plan = mapVariantToPlan(attributes.variant_id);
  const { chatLimit, voiceLimit } = getCreditLimits(plan);
  const now = new Date();
  const endDate = new Date(attributes.renews_at);

  try {
    // Update user subscription in Firestore
    await db.collection('users').doc(userId).update({
      subscription: {
        plan: plan,
        status: 'active',
        startDate: admin.firestore.Timestamp.fromDate(now),
        endDate: admin.firestore.Timestamp.fromDate(endDate),
        autoRenew: true,
        trialUsed: false,
        paymentPlatform: 'lemonsqueezy',
        subscriptionId: id,
        customerId: attributes.customer_id.toString(),
      },
      credits: {
        chat: {
          limit: chatLimit,
          used: 0,
          remaining: chatLimit,
        },
        voice: {
          limit: voiceLimit,
          used: 0,
          remaining: voiceLimit,
        },
        resetDate: admin.firestore.Timestamp.fromDate(endDate),
      },
      payment: {
        lemonsqueezy: {
          customerId: attributes.customer_id.toString(),
          subscriptionId: id,
        },
      },
    });

    // Log the successful upgrade
    await db.collection('payment_transactions').add({
      userId: userId,
      type: 'subscription_created',
      plan: plan,
      amount: 0, // LemonSqueezy doesn't provide amount in subscription webhook
      currency: 'USD',
      paymentPlatform: 'lemonsqueezy',
      subscriptionId: id,
      customerId: attributes.customer_id.toString(),
      status: 'completed',
      createdAt: admin.firestore.Timestamp.fromDate(now),
      metadata: {
        variantId: attributes.variant_id,
        productId: attributes.product_id,
        userEmail: attributes.user_email,
      },
    });

    console.log(`Subscription created for user ${userId}, plan: ${plan}`);
  } catch (error) {
    console.error('Error handling subscription created:', error);
    throw error;
  }
}

/**
 * Handle subscription updated event
 */
async function handleSubscriptionUpdated(data: LemonSqueezyWebhookData): Promise<void> {
  const { attributes } = data.data;
  const userId = data.meta.custom_data?.user_id;

  if (!userId) {
    console.error('No user_id found in webhook custom_data');
    return;
  }

  // const plan = mapVariantToPlan(attributes.variant_id); // Currently unused
  const status = attributes.cancelled ? 'cancelled' : 'active';
  const endDate = new Date(attributes.ends_at || attributes.renews_at);

  try {
    // Update user subscription status
    await db.collection('users').doc(userId).update({
      'subscription.status': status,
      'subscription.endDate': admin.firestore.Timestamp.fromDate(endDate),
      'subscription.autoRenew': !attributes.cancelled,
    });

    console.log(`Subscription updated for user ${userId}, status: ${status}`);
  } catch (error) {
    console.error('Error handling subscription updated:', error);
    throw error;
  }
}

/**
 * Handle subscription cancelled event
 */
async function handleSubscriptionCancelled(data: LemonSqueezyWebhookData): Promise<void> {
  const userId = data.meta.custom_data?.user_id;

  if (!userId) {
    console.error('No user_id found in webhook custom_data');
    return;
  }

  try {
    // Update subscription status to cancelled
    await db.collection('users').doc(userId).update({
      'subscription.status': 'cancelled',
      'subscription.autoRenew': false,
    });

    console.log(`Subscription cancelled for user ${userId}`);
  } catch (error) {
    console.error('Error handling subscription cancelled:', error);
    throw error;
  }
}

/**
 * Main LemonSqueezy webhook handler
 */
export const lemonsqueezyWebhook = functions.https.onRequest(async (req, res) => {
  // Only allow POST requests
  if (req.method !== 'POST') {
    res.status(405).send('Method Not Allowed');
    return;
  }

  try {
    const signature = req.get('X-Signature') || '';
    const payload = JSON.stringify(req.body);

    // Verify webhook signature
    if (!verifyWebhookSignature(payload, signature)) {
      console.error('Invalid webhook signature');
      res.status(401).send('Unauthorized');
      return;
    }

    const webhookData: LemonSqueezyWebhookData = req.body;
    const eventName = webhookData.meta.event_name;

    console.log(`Received LemonSqueezy webhook: ${eventName}`);

    // Handle different webhook events
    switch (eventName) {
      case 'subscription_created':
        await handleSubscriptionCreated(webhookData);
        break;
      
      case 'subscription_updated':
        await handleSubscriptionUpdated(webhookData);
        break;
      
      case 'subscription_cancelled':
        await handleSubscriptionCancelled(webhookData);
        break;
      
      case 'subscription_resumed':
        await handleSubscriptionUpdated(webhookData); // Same logic as updated
        break;
      
      case 'subscription_expired':
        await handleSubscriptionCancelled(webhookData); // Same logic as cancelled
        break;
      
      default:
        console.log(`Unhandled webhook event: ${eventName}`);
    }

    res.status(200).send('OK');
  } catch (error) {
    console.error('Error processing LemonSqueezy webhook:', error);
    res.status(500).send('Internal Server Error');
  }
});
