import 'package:equatable/equatable.dart';

class ApiResponse<T> extends Equatable {
  final bool success;
  final T? data;
  final String? error;
  final int? statusCode;

  const ApiResponse({
    required this.success,
    this.data,
    this.error,
    this.statusCode,
  });

  factory ApiResponse.success(T data) {
    return ApiResponse(
      success: true,
      data: data,
    );
  }

  factory ApiResponse.error(String error, {int? statusCode}) {
    return ApiResponse(
      success: false,
      error: error,
      statusCode: statusCode,
    );
  }

  @override
  List<Object?> get props => [success, data, error, statusCode];

  /// Returns the error message if available
  String? get errorMessage => error;
}

class ChatResponse extends Equatable {
  final String message;
  final String conversationId;
  final int tokensUsed;

  const ChatResponse({
    required this.message,
    required this.conversationId,
    required this.tokensUsed,
  });

  factory ChatResponse.fromMap(Map<String, dynamic> map) {
    return ChatResponse(
      message: map['message'] ?? '',
      conversationId: map['conversationId'] ?? '',
      tokensUsed: map['tokensUsed'] ?? 0,
    );
  }

  @override
  List<Object?> get props => [message, conversationId, tokensUsed];
}

class VoiceResponse extends Equatable {
  final String message;
  final String audioFilePath;
  final String conversationId;
  final int tokensUsed;
  final int characters;

  const VoiceResponse({
    required this.message,
    required this.audioFilePath,
    required this.conversationId,
    required this.tokensUsed,
    required this.characters,
  });

  factory VoiceResponse.fromMap(Map<String, dynamic> map) {
    return VoiceResponse(
      message: map['message'] ?? '',
      audioFilePath: map['audioFilePath'] ?? '',
      conversationId: map['conversationId'] ?? '',
      tokensUsed: map['tokensUsed'] ?? 0,
      characters: map['characters'] ?? 0,
    );
  }

  @override
  List<Object?> get props => [message, audioFilePath, conversationId, tokensUsed, characters];
}
