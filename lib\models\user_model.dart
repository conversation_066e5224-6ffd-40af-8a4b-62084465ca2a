import 'package:equatable/equatable.dart';

class UserModel extends Equatable {
  final String id;
  final String email;
  final String name;
  final DateTime createdAt;
  final DateTime lastActive;
  final Map<String, dynamic>? preferences;
  final String? profilePictureUrl;

  const UserModel({
    required this.id,
    required this.email,
    required this.name,
    required this.createdAt,
    required this.lastActive,
    this.preferences,
    this.profilePictureUrl,
  });

  factory UserModel.fromMap(Map<String, dynamic> map) {
    try {
      return UserModel(
        id: _getStringValue(map, 'id'),
        email: _getStringValue(map, 'email'),
        name: _getStringValue(map, 'name'),
        createdAt: _getDateTimeValue(map, 'createdAt'),
        lastActive: _getDateTimeValue(map, 'lastActive'),
        preferences: map['preferences'] as Map<String, dynamic>?,
        profilePictureUrl: map['profilePictureUrl'] as String?,
      );
    } catch (e) {
      print('Error creating UserModel from map: $e');
      print('Map data: $map');
      rethrow;
    }
  }

  static String _getStringValue(Map<String, dynamic> map, String key) {
    final value = map[key];
    if (value == null) return '';
    if (value is String) return value;
    return value.toString();
  }

  static DateTime _getDateTimeValue(Map<String, dynamic> map, String key) {
    final value = map[key];
    if (value == null) return DateTime.now();
    
    if (value is int) {
      return DateTime.fromMillisecondsSinceEpoch(value);
    }
    
    if (value is DateTime) {
      return value;
    }
    
    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        print('Failed to parse DateTime from string: $value');
        return DateTime.now();
      }
    }
    
    return DateTime.now();
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastActive': lastActive.millisecondsSinceEpoch,
      'preferences': preferences,
      'profilePictureUrl': profilePictureUrl,
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? name,
    DateTime? createdAt,
    DateTime? lastActive,
    Map<String, dynamic>? preferences,
    String? profilePictureUrl,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      createdAt: createdAt ?? this.createdAt,
      lastActive: lastActive ?? this.lastActive,
      preferences: preferences ?? this.preferences,
      profilePictureUrl: profilePictureUrl ?? this.profilePictureUrl,
    );
  }

  @override
  List<Object?> get props => [id, email, name, createdAt, lastActive, preferences, profilePictureUrl];

  @override
    String toString() {
      return 'UserModel(id: $id, email: $email, name: $name, createdAt: $createdAt, lastActive: $lastActive, preferences: $preferences)';
    }
  }