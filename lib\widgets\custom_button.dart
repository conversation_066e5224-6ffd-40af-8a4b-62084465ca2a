import 'dart:async';
import 'package:flutter/material.dart';
import '../utils/theme.dart';


enum ButtonType { primary, secondary, outline, text }


enum ErrorType { validation, network, authentication, general }

// Purpose: Create styled snack bars with different error types and retry functionality
class EnhancedSnackBar {
  // Purpose: Create customized snack bar with error type styling
  static SnackBar create({
    required String message,
    ErrorType type = ErrorType.general,
    VoidCallback? onRetry,
    Duration duration = const Duration(seconds: 4),
  }) {
    Color backgroundColor;
    IconData icon;

    switch (type) {
      case ErrorType.validation:
        backgroundColor = AppTheme.errorColor;
        icon = Icons.error_outline;
        break;
      case ErrorType.network:
        backgroundColor = Colors.orange;
        icon = Icons.wifi_off;
        break;
      case ErrorType.authentication:
        backgroundColor = Colors.red.shade700;
        icon = Icons.security;
        break;
      case ErrorType.general:
        backgroundColor = AppTheme.errorColor;
        icon = Icons.warning_amber;
        break;
    }

    return SnackBar(
      content: Row(
        children: [
          Icon(icon, color: Colors.white, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (onRetry != null) ...[
            const SizedBox(width: 8),
            TextButton(
              onPressed: onRetry,
              style: TextButton.styleFrom(
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 8),
              ),
              child: const Text(
                'Retry',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ],
      ),
      backgroundColor: backgroundColor,
      duration: duration,
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      margin: const EdgeInsets.all(16),
    );
  }
}


Future<void> showSessionTimeoutDialog({
  required BuildContext context,
  required VoidCallback onExtendSession,
  required VoidCallback onLogout,
  int warningTimeMinutes = 5,
}) {
  return showDialog<void>(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return _SessionTimeoutDialog(
        onExtendSession: onExtendSession,
        onLogout: onLogout,
        warningTimeMinutes: warningTimeMinutes,
      );
    },
  );
}

class _SessionTimeoutDialog extends StatefulWidget {
  final VoidCallback onExtendSession;
  final VoidCallback onLogout;
  final int warningTimeMinutes;

  const _SessionTimeoutDialog({
    required this.onExtendSession,
    required this.onLogout,
    this.warningTimeMinutes = 5,
  });

  @override
  State<_SessionTimeoutDialog> createState() => _SessionTimeoutDialogState();
}

class _SessionTimeoutDialogState extends State<_SessionTimeoutDialog> {
  Timer? _countdownTimer;
  int _remainingSeconds = 300;

  @override
  void initState() {
    super.initState();
    _remainingSeconds = widget.warningTimeMinutes * 60;
    _startCountdown();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _startCountdown() {
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _remainingSeconds--;
        });

        if (_remainingSeconds <= 0) {
          timer.cancel();
          widget.onLogout();
        }
      } else {
        timer.cancel();
      }
    });
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.access_time, color: Colors.orange, size: 28),
            const SizedBox(width: 12),
            Text('Session Expiring',
                 style: TextStyle(color: AppTheme.textPrimary, fontWeight: FontWeight.bold)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Column(
                children: [
                  Icon(Icons.timer, color: Colors.orange, size: 48),
                  const SizedBox(height: 12),
                  Text('Your session will expire in:',
                       style: TextStyle(color: AppTheme.textSecondary, fontSize: 14),
                       textAlign: TextAlign.center),
                  const SizedBox(height: 8),
                  Text(_formatTime(_remainingSeconds),
                       style: const TextStyle(color: Colors.orange, fontSize: 32,
                                            fontWeight: FontWeight.bold, fontFamily: 'monospace')),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Text('For your security, you will be automatically logged out due to inactivity. Would you like to extend your session?',
                 style: TextStyle(color: AppTheme.textSecondary, fontSize: 14, height: 1.4),
                 textAlign: TextAlign.center),
          ],
        ),
        actions: [
          Row(
            children: [
              Expanded(
                child: CustomButton(text: 'Logout', onPressed: () {
                  _countdownTimer?.cancel();
                  Navigator.of(context).pop();
                  widget.onLogout();
                }, type: ButtonType.outline, height: 44),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: CustomButton(text: 'Stay Logged In', onPressed: () {
                  _countdownTimer?.cancel();
                  Navigator.of(context).pop();
                  widget.onExtendSession();
                }, height: 44),
              ),
            ],
          ),
        ],
        actionsPadding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      ),
    );
  }
}


// Purpose: Reusable button widget with multiple styles and loading states
class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final bool isLoading;
  final IconData? icon;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.isLoading = false,
    this.icon,
    this.width,
    this.height,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? double.infinity,
      height: height ?? 56,
      child: _buildButton(context),
    );
  }

  Widget _buildButton(BuildContext context) {
    switch (type) {
      case ButtonType.primary:
        return ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
            padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            elevation: 3,
          ),
          child: _buildButtonContent(),
        );

      case ButtonType.secondary:
        return ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.secondaryColor,
            foregroundColor: Colors.white,
            padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            elevation: 3,
          ),
          child: _buildButtonContent(),
        );

      case ButtonType.outline:
        return OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: OutlinedButton.styleFrom(
            foregroundColor: AppTheme.primaryColor,
            side: const BorderSide(color: AppTheme.primaryColor, width: 2),
            padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
          ),
          child: _buildButtonContent(),
        );

      case ButtonType.text:
        return TextButton(
          onPressed: isLoading ? null : onPressed,
          style: TextButton.styleFrom(
            foregroundColor: AppTheme.primaryColor,
            padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
          ),
          child: _buildButtonContent(),
        );
    }
  }

  Widget _buildButtonContent() {
    if (isLoading) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 20,
            width: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                type == ButtonType.outline || type == ButtonType.text
                    ? AppTheme.primaryColor
                    : Colors.white
              ),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            'Loading...',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: type == ButtonType.outline || type == ButtonType.text
                  ? AppTheme.primaryColor
                  : Colors.white,
            ),
          ),
        ],
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 20),
          const SizedBox(width: 8),
          Text(
            text,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      );
    }

    return Text(
      text,
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
      ),
    );
  }
}