import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../models/subscription_model.dart';
import '../../services/dynamic_plan_service.dart';
import '../../services/payment_manager.dart';
import '../../utils/constants.dart';

class PlanManagementScreen extends StatefulWidget {
  const PlanManagementScreen({super.key});

  @override
  State<PlanManagementScreen> createState() => _PlanManagementScreenState();
}

class _PlanManagementScreenState extends State<PlanManagementScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final DynamicPlanService _planService = DynamicPlanService();
  final PaymentManager _paymentManager = PaymentManager.instance;
  
  bool _isLoading = false;
  bool _isSaving = false;
  String? _lastUpdateStatus;
  
  // Controllers for each plan
  final Map<SubscriptionPlan, Map<String, TextEditingController>> _controllers = {};
  final Map<SubscriptionPlan, Map<String, dynamic>> _planData = {};

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadCurrentConfigurations();
  }

  void _initializeControllers() {
    for (final plan in SubscriptionPlan.values) {
      _controllers[plan] = {
        'name': TextEditingController(),
        'price': TextEditingController(),
        'chatCredits': TextEditingController(),
        'voiceCredits': TextEditingController(),
        'durationDays': TextEditingController(),
        'description': TextEditingController(),
      };
      
      _planData[plan] = {
        'features': <String>[],
        'popular': false,
        'available': true,
        'color': 0xFF2196F3,
      };
    }
  }

  Future<void> _loadCurrentConfigurations() async {
    setState(() => _isLoading = true);
    
    try {
      await _planService.initialize();
      
      for (final plan in SubscriptionPlan.values) {
        final config = _planService.getPlanConfig(plan);
        
        _controllers[plan]!['name']!.text = config['name'] ?? '';
        _controllers[plan]!['price']!.text = (config['price'] ?? 0.0).toString();
        _controllers[plan]!['chatCredits']!.text = (config['chatCredits'] ?? 0).toString();
        _controllers[plan]!['voiceCredits']!.text = (config['voiceCredits'] ?? 0).toString();
        _controllers[plan]!['durationDays']!.text = (config['durationDays'] ?? 0).toString();
        _controllers[plan]!['description']!.text = config['description'] ?? '';
        
        _planData[plan] = {
          'features': List<String>.from(config['features'] ?? []),
          'popular': config['popular'] ?? false,
          'available': config['available'] ?? true,
          'color': config['color'] ?? 0xFF2196F3,
        };
      }
    } catch (e) {
      _showError('Failed to load configurations: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveConfigurations() async {
    setState(() => _isSaving = true);

    try {
      // Get current configurations for comparison
      final oldConfigs = _planService.getAllPlanConfigs();

      final configData = <String, dynamic>{};

      // Build configuration data
      for (final plan in SubscriptionPlan.values) {
        configData[plan.name] = {
          'id': plan.name,
          'name': _controllers[plan]!['name']!.text,
          'price': double.tryParse(_controllers[plan]!['price']!.text) ?? 0.0,
          'chatCredits': int.tryParse(_controllers[plan]!['chatCredits']!.text) ?? 0,
          'voiceCredits': int.tryParse(_controllers[plan]!['voiceCredits']!.text) ?? 0,
          'durationDays': int.tryParse(_controllers[plan]!['durationDays']!.text) ?? 0,
          'description': _controllers[plan]!['description']!.text,
          'features': _planData[plan]!['features'],
          'popular': _planData[plan]!['popular'],
          'available': _planData[plan]!['available'],
          'color': _planData[plan]!['color'],
          'lastUpdated': FieldValue.serverTimestamp(),
          'updatedBy': 'admin', // TODO: Get actual admin user ID
        };
      }

      // Save to Firestore (this will trigger real-time updates)
      await _firestore
          .collection(AppConstants.appConfigCollection)
          .doc('plan_configuration')
          .set(configData, SetOptions(merge: true));

      // Update payment providers with new pricing
      await _updatePaymentProviders();

      // Update existing user subscriptions with new plan benefits
      await _updateExistingUserSubscriptions(oldConfigs);

      setState(() {
        _lastUpdateStatus = 'Successfully updated all plan configurations and user subscriptions at ${DateTime.now().toString().substring(11, 19)}';
      });

      _showSuccess('Plan configurations and user subscriptions updated successfully!');

    } catch (e) {
      _showError('Failed to save configurations: $e');
      setState(() {
        _lastUpdateStatus = 'Update failed: $e';
      });
    } finally {
      setState(() => _isSaving = false);
    }
  }

  Future<void> _updateExistingUserSubscriptions(Map<SubscriptionPlan, Map<String, dynamic>> oldConfigs) async {
    try {
      await _planService.updateExistingUserSubscriptions(oldConfigs);
      debugPrint('Successfully updated existing user subscriptions');
    } catch (e) {
      debugPrint('Failed to update existing user subscriptions: $e');
      // Don't throw here as the plan update was successful
    }
  }

  Future<void> _updatePaymentProviders() async {
    try {
      // Reinitialize payment manager with new configurations
      await _paymentManager.reinitializeWithUpdatedPlans();
      debugPrint('Payment providers updated with new plan configurations');
    } catch (e) {
      debugPrint('Failed to update payment providers: $e');
      // Don't throw here as the plan update was successful
    }
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Plan Management'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          if (_lastUpdateStatus != null)
            IconButton(
              icon: const Icon(Icons.info),
              onPressed: () => _showUpdateStatus(),
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadCurrentConfigurations,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(),
                  const SizedBox(height: 20),
                  ...SubscriptionPlan.values.map((plan) => _buildPlanCard(plan)),
                  const SizedBox(height: 20),
                  _buildSaveButton(),
                ],
              ),
            ),
    );
  }

  Widget _buildHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Real-time Plan Configuration',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'Changes made here will immediately propagate to all users and payment providers.',
              style: TextStyle(color: Colors.grey),
            ),
            if (_lastUpdateStatus != null) ...[
              const SizedBox(height: 8),
              Text(
                _lastUpdateStatus!,
                style: TextStyle(
                  color: _lastUpdateStatus!.contains('failed') ? Colors.red : Colors.green,
                  fontSize: 12,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPlanCard(SubscriptionPlan plan) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  plan.name.toUpperCase(),
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Switch(
                  value: _planData[plan]!['available'],
                  onChanged: (value) {
                    setState(() {
                      _planData[plan]!['available'] = value;
                    });
                  },
                ),
                const Text('Available'),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _controllers[plan]!['name'],
                    decoration: const InputDecoration(
                      labelText: 'Plan Name',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _controllers[plan]!['price'],
                    decoration: const InputDecoration(
                      labelText: 'Price (\$)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _controllers[plan]!['chatCredits'],
                    decoration: const InputDecoration(
                      labelText: 'Chat Credits',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _controllers[plan]!['voiceCredits'],
                    decoration: const InputDecoration(
                      labelText: 'Voice Credits',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _controllers[plan]!['durationDays'],
                    decoration: const InputDecoration(
                      labelText: 'Duration (Days)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _controllers[plan]!['description'],
              decoration: const InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isSaving ? null : _saveConfigurations,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: _isSaving
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(color: Colors.white),
                  ),
                  SizedBox(width: 8),
                  Text('Updating...'),
                ],
              )
            : const Text('Save All Changes'),
      ),
    );
  }

  void _showUpdateStatus() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Last Update Status'),
        content: Text(_lastUpdateStatus ?? 'No updates yet'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    for (final planControllers in _controllers.values) {
      for (final controller in planControllers.values) {
        controller.dispose();
      }
    }
    super.dispose();
  }
}
