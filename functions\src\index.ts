import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import express from 'express';
import cors from 'cors';

// Import webhook handlers
import { stripeWebhook } from './webhooks/stripe-webhook';
import { paypalWebhook } from './webhooks/paypal-webhook';


// Initialize Firebase Admin
admin.initializeApp();

// Initialize Express app
const app = express();
app.use(cors({ origin: true }));
app.use(express.json());

// Subscription plan configurations
export const SUBSCRIPTION_PLANS = {
  free: {
    id: 'free',
    name: 'Free Plan',
    price: 0,
    credits: { chat: 25, voice: 5 },
    duration: 7, // days
  },
  basic: {
    id: 'basic',
    name: 'Basic Plan',
    price: 15,
    credits: { chat: 13000, voice: 80 },
    duration: 30, // days
  },
  premium: {
    id: 'premium',
    name: 'Premium Plan',
    price: 30,
    credits: { chat: 20000, voice: 300 }, // Transparent limits
    duration: 30, // days
  },
};

// Helper function to verify Firebase Auth token (currently unused but kept for future use)
// const verifyAuth = async (req: any, res: any, next: any) => {
//   try {
//     const token = req.headers.authorization?.split('Bearer ')[1];
//     if (!token) {
//       return res.status(401).json({ error: 'No authorization token provided' });
//     }

//     const decodedToken = await admin.auth().verifyIdToken(token);
//     req.user = decodedToken;
//     next();
//   } catch (error) {
//     console.error('Auth verification error:', error);
//     return res.status(401).json({ error: 'Invalid authorization token' });
//   }
// };

// Helper function to get user subscription data
const getUserSubscription = async (userId: string) => {
  const userDoc = await admin.firestore().collection('users').doc(userId).get();
  if (!userDoc.exists) {
    throw new Error('User not found');
  }
  return userDoc.data();
};

// Helper function to update user credits
const updateUserCredits = async (userId: string, chatUsed: number, voiceUsed: number) => {
  const userRef = admin.firestore().collection('users').doc(userId);
  
  await userRef.update({
    'credits.chat.used': admin.firestore.FieldValue.increment(chatUsed),
    'credits.voice.used': admin.firestore.FieldValue.increment(voiceUsed),
    'usage.totalChatPrompts': admin.firestore.FieldValue.increment(chatUsed),
    'usage.totalVoicePrompts': admin.firestore.FieldValue.increment(voiceUsed),
    'usage.lastChatAt': chatUsed > 0 ? admin.firestore.FieldValue.serverTimestamp() : null,
    'usage.lastVoiceAt': voiceUsed > 0 ? admin.firestore.FieldValue.serverTimestamp() : null,
  });
};

// Webhook routes - these will be handled by separate webhook functions
app.post('/stripe-webhook', (req: express.Request, res: express.Response) => {
  res.status(200).send('Use the stripeWebhook Cloud Function instead');
});

app.post('/paypal-webhook', (req: express.Request, res: express.Response) => {
  res.status(200).send('Use the paypalWebhook Cloud Function instead');
});

// LemonSqueezy Webhook Handler
app.post('/lemonsqueezy-webhook', async (req: express.Request, res: express.Response) => {
  try {
    console.log('LemonSqueezy webhook received:', req.body);

    // Verify webhook signature (implement when LemonSqueezy is configured)
    // const signature = req.headers['x-signature'];
    // if (!verifyLemonSqueezySignature(req.body, signature)) {
    //   return res.status(401).json({ error: 'Invalid signature' });
    // }

    const { event_name, data } = req.body;

    switch (event_name) {
      case 'subscription_created':
        await handleSubscriptionCreated(data);
        break;
      case 'subscription_updated':
        await handleSubscriptionUpdated(data);
        break;
      case 'subscription_cancelled':
        await handleSubscriptionCancelled(data);
        break;
      case 'subscription_resumed':
        await handleSubscriptionResumed(data);
        break;
      default:
        console.log('Unhandled webhook event:', event_name);
    }

    res.status(200).json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

// Handle subscription created
const handleSubscriptionCreated = async (data: any) => {
  const { customer_id, variant_id } = data.attributes;
  
  // Map variant_id to subscription plan
  const plan = mapVariantToPlan(variant_id);
  if (!plan) {
    throw new Error('Unknown subscription variant');
  }

  // Find user by customer_id (you'll need to store this mapping)
  const userId = await findUserByCustomerId(customer_id);
  if (!userId) {
    throw new Error('User not found for customer_id');
  }

  // Update user subscription
  const planConfig = SUBSCRIPTION_PLANS[plan as keyof typeof SUBSCRIPTION_PLANS];
  const now = new Date();
  const endDate = new Date(now.getTime() + planConfig.duration * 24 * 60 * 60 * 1000);

  await admin.firestore().collection('users').doc(userId).update({
    'subscription.plan': plan,
    'subscription.status': 'active',
    'subscription.startDate': admin.firestore.Timestamp.fromDate(now),
    'subscription.endDate': admin.firestore.Timestamp.fromDate(endDate),
    'subscription.subscriptionId': data.id,
    'subscription.paymentPlatform': 'lemonsqueezy',
    'credits.chat.limit': planConfig.credits.chat,
    'credits.chat.used': 0,
    'credits.voice.limit': planConfig.credits.voice,
    'credits.voice.used': 0,
    'credits.resetDate': admin.firestore.Timestamp.fromDate(endDate),
    'payment.lemonsqueezy.customerId': customer_id,
    'payment.lemonsqueezy.subscriptionId': data.id,
  });

  console.log(`Subscription created for user ${userId}: ${plan}`);
};

// Handle subscription updated
const handleSubscriptionUpdated = async (data: any) => {
  // Implementation for subscription updates
  console.log('Subscription updated:', data);
};

// Handle subscription cancelled
const handleSubscriptionCancelled = async (data: any) => {
  const userId = await findUserBySubscriptionId(data.id);
  if (!userId) return;

  await admin.firestore().collection('users').doc(userId).update({
    'subscription.status': 'cancelled',
    'subscription.autoRenew': false,
  });

  console.log(`Subscription cancelled for user ${userId}`);
};

// Handle subscription resumed
const handleSubscriptionResumed = async (data: any) => {
  const userId = await findUserBySubscriptionId(data.id);
  if (!userId) return;

  await admin.firestore().collection('users').doc(userId).update({
    'subscription.status': 'active',
    'subscription.autoRenew': true,
  });

  console.log(`Subscription resumed for user ${userId}`);
};

// Helper functions (to be implemented)
const mapVariantToPlan = (variantId: string): string | null => {
  // Map LemonSqueezy variant IDs to plan names
  const variantMap: { [key: string]: string } = {
    // 'variant_123': 'basic',
    // 'variant_456': 'premium',
  };
  return variantMap[variantId] || null;
};

const findUserByCustomerId = async (customerId: string): Promise<string | null> => {
  const query = await admin.firestore()
    .collection('users')
    .where('payment.lemonsqueezy.customerId', '==', customerId)
    .limit(1)
    .get();
  
  return query.empty ? null : query.docs[0].id;
};

const findUserBySubscriptionId = async (subscriptionId: string): Promise<string | null> => {
  const query = await admin.firestore()
    .collection('users')
    .where('payment.lemonsqueezy.subscriptionId', '==', subscriptionId)
    .limit(1)
    .get();
  
  return query.empty ? null : query.docs[0].id;
};

// Export the webhook handler
export const lemonsqueezyWebhook = functions.https.onRequest(app);

// Chat API with credit deduction
export const chatAPI = functions.https.onCall(async (data: any, context: any) => {
  // Verify authentication
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const userId = context.auth.uid;
  const { message, conversationHistory } = data;

  try {
    // Get user subscription
    const userData = await getUserSubscription(userId);
    const chatRemaining = userData?.credits?.chat?.remaining || 0;

    // Check if user has credits
    if (chatRemaining <= 0) {
      throw new functions.https.HttpsError('resource-exhausted', 'No chat credits remaining');
    }

    // Check if subscription is active
    const subscriptionEndDate = userData?.subscription?.endDate?.toDate();
    if (!subscriptionEndDate || new Date() > subscriptionEndDate) {
      throw new functions.https.HttpsError('permission-denied', 'Subscription expired');
    }

    // Process chat request with OpenAI (implement OpenAI integration)
    const response = await processOpenAIRequest(message, conversationHistory);

    // Deduct credit
    await updateUserCredits(userId, 1, 0);

    // Log usage
    await admin.firestore().collection('usage_logs').add({
      userId,
      type: 'chat',
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      creditsUsed: 1,
      plan: userData?.subscription?.plan || 'free',
      success: true,
    });

    return { response, creditsRemaining: chatRemaining - 1 };
  } catch (error) {
    console.error('Chat API error:', error);
    
    // Log error
    await admin.firestore().collection('usage_logs').add({
      userId,
      type: 'chat',
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      creditsUsed: 0,
      plan: 'unknown',
      success: false,
      error: error instanceof Error ? error.message : String(error),
    });

    throw error;
  }
});

// Voice API with credit deduction
export const voiceAPI = functions.https.onCall(async (data: any, context: any) => {
  // Similar implementation to chatAPI but for voice processing
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const userId = context.auth.uid;
  const { text } = data;

  try {
    const userData = await getUserSubscription(userId);
    const voiceRemaining = userData?.credits?.voice?.remaining || 0;

    if (voiceRemaining <= 0) {
      throw new functions.https.HttpsError('resource-exhausted', 'No voice credits remaining');
    }

    // Process voice request with ElevenLabs (implement ElevenLabs integration)
    const audioUrl = await processElevenLabsRequest(text);

    // Deduct credit
    await updateUserCredits(userId, 0, 1);

    return { audioUrl, creditsRemaining: voiceRemaining - 1 };
  } catch (error) {
    console.error('Voice API error:', error);
    throw error;
  }
});

// Placeholder functions (to be implemented)
const processOpenAIRequest = async (message: string, history: any[]): Promise<string> => {
  // Implement OpenAI API call
  return 'Mock response from OpenAI';
};

const processElevenLabsRequest = async (text: string): Promise<string> => {
  // Implement ElevenLabs API call
  return 'https://mock-audio-url.com/audio.mp3';
};

// Admin function to delete user from Firebase Auth
export const deleteUserFromAuth = functions.https.onCall(async (data: any, context: any) => {
  // Verify authentication
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { userId, adminEmail } = data;

  if (!userId) {
    throw new functions.https.HttpsError('invalid-argument', 'userId is required');
  }

  try {
    // Verify admin access by checking if the calling user is an admin
    const callerUid = context.auth.uid;
    const callerDoc = await admin.firestore().collection('users').doc(callerUid).get();

    if (!callerDoc.exists) {
      throw new functions.https.HttpsError('permission-denied', 'Caller user document not found');
    }

    const callerData = callerDoc.data();
    const isAdmin = callerData?.isAdmin === true || callerData?.role === 'admin';

    if (!isAdmin) {
      throw new functions.https.HttpsError('permission-denied', 'Only admins can delete users');
    }

    // Get user data before deletion for logging
    let userEmail = 'unknown';
    try {
      const userRecord = await admin.auth().getUser(userId);
      userEmail = userRecord.email || 'unknown';
    } catch (error) {
      console.log(`User ${userId} not found in Firebase Auth, may have been already deleted`);
    }

    // Delete user from Firebase Authentication
    try {
      await admin.auth().deleteUser(userId);
      console.log(`Successfully deleted user ${userId} (${userEmail}) from Firebase Auth`);
    } catch (error) {
      console.log(`Error deleting user ${userId} from Firebase Auth:`, error);
      // Don't throw error if user doesn't exist in Auth
      if ((error as any).code !== 'auth/user-not-found') {
        throw error;
      }
    }

    // Log the admin action
    await admin.firestore().collection('admin_logs').add({
      action: 'delete_user_from_auth',
      description: `Deleted user ${userId} (${userEmail}) from Firebase Authentication`,
      userId: userId,
      userEmail: userEmail,
      deletedBy: adminEmail || context.auth?.token?.email || 'unknown',
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message: `User ${userId} (${userEmail}) deleted from Firebase Auth successfully`
    };

  } catch (error) {
    console.error('Error in deleteUserFromAuth:', error);

    // Log the failed attempt
    await admin.firestore().collection('admin_logs').add({
      action: 'delete_user_from_auth_failed',
      description: `Failed to delete user ${userId} from Firebase Authentication: ${error instanceof Error ? error.message : String(error)}`,
      userId: userId,
      deletedBy: adminEmail || context.auth?.token?.email || 'unknown',
      error: error instanceof Error ? error.message : String(error),
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    });

    throw new functions.https.HttpsError('internal', `Failed to delete user: ${error instanceof Error ? error.message : String(error)}`);
  }
});

// Export webhook handlers
export { stripeWebhook, paypalWebhook };
