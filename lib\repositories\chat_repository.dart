import 'dart:async';
import 'package:flight_fear_wellness_app/models/chat_message.dart';
import 'package:flight_fear_wellness_app/models/user_memory_model.dart';
import 'package:flight_fear_wellness_app/services/firebase_service.dart';
import 'package:flight_fear_wellness_app/services/secure_api_service.dart';
import 'package:flight_fear_wellness_app/services/crisis_intervention_service.dart';
import 'package:flight_fear_wellness_app/services/user_memory_service.dart';
import 'package:flight_fear_wellness_app/services/memory_analysis_service.dart';
import 'package:flight_fear_wellness_app/providers/subscription_provider.dart';


class ChatRepository {
  final FirebaseService firebaseService;
  final SecureAPIService _secureAPIService = SecureAPIService();
  final UserMemoryService _memoryService = UserMemoryService();
  final MemoryAnalysisService _analysisService = MemoryAnalysisService();
  final SubscriptionProvider? subscriptionProvider;

  ChatRepository({
    required this.firebaseService,
    this.subscriptionProvider,
  });

  Stream<List<ChatMessage>> getChatMessages(String userId) {
    return firebaseService.getChatMessages(userId);
  }

  // Purpose: Save chat message to Firebase with error handling
  Future<void> saveMessage(ChatMessage message) async {
    final response = await firebaseService.saveChatMessage(message);
    if (!response.success) {
      throw Exception(response.error ?? 'Failed to save message');
    }
  }

  // Purpose: Generate AI response using secure API with mood analysis, credit validation, and user memory context
  Future<MoodBasedChatResponse> generateAIResponse(
    String userId,
    String message,
    {List<ChatMessage>? conversationHistory}
  ) async {
    try {
      // Get user memory for context
      final memoryResponse = await _memoryService.getUserMemory(userId);
      final userMemory = memoryResponse.success ? memoryResponse.data : null;

      final historyForAPI = conversationHistory?.map((msg) => {
        'role': msg.type == MessageType.user ? 'user' : 'assistant',
        'content': msg.content,
      }).toList() ?? [];

      // Add user memory context to conversation history
      final contextualHistory = userMemory != null
          ? _addUserContextToHistory(historyForAPI, userMemory)
          : historyForAPI;

      final response = await _secureAPIService.processChatRequest(
        message: message,
        conversationHistory: contextualHistory,
      );

      if (!response['success']) {
        throw Exception(response['error'] ?? 'Failed to generate AI response');
      }

      final therapeuticResponse = response['message'];
      final moodLevel = response['moodLevel'] ?? 'normal';

      if (subscriptionProvider != null && response['updatedCredits'] != null) {
        print('ChatRepository: Updating subscription provider with new credit data');
        print('ChatRepository: Credit data received: ${response['updatedCredits']}');
        subscriptionProvider!.updateCreditsDirectly(response['updatedCredits']);
      } else {
        print('ChatRepository: Warning - No subscription provider or credit data available');
        if (subscriptionProvider == null) {
          print('ChatRepository: Subscription provider is null');
        }
        if (response['updatedCredits'] == null) {
          print('ChatRepository: Updated credits data is null');
        }
      }

      final aiMessage = ChatMessage(
        id: '${DateTime.now().millisecondsSinceEpoch}_ai',
        userId: userId,
        content: therapeuticResponse,
        type: MessageType.ai,
        timestamp: DateTime.now(),
        metadata: {
          'conversationId': DateTime.now().millisecondsSinceEpoch.toString(),
          'tokensUsed': response['usage']?['total_tokens'] ?? 0,
          'isTherapeutic': true,
          'model': response['model'] ?? 'gpt-4o-mini',
          'moodLevel': moodLevel,
        },
      );

      await saveMessage(aiMessage);

      // Analyze conversation for memory insights
      if (conversationHistory != null) {
        final allMessages = [...conversationHistory, aiMessage];
        await _analysisService.analyzeConversation(userId, allMessages, isVoiceSession: false);
      }

      final interventionOptions = CrisisInterventionService.getCrisisOptions(moodLevel);

      final shouldOfferDebate = CrisisInterventionService.shouldOfferDebate(message, historyForAPI);

      return MoodBasedChatResponse(
        message: therapeuticResponse,
        moodLevel: moodLevel,
        interventionOptions: interventionOptions,
        shouldOfferDebate: shouldOfferDebate,
        metadata: {
          'chatMessage': aiMessage,
          'tokensUsed': response['usage']?['total_tokens'] ?? 0,
          'model': response['model'] ?? 'gpt-4o-mini',
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Add user memory context to conversation history
  List<Map<String, String>> _addUserContextToHistory(
    List<Map<String, String>> history,
    UserMemoryModel userMemory,
  ) {
    final contextMessage = _buildContextMessage(userMemory);
    if (contextMessage.isEmpty) return history;

    // Add context as a system message at the beginning
    return [
      {'role': 'system', 'content': contextMessage},
      ...history,
    ];
  }

  /// Build context message from user memory
  String _buildContextMessage(UserMemoryModel userMemory) {
    final contextParts = <String>[];

    // Personal information context
    if (userMemory.personalInfo.preferredName != null) {
      contextParts.add('User prefers to be called: ${userMemory.personalInfo.preferredName}');
    }

    if (userMemory.personalInfo.phobias.isNotEmpty) {
      contextParts.add('Known phobias/fears: ${userMemory.personalInfo.phobias.join(', ')}');
    }

    if (userMemory.personalInfo.copingMechanisms.isNotEmpty) {
      contextParts.add('Effective coping mechanisms: ${userMemory.personalInfo.copingMechanisms.join(', ')}');
    }

    // Aviation-specific context
    if (userMemory.aviationData.flightFears.isNotEmpty) {
      final fears = userMemory.aviationData.flightFears.entries
          .map((e) => '${e.key} (severity: ${e.value}/10)')
          .join(', ');
      contextParts.add('Flight-specific fears: $fears');
    }

    if (userMemory.aviationData.upcomingFlights.isNotEmpty) {
      final upcomingFlight = userMemory.aviationData.upcomingFlights.first;
      contextParts.add('Upcoming flight: ${upcomingFlight.departureAirport} to ${upcomingFlight.arrivalAirport} on ${upcomingFlight.flightDate.toString().split(' ')[0]}');
    }

    if (userMemory.aviationData.successfulCopingStrategies.isNotEmpty) {
      contextParts.add('Previously successful strategies: ${userMemory.aviationData.successfulCopingStrategies.join(', ')}');
    }

    // Behavioral patterns context
    if (userMemory.behavioralPatterns.preferredCopingMethods.isNotEmpty) {
      contextParts.add('Preferred coping methods: ${userMemory.behavioralPatterns.preferredCopingMethods.join(', ')}');
    }

    // Therapy progress context
    if (userMemory.therapyProgress.goals.isNotEmpty) {
      final activeGoals = userMemory.therapyProgress.goals
          .where((goal) => !goal.isCompleted)
          .map((goal) => goal.title)
          .join(', ');
      if (activeGoals.isNotEmpty) {
        contextParts.add('Current therapy goals: $activeGoals');
      }
    }

    if (contextParts.isEmpty) return '';

    return 'User Context: ${contextParts.join('. ')}.';
  }

  // Purpose: Clear all chat history for a specific user
  Future<void> clearChatHistory(String userId) async {
    final response = await firebaseService.deleteChatHistory(userId);
    if (!response.success) {
      throw Exception(response.error ?? 'Failed to delete chat history');
    }
  }

  
}