import 'package:cloud_firestore/cloud_firestore.dart';

/// Model for intelligent insights extracted from user conversations and behavior
class MemoryInsights {
  final String userId;
  final DateTime generatedAt;
  final double averageAnxietyLevel;
  final int totalSessions;
  final int voiceSessions;
  final int chatSessions;
  final Map<String, int> detectedPhobias;
  final Map<String, int> detectedTriggers;
  final Map<String, int> successfulStrategies;
  final Map<String, int> moodPatterns;
  final List<FlightExperience> flightHistory;
  final List<String> medicalNotes;
  final Map<String, double> weeklyAnxietyTrends;
  final Map<String, int> toolUsageFrequency;
  final double copingSuccessRate;
  final List<String> improvementAreas;
  final List<String> strengths;

  const MemoryInsights({
    required this.userId,
    required this.generatedAt,
    required this.averageAnxietyLevel,
    required this.totalSessions,
    required this.voiceSessions,
    required this.chatSessions,
    required this.detectedPhobias,
    required this.detectedTriggers,
    required this.successfulStrategies,
    required this.moodPatterns,
    required this.flightHistory,
    required this.medicalNotes,
    required this.weeklyAnxietyTrends,
    required this.toolUsageFrequency,
    required this.copingSuccessRate,
    required this.improvementAreas,
    required this.strengths,
  });

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'generatedAt': Timestamp.fromDate(generatedAt),
      'averageAnxietyLevel': averageAnxietyLevel,
      'totalSessions': totalSessions,
      'voiceSessions': voiceSessions,
      'chatSessions': chatSessions,
      'detectedPhobias': detectedPhobias,
      'detectedTriggers': detectedTriggers,
      'successfulStrategies': successfulStrategies,
      'moodPatterns': moodPatterns,
      'flightHistory': flightHistory.map((f) => f.toMap()).toList(),
      'medicalNotes': medicalNotes,
      'weeklyAnxietyTrends': weeklyAnxietyTrends,
      'toolUsageFrequency': toolUsageFrequency,
      'copingSuccessRate': copingSuccessRate,
      'improvementAreas': improvementAreas,
      'strengths': strengths,
    };
  }

  factory MemoryInsights.fromMap(Map<String, dynamic> map) {
    return MemoryInsights(
      userId: map['userId'] ?? '',
      generatedAt: (map['generatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      averageAnxietyLevel: (map['averageAnxietyLevel'] ?? 0.0).toDouble(),
      totalSessions: map['totalSessions'] ?? 0,
      voiceSessions: map['voiceSessions'] ?? 0,
      chatSessions: map['chatSessions'] ?? 0,
      detectedPhobias: Map<String, int>.from(map['detectedPhobias'] ?? {}),
      detectedTriggers: Map<String, int>.from(map['detectedTriggers'] ?? {}),
      successfulStrategies: Map<String, int>.from(map['successfulStrategies'] ?? {}),
      moodPatterns: Map<String, int>.from(map['moodPatterns'] ?? {}),
      flightHistory: (map['flightHistory'] as List?)
          ?.map((f) => FlightExperience.fromMap(f))
          .toList() ?? [],
      medicalNotes: List<String>.from(map['medicalNotes'] ?? []),
      weeklyAnxietyTrends: Map<String, double>.from(map['weeklyAnxietyTrends'] ?? {}),
      toolUsageFrequency: Map<String, int>.from(map['toolUsageFrequency'] ?? {}),
      copingSuccessRate: (map['copingSuccessRate'] ?? 0.0).toDouble(),
      improvementAreas: List<String>.from(map['improvementAreas'] ?? []),
      strengths: List<String>.from(map['strengths'] ?? []),
    );
  }
}

/// Model for flight experience data
class FlightExperience {
  final DateTime flightDate;
  final String route;
  final double anxietyLevel;
  final List<String> strategiesUsed;
  final String outcome;
  final String notes;

  const FlightExperience({
    required this.flightDate,
    required this.route,
    required this.anxietyLevel,
    required this.strategiesUsed,
    required this.outcome,
    required this.notes,
  });

  Map<String, dynamic> toMap() {
    return {
      'flightDate': Timestamp.fromDate(flightDate),
      'route': route,
      'anxietyLevel': anxietyLevel,
      'strategiesUsed': strategiesUsed,
      'outcome': outcome,
      'notes': notes,
    };
  }

  factory FlightExperience.fromMap(Map<String, dynamic> map) {
    return FlightExperience(
      flightDate: (map['flightDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      route: map['route'] ?? '',
      anxietyLevel: (map['anxietyLevel'] ?? 0.0).toDouble(),
      strategiesUsed: List<String>.from(map['strategiesUsed'] ?? []),
      outcome: map['outcome'] ?? '',
      notes: map['notes'] ?? '',
    );
  }
}
