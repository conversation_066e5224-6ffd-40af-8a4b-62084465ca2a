import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import '../../interfaces/payment_provider_interface.dart';
import '../../models/payment_models.dart';
import '../../models/subscription_model.dart';

// Purpose: LemonSqueezy payment provider implementation for subscription management
class LemonSqueezyProvider implements PaymentProviderInterface {
  static const String _baseUrl = 'https://api.lemonsqueezy.com/v1';
  
  PaymentConfig? _config;
  Map<String, String> get _headers => {
    'Authorization': 'Bearer ${_config?.apiKey}',
    'Accept': 'application/vnd.api+json',
    'Content-Type': 'application/vnd.api+json',
  };

  @override
  Future<void> initialize(PaymentConfig config) async {
    if (!validateConfig(config)) {
      throw Exception('Invalid LemonSqueezy configuration');
    }

    _config = config;

    try {
      // Verify API connectivity by making a test call
      await _verifyConnection();
      debugPrint('LemonSqueezy provider initialized successfully');
    } catch (e) {
      debugPrint('LemonSqueezy initialization failed: $e');
      throw Exception('Failed to initialize LemonSqueezy: $e');
    }
  }

  Future<void> _verifyConnection() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/stores'),
        headers: _headers,
      );

      if (response.statusCode != 200) {
        final errorBody = response.body;
        throw Exception('API connectivity test failed: ${response.statusCode} - $errorBody');
      }

      // Verify that the store ID exists in the response
      final data = jsonDecode(response.body);
      final stores = data['data'] as List?;

      if (stores == null || stores.isEmpty) {
        throw Exception('No stores found for this API key');
      }

      // Check if our configured store ID exists
      final storeExists = stores.any((store) => store['id'] == _config?.storeId);
      if (!storeExists) {
        debugPrint('Warning: Configured store ID ${_config?.storeId} not found in available stores');
      }

      debugPrint('LemonSqueezy API connectivity verified');
    } catch (e) {
      throw Exception('LemonSqueezy API verification failed: $e');
    }
  }

  @override
  Future<PaymentResult> createCustomer({
    required String email,
    required String name,
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/customers'),
        headers: _headers,
        body: jsonEncode({
          'data': {
            'type': 'customers',
            'attributes': {
              'name': name,
              'email': email,
              'store_id': _config?.storeId,
              ...metadata,
            },
          },
        }),
      );

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        final customerId = data['data']['id'];
        
        return PaymentResult.success(
          paymentId: customerId,
          customerId: customerId,
          metadata: {'customer_data': data['data']},
        );
      } else {
        final error = jsonDecode(response.body);
        return PaymentResult.failure(
          error: error['errors']?[0]?['detail'] ?? 'Failed to create customer',
        );
      }
    } catch (e) {
      return PaymentResult.failure(error: e.toString());
    }
  }

  @override
  Future<PaymentResult> createSubscription({
    required String customerId,
    required SubscriptionPlan plan,
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      final productId = _config?.planIds[plan];
      if (productId == null) {
        return PaymentResult.failure(
          error: 'Product ID not configured for plan: ${plan.name}',
        );
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/subscriptions'),
        headers: _headers,
        body: jsonEncode({
          'data': {
            'type': 'subscriptions',
            'attributes': {
              'customer_id': customerId,
              'product_id': productId,
              'store_id': _config?.storeId,
              ...metadata,
            },
          },
        }),
      );

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        final subscriptionId = data['data']['id'];
        
        return PaymentResult.success(
          paymentId: subscriptionId,
          subscriptionId: subscriptionId,
          customerId: customerId,
          metadata: {'subscription_data': data['data']},
        );
      } else {
        final error = jsonDecode(response.body);
        return PaymentResult.failure(
          error: error['errors']?[0]?['detail'] ?? 'Failed to create subscription',
        );
      }
    } catch (e) {
      return PaymentResult.failure(error: e.toString());
    }
  }

  @override
  Future<PaymentResult> createPaymentIntent({
    required String customerId,
    required SubscriptionPlan plan,
    required double amount,
    required String currency,
    Map<String, dynamic> metadata = const {},
  }) async {
    return createSubscription(
      customerId: customerId,
      plan: plan,
      metadata: {
        ...metadata,
        'amount': amount,
        'currency': currency,
      },
    );
  }

  @override
  Future<ProviderSubscription?> getSubscription(String subscriptionId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/subscriptions/$subscriptionId'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final attributes = data['data']['attributes'];
        
        return ProviderSubscription(
          id: subscriptionId,
          customerId: attributes['customer_id'].toString(),
          plan: _parsePlanFromProductId(attributes['product_id'].toString()),
          status: _parseSubscriptionStatus(attributes['status']),
          currentPeriodStart: DateTime.parse(attributes['created_at']),
          currentPeriodEnd: DateTime.parse(attributes['renews_at'] ?? attributes['ends_at']),
          cancelAtPeriodEnd: attributes['cancelled'] ?? false,
          amount: (attributes['subtotal'] ?? 0).toDouble() / 100,
          currency: attributes['currency'] ?? 'USD',
          provider: PaymentProvider.lemonsqueezy,
          metadata: attributes,
        );
      }
      
      return null;
    } catch (e) {
      debugPrint('Failed to get subscription: $e');
      return null;
    }
  }

  @override
  Future<PaymentResult> cancelSubscription({
    required String subscriptionId,
    bool cancelAtPeriodEnd = true,
  }) async {
    try {
      final response = await http.patch(
        Uri.parse('$_baseUrl/subscriptions/$subscriptionId'),
        headers: _headers,
        body: jsonEncode({
          'data': {
            'type': 'subscriptions',
            'id': subscriptionId,
            'attributes': {
              'cancelled': true,
            },
          },
        }),
      );

      if (response.statusCode == 200) {
        return PaymentResult.success(
          paymentId: subscriptionId,
          subscriptionId: subscriptionId,
        );
      } else {
        final error = jsonDecode(response.body);
        return PaymentResult.failure(
          error: error['errors']?[0]?['detail'] ?? 'Failed to cancel subscription',
        );
      }
    } catch (e) {
      return PaymentResult.failure(error: e.toString());
    }
  }

  @override
  Future<PaymentResult> updateSubscription({
    required String subscriptionId,
    required SubscriptionPlan newPlan,
  }) async {
    try {
      final productId = _config?.planIds[newPlan];
      if (productId == null) {
        return PaymentResult.failure(
          error: 'Product ID not configured for plan: ${newPlan.name}',
        );
      }

      final response = await http.patch(
        Uri.parse('$_baseUrl/subscriptions/$subscriptionId'),
        headers: _headers,
        body: jsonEncode({
          'data': {
            'type': 'subscriptions',
            'id': subscriptionId,
            'attributes': {
              'product_id': productId,
            },
          },
        }),
      );

      if (response.statusCode == 200) {
        return PaymentResult.success(
          paymentId: subscriptionId,
          subscriptionId: subscriptionId,
        );
      } else {
        final error = jsonDecode(response.body);
        return PaymentResult.failure(
          error: error['errors']?[0]?['detail'] ?? 'Failed to update subscription',
        );
      }
    } catch (e) {
      return PaymentResult.failure(error: e.toString());
    }
  }

  @override
  Future<PaymentCustomer?> getCustomer(String customerId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/customers/$customerId'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final attributes = data['data']['attributes'];
        
        return PaymentCustomer(
          id: customerId,
          email: attributes['email'],
          name: attributes['name'],
          provider: PaymentProvider.lemonsqueezy,
          providerCustomerId: customerId,
          metadata: attributes,
        );
      }
      
      return null;
    } catch (e) {
      debugPrint('Failed to get customer: $e');
      return null;
    }
  }

  @override
  Future<List<PaymentInvoice>> getCustomerInvoices(String customerId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/orders?filter[customer_id]=$customerId'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final orders = data['data'] as List;
        
        return orders.map((order) {
          final attributes = order['attributes'];
          return PaymentInvoice(
            id: order['id'],
            customerId: customerId,
            amount: (attributes['total'] ?? 0).toDouble() / 100,
            currency: attributes['currency'] ?? 'USD',
            status: _parsePaymentStatus(attributes['status']),
            createdAt: DateTime.parse(attributes['created_at']),
            paidAt: attributes['status'] == 'paid' 
                ? DateTime.parse(attributes['created_at']) 
                : null,
            provider: PaymentProvider.lemonsqueezy,
            metadata: attributes,
          );
        }).toList();
      }
      
      return [];
    } catch (e) {
      debugPrint('Failed to get customer invoices: $e');
      return [];
    }
  }

  @override
  bool verifyWebhookSignature({
    required String payload,
    required String signature,
    required String secret,
  }) {
    try {
      final hmac = Hmac(sha256, utf8.encode(secret));
      final digest = hmac.convert(utf8.encode(payload));
      final expectedSignature = 'sha256=${digest.toString()}';
      
      return signature == expectedSignature;
    } catch (e) {
      debugPrint('Failed to verify webhook signature: $e');
      return false;
    }
  }

  @override
  Future<PaymentResult> processWebhookEvent(WebhookEvent event) async {
    try {
      switch (event.eventType) {
        case 'subscription_created':
          await _handleSubscriptionCreated(event.data);
          break;
        case 'subscription_updated':
          await _handleSubscriptionUpdated(event.data);
          break;
        case 'subscription_cancelled':
          await _handleSubscriptionCancelled(event.data);
          break;
        case 'order_created':
          await _handleOrderCreated(event.data);
          break;
        default:
          debugPrint('Unhandled webhook event: ${event.eventType}');
      }
      
      return PaymentResult.success(
        paymentId: event.id,
        metadata: {'event_type': event.eventType},
      );
    } catch (e) {
      return PaymentResult.failure(error: e.toString());
    }
  }

  @override
  String getCheckoutUrl({
    required SubscriptionPlan plan,
    required String customerEmail,
    String? customerId,
    Map<String, dynamic> metadata = const {},
  }) {
    final productId = _config?.planIds[plan];
    if (productId == null) {
      throw Exception('Product ID not configured for plan: ${plan.name}');
    }

    final params = {
      'checkout[email]': customerEmail,
      'checkout[custom][user_id]': customerId ?? '',
      ...metadata.map((k, v) => MapEntry('checkout[custom][$k]', v.toString())),
    };

    final queryString = params.entries
        .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');

    return 'https://your-store.lemonsqueezy.com/checkout/buy/${_config?.storeId}?$queryString';
  }

  @override
  String? getCustomerPortalUrl(String customerId) {
    return null;
  }

  @override
  bool validateConfig(PaymentConfig config) {
    return config.provider == PaymentProvider.lemonsqueezy &&
           config.apiKey.isNotEmpty &&
           config.storeId?.isNotEmpty == true &&
           config.planIds.isNotEmpty;
  }

  @override
  String get providerName => 'LemonSqueezy';

  @override
  List<String> get supportedFeatures => [
        'subscriptions',
        'one-time-payments',
        'webhooks',
        'customer-management',
      ];

  SubscriptionPlan _parsePlanFromProductId(String productId) {
    final planEntry = _config?.planIds.entries.firstWhere(
      (entry) => entry.value == productId,
      orElse: () => const MapEntry(SubscriptionPlan.free, ''),
    );
    return planEntry?.key ?? SubscriptionPlan.free;
  }

  ProviderSubscriptionStatus _parseSubscriptionStatus(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return ProviderSubscriptionStatus.active;
      case 'cancelled':
        return ProviderSubscriptionStatus.cancelled;
      case 'expired':
        return ProviderSubscriptionStatus.expired;
      case 'past_due':
        return ProviderSubscriptionStatus.pastDue;
      case 'unpaid':
        return ProviderSubscriptionStatus.unpaid;
      default:
        return ProviderSubscriptionStatus.active;
    }
  }

  PaymentStatus _parsePaymentStatus(String status) {
    switch (status.toLowerCase()) {
      case 'paid':
        return PaymentStatus.completed;
      case 'pending':
        return PaymentStatus.pending;
      case 'failed':
        return PaymentStatus.failed;
      case 'refunded':
        return PaymentStatus.refunded;
      default:
        return PaymentStatus.pending;
    }
  }

  Future<void> _handleSubscriptionCreated(Map<String, dynamic> data) async {
    debugPrint('Handling subscription created: ${data['data']['id']}');
  }

  Future<void> _handleSubscriptionUpdated(Map<String, dynamic> data) async {
    debugPrint('Handling subscription updated: ${data['data']['id']}');
  }

  Future<void> _handleSubscriptionCancelled(Map<String, dynamic> data) async {
    debugPrint('Handling subscription cancelled: ${data['data']['id']}');
  }

  Future<void> _handleOrderCreated(Map<String, dynamic> data) async {
    debugPrint('Handling order created: ${data['data']['id']}');
  }
}
