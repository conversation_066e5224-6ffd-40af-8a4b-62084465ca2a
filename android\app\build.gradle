plugins {
    id "com.android.application"
    id "org.jetbrains.kotlin.android"  // Correct (version inherited from root project)
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"  // Google Services plugin
}

android {
    namespace = "com.example.flight_fear_wellness_app"
    compileSdk 35
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
        freeCompilerArgs += ['-Xjvm-default=all']
    }

    defaultConfig {
        applicationId = "com.example.flight_fear_wellness_app"
        minSdk 23
        targetSdk 35  // Changed from targetSdkVersion to targetSdk (modern syntax)
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        multiDexEnabled = true
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.debug
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8"  // Removed explicit version (inherit from root)
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation platform('com.google.firebase:firebase-bom:33.0.0')
    implementation 'com.google.firebase:firebase-analytics-ktx'  // Recommended over firebase-analytics
}