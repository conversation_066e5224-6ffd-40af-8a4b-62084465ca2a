import 'package:equatable/equatable.dart';
import 'package:flight_fear_wellness_app/services/crisis_intervention_service.dart';

enum VoiceStatus { initial, recording, processing, playing, idle, error }

class VoiceState extends Equatable {
  final VoiceStatus status;
  final String? error;
  final String? currentInput;
  final String? currentResponse;
  final String? audioFilePath;
  final bool isRecording;
  final List<String> conversationHistory;
  final String displayedText;
  final int currentWordIndex;
  final List<String> responseWords;
  final bool isAnimatingText;
  final List<CrisisInterventionOption> crisisOptions;
  final String? currentMoodLevel;
  final bool shouldOfferDebate;
  final String? debateTopic;

  const VoiceState({
    this.status = VoiceStatus.initial,
    this.error,
    this.currentInput,
    this.currentResponse,
    this.audioFilePath,
    this.isRecording = false,
    this.conversationHistory = const [],
    this.displayedText = '',
    this.currentWordIndex = 0,
    this.responseWords = const [],
    this.isAnimatingText = false,
    this.crisisOptions = const [],
    this.currentMoodLevel,
    this.shouldOfferDebate = false,
    this.debateTopic,
  });

  VoiceState copyWith({
    VoiceStatus? status,
    String? error,
    String? currentInput,
    String? currentResponse,
    String? audioFilePath,
    bool? isRecording,
    List<String>? conversationHistory,
    String? displayedText,
    int? currentWordIndex,
    List<String>? responseWords,
    bool? isAnimatingText,
    List<CrisisInterventionOption>? crisisOptions,
    String? currentMoodLevel,
    bool? shouldOfferDebate,
    String? debateTopic,
  }) {
    return VoiceState(
      status: status ?? this.status,
      error: error ?? this.error,
      currentInput: currentInput ?? this.currentInput,
      currentResponse: currentResponse ?? this.currentResponse,
      audioFilePath: audioFilePath ?? this.audioFilePath,
      isRecording: isRecording ?? this.isRecording,
      conversationHistory: conversationHistory ?? this.conversationHistory,
      displayedText: displayedText ?? this.displayedText,
      currentWordIndex: currentWordIndex ?? this.currentWordIndex,
      responseWords: responseWords ?? this.responseWords,
      isAnimatingText: isAnimatingText ?? this.isAnimatingText,
      crisisOptions: crisisOptions ?? this.crisisOptions,
      currentMoodLevel: currentMoodLevel ?? this.currentMoodLevel,
      shouldOfferDebate: shouldOfferDebate ?? this.shouldOfferDebate,
      debateTopic: debateTopic ?? this.debateTopic,
    );
  }

  @override
  List<Object?> get props => [
        status,
        error,
        currentInput,
        currentResponse,
        audioFilePath,
        isRecording,
        conversationHistory,
        displayedText,
        currentWordIndex,
        responseWords,
        isAnimatingText,
        crisisOptions,
        currentMoodLevel,
        shouldOfferDebate,
        debateTopic,
      ];
}