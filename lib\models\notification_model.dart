import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

enum NotificationType {
  welcome,
  subscriptionChange,
  creditWarning,
  paymentConfirmation,
  planAccessUpdate,
  general,
}

enum NotificationPriority {
  low,
  normal,
  high,
  urgent,
}

class NotificationModel extends Equatable {
  final String id;
  final String userId;
  final String title;
  final String message;
  final NotificationType type;
  final NotificationPriority priority;
  final bool isRead;
  final DateTime createdAt;
  final DateTime? readAt;
  final Map<String, dynamic>? metadata;
  final String? actionUrl;
  final String? actionText;

  const NotificationModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.message,
    required this.type,
    this.priority = NotificationPriority.normal,
    this.isRead = false,
    required this.createdAt,
    this.readAt,
    this.metadata,
    this.actionUrl,
    this.actionText,
  });

  factory NotificationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return NotificationModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      title: data['title'] ?? '',
      message: data['message'] ?? '',
      type: NotificationType.values.firstWhere(
        (e) => e.name == data['type'],
        orElse: () => NotificationType.general,
      ),
      priority: NotificationPriority.values.firstWhere(
        (e) => e.name == data['priority'],
        orElse: () => NotificationPriority.normal,
      ),
      isRead: data['isRead'] ?? false,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      readAt: (data['readAt'] as Timestamp?)?.toDate(),
      metadata: data['metadata'] as Map<String, dynamic>?,
      actionUrl: data['actionUrl'],
      actionText: data['actionText'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'title': title,
      'message': message,
      'type': type.name,
      'priority': priority.name,
      'isRead': isRead,
      'createdAt': Timestamp.fromDate(createdAt),
      'readAt': readAt != null ? Timestamp.fromDate(readAt!) : null,
      'metadata': metadata,
      'actionUrl': actionUrl,
      'actionText': actionText,
    };
  }

  NotificationModel copyWith({
    String? id,
    String? userId,
    String? title,
    String? message,
    NotificationType? type,
    NotificationPriority? priority,
    bool? isRead,
    DateTime? createdAt,
    DateTime? readAt,
    Map<String, dynamic>? metadata,
    String? actionUrl,
    String? actionText,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      metadata: metadata ?? this.metadata,
      actionUrl: actionUrl ?? this.actionUrl,
      actionText: actionText ?? this.actionText,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        title,
        message,
        type,
        priority,
        isRead,
        createdAt,
        readAt,
        metadata,
        actionUrl,
        actionText,
      ];
}

class NotificationFactory {
  static NotificationModel createWelcomeNotification(String userId) {
    return NotificationModel(
      id: '',
      userId: userId,
      title: 'Welcome to Alora! 🌟',
      message: 'You now have access to Basic plan features: 25 chat credits and 5 voice credits. Start your journey to overcome flight anxiety!',
      type: NotificationType.welcome,
      priority: NotificationPriority.normal,
      createdAt: DateTime.now(),
      metadata: {
        'chatCredits': 25,
        'voiceCredits': 5,
        'planType': 'basic',
      },
      actionText: 'Get Started',
      actionUrl: '/home',
    );
  }

  static NotificationModel createSubscriptionChangeNotification(
    String userId,
    String fromPlan,
    String toPlan,
    Map<String, int> newCredits,
  ) {
    final isUpgrade = _isUpgrade(fromPlan, toPlan);
    final emoji = isUpgrade ? '🎉' : '📋';
    final action = isUpgrade ? 'upgraded' : 'changed';
    
    return NotificationModel(
      id: '',
      userId: userId,
      title: 'Plan ${action.toUpperCase()} $emoji',
      message: 'Your plan has been $action from $fromPlan to $toPlan. New credits: ${newCredits['chat']} chat, ${newCredits['voice']} voice.',
      type: NotificationType.subscriptionChange,
      priority: isUpgrade ? NotificationPriority.high : NotificationPriority.normal,
      createdAt: DateTime.now(),
      metadata: {
        'fromPlan': fromPlan,
        'toPlan': toPlan,
        'chatCredits': newCredits['chat'],
        'voiceCredits': newCredits['voice'],
        'isUpgrade': isUpgrade,
      },
      actionText: 'View Features',
      actionUrl: '/subscription',
    );
  }

  static NotificationModel createCreditWarningNotification(
    String userId,
    String creditType,
    int remaining,
    int total,
  ) {
    final percentage = (remaining / total * 100).round();
    return NotificationModel(
      id: '',
      userId: userId,
      title: 'Credit Usage Alert ⚠️',
      message: 'You have $remaining $creditType credits remaining ($percentage% used). Consider upgrading for unlimited access.',
      type: NotificationType.creditWarning,
      priority: NotificationPriority.high,
      createdAt: DateTime.now(),
      metadata: {
        'creditType': creditType,
        'remaining': remaining,
        'total': total,
        'percentage': percentage,
      },
      actionText: 'Upgrade Plan',
      actionUrl: '/subscription',
    );
  }

  static NotificationModel createPaymentConfirmationNotification(
    String userId,
    String planName,
    double amount,
    Map<String, int> allocatedCredits,
  ) {
    return NotificationModel(
      id: '',
      userId: userId,
      title: 'Payment Successful! 💳',
      message: 'Your $planName subscription (\$${amount.toStringAsFixed(0)}) is active. Credits allocated: ${allocatedCredits['chat']} chat, ${allocatedCredits['voice']} voice.',
      type: NotificationType.paymentConfirmation,
      priority: NotificationPriority.high,
      createdAt: DateTime.now(),
      metadata: {
        'planName': planName,
        'amount': amount,
        'chatCredits': allocatedCredits['chat'],
        'voiceCredits': allocatedCredits['voice'],
      },
      actionText: 'Start Using',
      actionUrl: '/home',
    );
  }

  static NotificationModel createPlanAccessUpdateNotification(
    String userId,
    String planName,
    List<String> newFeatures,
  ) {
    return NotificationModel(
      id: '',
      userId: userId,
      title: 'New Features Available! ✨',
      message: 'Your $planName plan now includes: ${newFeatures.join(', ')}. Explore these new capabilities!',
      type: NotificationType.planAccessUpdate,
      priority: NotificationPriority.normal,
      createdAt: DateTime.now(),
      metadata: {
        'planName': planName,
        'newFeatures': newFeatures,
      },
      actionText: 'Explore Features',
      actionUrl: '/home',
    );
  }

  static bool _isUpgrade(String fromPlan, String toPlan) {
    const planHierarchy = ['free', 'basic', 'premium'];
    final fromIndex = planHierarchy.indexOf(fromPlan.toLowerCase());
    final toIndex = planHierarchy.indexOf(toPlan.toLowerCase());
    return toIndex > fromIndex;
  }
}
