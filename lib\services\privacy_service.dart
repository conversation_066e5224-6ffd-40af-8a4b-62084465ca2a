import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:pdf/widgets.dart' as pw;
import '../models/user_memory_model.dart';
import '../models/api_response.dart';
import '../models/export_summary_model.dart';
import '../services/user_memory_service.dart';
import '../services/memory_analysis_service.dart';

import '../utils/intelligent_pdf_generator.dart';

/// Service for handling privacy controls and GDPR compliance
class PrivacyService {
  final UserMemoryService _memoryService = UserMemoryService();
  final MemoryAnalysisService _analysisService = MemoryAnalysisService();

  /// Export user memory data with intelligent insights and progress tracking
  Future<ApiResponse<String>> exportUserMemoryData(String userId, {String format = 'pdf'}) async {
    try {
      // Get user memory data
      final memoryResponse = await _memoryService.getUserMemory(userId);
      if (!memoryResponse.success) {
        return ApiResponse.error(memoryResponse.error!);
      }

      final userMemory = memoryResponse.data!;

      // Analyze existing chat history to ensure user memory is up-to-date
      debugPrint('PrivacyService: Analyzing existing chat history before export...');
      try {
        await _analysisService.analyzeExistingChatHistory(userId);
        debugPrint('PrivacyService: Chat history analysis completed');
      } catch (e) {
        debugPrint('PrivacyService: Error during chat history analysis: $e');
      }

      // Get updated user memory after analysis
      final updatedMemoryResponse = await _memoryService.getUserMemory(userId);
      final finalUserMemory = updatedMemoryResponse.success ? updatedMemoryResponse.data! : userMemory;

      // Generate intelligent insights
      final insights = await _analysisService.generateInsightsForExport(finalUserMemory);

      // Temporarily disable export history to avoid Firestore index issues
      ProgressComparison? progressComparison;

      String filePath;
      if (format == 'pdf') {
        // Generate intelligent PDF
        filePath = await IntelligentPdfGenerator.generateWellnessReport(
          insights: insights,
          userMemory: finalUserMemory,
          progressComparison: progressComparison,
        );
      } else {
        // Generate JSON export (fallback)
        filePath = await _generateJSONExport(finalUserMemory);
      }

      // Export history temporarily disabled to avoid Firestore index issues

      return ApiResponse.success(filePath);
    } catch (e) {
      debugPrint('Error exporting user memory data: $e');
      return ApiResponse.error('Failed to export data: ${e.toString()}');
    }
  }

  /// Generate JSON export as fallback
  Future<String> _generateJSONExport(UserMemoryModel userMemory) async {
    final directory = await getApplicationDocumentsDirectory();
    final timestamp = DateTime.now().toIso8601String().split('T')[0];
    final fileName = 'ALORA_Data_Export_$timestamp.json';
    final file = File('${directory.path}/$fileName');

    final jsonData = {
      'exportDate': DateTime.now().toIso8601String(),
      'appName': 'ALORA',
      'userData': userMemory.toMap(),
    };

    await file.writeAsString(jsonEncode(jsonData));
    return file.path;
  }

  /// Share exported data file with user
  Future<ApiResponse<void>> shareExportedData(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        return ApiResponse.error('Export file not found');
      }

      final result = await Share.shareXFiles(
        [XFile(filePath)],
        text: 'Your ALORA data export',
        subject: 'ALORA Personal Data Export',
      );

      if (result.status == ShareResultStatus.success) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error('Failed to share file: ${result.status}');
      }
    } catch (e) {
      debugPrint('Error sharing exported data: $e');
      return ApiResponse.error('Failed to share exported data: ${e.toString()}');
    }
  }

  /// Delete user memory data (GDPR right to be forgotten)
  Future<ApiResponse<void>> deleteUserMemoryData(String userId) async {
    try {
      // Delete user memory
      final memoryResult = await _memoryService.deleteUserMemory(userId);
      if (!memoryResult.success) {
        return ApiResponse.error('Failed to delete user memory: ${memoryResult.error}');
      }

      // Clear memory caches
      _memoryService.clearCache(userId);

      return ApiResponse.success(null);
    } catch (e) {
      debugPrint('Error deleting user memory data: $e');
      return ApiResponse.error('Failed to delete user memory data: ${e.toString()}');
    }
  }

  /// Get privacy settings for user
  Future<ApiResponse<PrivacySettings>> getPrivacySettings(String userId) async {
    try {
      // For now, return default settings
      // In a real app, these would be stored in Firebase
      final settings = PrivacySettings(
        userId: userId,
        dataCollectionEnabled: true,
        analyticsEnabled: true,
        personalizedAdsEnabled: false,
        dataRetentionDays: 365,
        shareDataForResearch: false,
        allowDataExport: true,
        allowDataDeletion: true,
        lastUpdated: DateTime.now(),
      );

      return ApiResponse.success(settings);
    } catch (e) {
      debugPrint('Error getting privacy settings: $e');
      return ApiResponse.error('Failed to get privacy settings: ${e.toString()}');
    }
  }

  /// Update privacy settings
  Future<ApiResponse<void>> updatePrivacySettings(
    String userId,
    PrivacySettings settings,
  ) async {
    try {
      // In a real app, save to Firebase
      // For now, just validate the settings
      if (settings.dataRetentionDays < 30) {
        return ApiResponse.error('Data retention must be at least 30 days');
      }

      return ApiResponse.success(null);
    } catch (e) {
      debugPrint('Error updating privacy settings: $e');
      return ApiResponse.error('Failed to update privacy settings: ${e.toString()}');
    }
  }

  /// Check if user has consented to data collection
  Future<bool> hasDataCollectionConsent(String userId) async {
    try {
      final settingsResponse = await getPrivacySettings(userId);
      if (settingsResponse.success) {
        return settingsResponse.data!.dataCollectionEnabled;
      }
      return false;
    } catch (e) {
      debugPrint('Error checking data collection consent: $e');
      return false;
    }
  }

  /// Record user consent for data collection
  Future<ApiResponse<void>> recordDataCollectionConsent(
    String userId,
    bool hasConsent,
  ) async {
    try {
      final settingsResponse = await getPrivacySettings(userId);
      if (!settingsResponse.success) {
        return ApiResponse.error(settingsResponse.error!);
      }

      final updatedSettings = settingsResponse.data!.copyWith(
        dataCollectionEnabled: hasConsent,
        lastUpdated: DateTime.now(),
      );

      return await updatePrivacySettings(userId, updatedSettings);
    } catch (e) {
      debugPrint('Error recording data collection consent: $e');
      return ApiResponse.error('Failed to record consent: ${e.toString()}');
    }
  }

  /// Get data retention policy information
  Map<String, dynamic> getDataRetentionPolicy() {
    return {
      'personalData': {
        'retentionPeriod': '1 year from last activity',
        'description': 'Personal information, preferences, and therapy progress',
      },
      'conversationHistory': {
        'retentionPeriod': '2 years from last activity',
        'description': 'Chat and voice conversation history',
      },
      'analyticsData': {
        'retentionPeriod': '6 months',
        'description': 'Anonymized usage statistics and app performance data',
      },
      'accountData': {
        'retentionPeriod': 'Until account deletion',
        'description': 'Email, authentication, and subscription information',
      },
    };
  }

  /// Get user rights information (GDPR)
  Map<String, dynamic> getUserRights() {
    return {
      'access': {
        'title': 'Right to Access',
        'description': 'You can request a copy of all personal data we hold about you.',
      },
      'rectification': {
        'title': 'Right to Rectification',
        'description': 'You can request correction of inaccurate or incomplete data.',
      },
      'erasure': {
        'title': 'Right to Erasure',
        'description': 'You can request deletion of your personal data.',
      },
      'portability': {
        'title': 'Right to Data Portability',
        'description': 'You can request your data in a machine-readable format.',
      },
      'objection': {
        'title': 'Right to Object',
        'description': 'You can object to processing of your personal data.',
      },
      'restriction': {
        'title': 'Right to Restriction',
        'description': 'You can request restriction of processing in certain circumstances.',
      },
    };
  }

  /// Anonymize user data (for research purposes)
  Future<ApiResponse<Map<String, dynamic>>> anonymizeUserData(String userId) async {
    try {
      final memoryResponse = await _memoryService.getUserMemory(userId);
      if (!memoryResponse.success) {
        return ApiResponse.error(memoryResponse.error!);
      }

      final memory = memoryResponse.data!;
      
      // Create anonymized version
      final anonymizedData = {
        'demographics': {
          'ageRange': _getAgeRange(memory.personalInfo.age),
          'hasOccupation': memory.personalInfo.occupation != null,
        },
        'phobias': {
          'count': memory.personalInfo.phobias.length,
          'categories': _categorizePhobias(memory.personalInfo.phobias),
        },
        'aviationData': {
          'flightFearsCount': memory.aviationData.flightFears.length,
          'averageFearSeverity': _calculateAverageSeverity(memory.aviationData.flightFears),
          'hasUpcomingFlights': memory.aviationData.upcomingFlights.isNotEmpty,
        },
        'behavioralPatterns': {
          'copingMethodsCount': memory.behavioralPatterns.preferredCopingMethods.length,
          'sessionFrequency': memory.behavioralPatterns.sessionFrequency,
        },
        'therapyProgress': {
          'overallProgress': memory.therapyProgress.overallProgress,
          'goalsCount': memory.therapyProgress.goals.length,
          'milestonesCount': memory.therapyProgress.milestones.length,
        },
        'metadata': {
          'anonymizedAt': DateTime.now().toIso8601String(),
          'dataVersion': '1.0',
        },
      };

      return ApiResponse.success(anonymizedData);
    } catch (e) {
      debugPrint('Error anonymizing user data: $e');
      return ApiResponse.error('Failed to anonymize user data: ${e.toString()}');
    }
  }

  String _getAgeRange(int? age) {
    if (age == null) return 'unknown';
    if (age < 18) return 'under_18';
    if (age < 25) return '18_24';
    if (age < 35) return '25_34';
    if (age < 45) return '35_44';
    if (age < 55) return '45_54';
    if (age < 65) return '55_64';
    return '65_plus';
  }

  List<String> _categorizePhobias(List<String> phobias) {
    final categories = <String>[];
    for (final phobia in phobias) {
      final lower = phobia.toLowerCase();
      if (lower.contains('flight') || lower.contains('flying') || lower.contains('airplane')) {
        categories.add('aviation');
      } else if (lower.contains('height') || lower.contains('falling')) {
        categories.add('heights');
      } else if (lower.contains('crowd') || lower.contains('people')) {
        categories.add('social');
      } else {
        categories.add('other');
      }
    }
    return categories.toSet().toList();
  }

  double _calculateAverageSeverity(Map<String, int> fears) {
    if (fears.isEmpty) return 0.0;
    final total = fears.values.reduce((a, b) => a + b);
    return total / fears.length;
  }

  // PDF Building Methods

  /// Build PDF header with ALORA branding
  pw.Widget _buildPdfHeader() {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              'ALORA',
              style: pw.TextStyle(
                fontSize: 28,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
            pw.Text(
              'Personal Data Export',
              style: const pw.TextStyle(
                fontSize: 16,
              ),
            ),
          ],
        ),
        pw.SizedBox(height: 8),
        pw.Divider(),
        pw.SizedBox(height: 8),
        pw.Text(
          'Flight Anxiety Wellness Companion',
          style: pw.TextStyle(
            fontSize: 14,
            fontStyle: pw.FontStyle.italic,
          ),
        ),
      ],
    );
  }

  /// Build export information section
  pw.Widget _buildExportInfo(String userId) {
    final now = DateTime.now();
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Export Information',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 12),
          pw.Text('Export Date: ${now.day}/${now.month}/${now.year} at ${now.hour}:${now.minute.toString().padLeft(2, '0')}'),
          pw.Text('User ID: $userId'),
          pw.Text('Export Version: 1.0'),
          pw.Text('App Version: ALORA v1.0'),
          pw.SizedBox(height: 8),
          pw.Text(
            'This document contains all your personal data stored in ALORA, formatted for easy reading.',
            style: pw.TextStyle(
              fontSize: 10,
              fontStyle: pw.FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  /// Build personal information section
  pw.Widget _buildPersonalInfoSection(Map<String, dynamic> userData) {
    final personalInfo = userData['personalInfo'] ?? {};

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Personal Information'),
        pw.SizedBox(height: 12),
        _buildDataTable([
          ['Name', personalInfo['name']?.toString() ?? 'Not provided'],
          ['Age', personalInfo['age']?.toString() ?? 'Not provided'],
          ['Occupation', personalInfo['occupation']?.toString() ?? 'Not provided'],
          ['Location', personalInfo['location']?.toString() ?? 'Not provided'],
          ['Emergency Contact', personalInfo['emergencyContact']?.toString() ?? 'Not provided'],
        ]),
        pw.SizedBox(height: 16),
        if (personalInfo['phobias'] != null && (personalInfo['phobias'] as List).isNotEmpty) ...[
          pw.Text(
            'Phobias and Fears:',
            style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12),
          ),
          pw.SizedBox(height: 4),
          ...((personalInfo['phobias'] as List).map((phobia) =>
            pw.Padding(
              padding: const pw.EdgeInsets.only(left: 16, bottom: 2),
              child: pw.Text('• $phobia', style: const pw.TextStyle(fontSize: 11)),
            ),
          )),
        ],
        if (personalInfo['medicalConditions'] != null && (personalInfo['medicalConditions'] as List).isNotEmpty) ...[
          pw.SizedBox(height: 12),
          pw.Text(
            'Medical Conditions:',
            style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12),
          ),
          pw.SizedBox(height: 4),
          ...((personalInfo['medicalConditions'] as List).map((condition) =>
            pw.Padding(
              padding: const pw.EdgeInsets.only(left: 16, bottom: 2),
              child: pw.Text('• $condition', style: const pw.TextStyle(fontSize: 11)),
            ),
          )),
        ],
      ],
    );
  }

  /// Build section header
  pw.Widget _buildSectionHeader(String title) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          title,
          style: pw.TextStyle(
            fontSize: 18,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 4),
        pw.Divider(),
      ],
    );
  }

  /// Build data table
  pw.Widget _buildDataTable(List<List<String>> data) {
    return pw.Table(
      border: pw.TableBorder.all(),
      children: data.map((row) =>
        pw.TableRow(
          children: row.map((cell) =>
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(cell, style: const pw.TextStyle(fontSize: 11)),
            ),
          ).toList(),
        ),
      ).toList(),
    );
  }

  /// Build flight anxiety section
  pw.Widget _buildFlightAnxietySection(Map<String, dynamic> userData) {
    final aviationData = userData['aviationData'] ?? {};

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Flight Anxiety Data'),
        pw.SizedBox(height: 12),

        if (aviationData['flightFears'] != null && (aviationData['flightFears'] as Map).isNotEmpty) ...[
          pw.Text('Flight Fears:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
          pw.SizedBox(height: 4),
          ...((aviationData['flightFears'] as Map).entries.map((entry) =>
            pw.Padding(
              padding: const pw.EdgeInsets.only(left: 16, bottom: 2),
              child: pw.Text('• ${entry.key}: ${entry.value}/10', style: const pw.TextStyle(fontSize: 11)),
            ),
          )),
          pw.SizedBox(height: 12),
        ],

        if (aviationData['upcomingFlights'] != null && (aviationData['upcomingFlights'] as List).isNotEmpty) ...[
          pw.Text('Upcoming Flights:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
          pw.SizedBox(height: 4),
          ...((aviationData['upcomingFlights'] as List).map((flight) =>
            pw.Padding(
              padding: const pw.EdgeInsets.only(left: 16, bottom: 2),
              child: pw.Text('• ${flight.toString()}', style: const pw.TextStyle(fontSize: 11)),
            ),
          )),
          pw.SizedBox(height: 12),
        ],

        if (aviationData['anxietyTriggers'] != null && (aviationData['anxietyTriggers'] as List).isNotEmpty) ...[
          pw.Text('Anxiety Triggers:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
          pw.SizedBox(height: 4),
          ...((aviationData['anxietyTriggers'] as List).map((trigger) =>
            pw.Padding(
              padding: const pw.EdgeInsets.only(left: 16, bottom: 2),
              child: pw.Text('• $trigger', style: const pw.TextStyle(fontSize: 11)),
            ),
          )),
        ],
      ],
    );
  }

  /// Build therapy progress section
  pw.Widget _buildTherapyProgressSection(Map<String, dynamic> userData) {
    final therapyProgress = userData['therapyProgress'] ?? {};

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Therapy Progress'),
        pw.SizedBox(height: 12),

        pw.Text('Overall Progress: ${therapyProgress['overallProgress'] ?? 0}%',
               style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
        pw.SizedBox(height: 12),

        if (therapyProgress['goals'] != null && (therapyProgress['goals'] as List).isNotEmpty) ...[
          pw.Text('Goals:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
          pw.SizedBox(height: 4),
          ...((therapyProgress['goals'] as List).map((goal) =>
            pw.Padding(
              padding: const pw.EdgeInsets.only(left: 16, bottom: 2),
              child: pw.Text('• ${goal.toString()}', style: const pw.TextStyle(fontSize: 11)),
            ),
          )),
          pw.SizedBox(height: 12),
        ],

        if (therapyProgress['milestones'] != null && (therapyProgress['milestones'] as List).isNotEmpty) ...[
          pw.Text('Milestones:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
          pw.SizedBox(height: 4),
          ...((therapyProgress['milestones'] as List).map((milestone) =>
            pw.Padding(
              padding: const pw.EdgeInsets.only(left: 16, bottom: 2),
              child: pw.Text('• ${milestone.toString()}', style: const pw.TextStyle(fontSize: 11)),
            ),
          )),
        ],
      ],
    );
  }

  /// Build behavioral patterns section
  pw.Widget _buildBehavioralPatternsSection(Map<String, dynamic> userData) {
    final behavioralPatterns = userData['behavioralPatterns'] ?? {};

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Behavioral Patterns'),
        pw.SizedBox(height: 12),

        pw.Text('Session Frequency: ${behavioralPatterns['sessionFrequency'] ?? 'Not specified'}',
               style: const pw.TextStyle(fontSize: 11)),
        pw.SizedBox(height: 8),

        if (behavioralPatterns['preferredCopingMethods'] != null &&
            (behavioralPatterns['preferredCopingMethods'] as List).isNotEmpty) ...[
          pw.Text('Preferred Coping Methods:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
          pw.SizedBox(height: 4),
          ...((behavioralPatterns['preferredCopingMethods'] as List).map((method) =>
            pw.Padding(
              padding: const pw.EdgeInsets.only(left: 16, bottom: 2),
              child: pw.Text('• $method', style: const pw.TextStyle(fontSize: 11)),
            ),
          )),
        ],
      ],
    );
  }

  /// Build conversation history section
  pw.Widget _buildConversationHistorySection(Map<String, dynamic> userData) {
    final conversationHistory = userData['conversationHistory'] ?? {};

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Conversation History'),
        pw.SizedBox(height: 12),

        pw.Text('Total Conversations: ${conversationHistory['totalConversations'] ?? 0}',
               style: const pw.TextStyle(fontSize: 11)),
        pw.SizedBox(height: 8),

        if (conversationHistory['recentTopics'] != null &&
            (conversationHistory['recentTopics'] as List).isNotEmpty) ...[
          pw.Text('Recent Topics:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
          pw.SizedBox(height: 4),
          ...((conversationHistory['recentTopics'] as List).take(10).map((topic) =>
            pw.Padding(
              padding: const pw.EdgeInsets.only(left: 16, bottom: 2),
              child: pw.Text('• $topic', style: const pw.TextStyle(fontSize: 11)),
            ),
          )),
        ],
      ],
    );
  }

  /// Build PDF footer
  pw.Widget _buildPdfFooter() {
    return pw.Column(
      children: [
        pw.Divider(),
        pw.SizedBox(height: 8),
        pw.Text(
          'This document was generated by ALORA - Flight Anxiety Wellness Companion',
          style: pw.TextStyle(
            fontSize: 10,
            fontStyle: pw.FontStyle.italic,
          ),
          textAlign: pw.TextAlign.center,
        ),
        pw.Text(
          'For support, visit our website or contact customer service',
          style: const pw.TextStyle(fontSize: 9),
          textAlign: pw.TextAlign.center,
        ),
      ],
    );
  }
}

/// Privacy settings model
class PrivacySettings {
  final String userId;
  final bool dataCollectionEnabled;
  final bool analyticsEnabled;
  final bool personalizedAdsEnabled;
  final int dataRetentionDays;
  final bool shareDataForResearch;
  final bool allowDataExport;
  final bool allowDataDeletion;
  final DateTime lastUpdated;

  const PrivacySettings({
    required this.userId,
    required this.dataCollectionEnabled,
    required this.analyticsEnabled,
    required this.personalizedAdsEnabled,
    required this.dataRetentionDays,
    required this.shareDataForResearch,
    required this.allowDataExport,
    required this.allowDataDeletion,
    required this.lastUpdated,
  });

  PrivacySettings copyWith({
    String? userId,
    bool? dataCollectionEnabled,
    bool? analyticsEnabled,
    bool? personalizedAdsEnabled,
    int? dataRetentionDays,
    bool? shareDataForResearch,
    bool? allowDataExport,
    bool? allowDataDeletion,
    DateTime? lastUpdated,
  }) {
    return PrivacySettings(
      userId: userId ?? this.userId,
      dataCollectionEnabled: dataCollectionEnabled ?? this.dataCollectionEnabled,
      analyticsEnabled: analyticsEnabled ?? this.analyticsEnabled,
      personalizedAdsEnabled: personalizedAdsEnabled ?? this.personalizedAdsEnabled,
      dataRetentionDays: dataRetentionDays ?? this.dataRetentionDays,
      shareDataForResearch: shareDataForResearch ?? this.shareDataForResearch,
      allowDataExport: allowDataExport ?? this.allowDataExport,
      allowDataDeletion: allowDataDeletion ?? this.allowDataDeletion,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'dataCollectionEnabled': dataCollectionEnabled,
      'analyticsEnabled': analyticsEnabled,
      'personalizedAdsEnabled': personalizedAdsEnabled,
      'dataRetentionDays': dataRetentionDays,
      'shareDataForResearch': shareDataForResearch,
      'allowDataExport': allowDataExport,
      'allowDataDeletion': allowDataDeletion,
      'lastUpdated': lastUpdated.millisecondsSinceEpoch,
    };
  }

  factory PrivacySettings.fromMap(Map<String, dynamic> map) {
    return PrivacySettings(
      userId: map['userId'] ?? '',
      dataCollectionEnabled: map['dataCollectionEnabled'] ?? true,
      analyticsEnabled: map['analyticsEnabled'] ?? true,
      personalizedAdsEnabled: map['personalizedAdsEnabled'] ?? false,
      dataRetentionDays: map['dataRetentionDays'] ?? 365,
      shareDataForResearch: map['shareDataForResearch'] ?? false,
      allowDataExport: map['allowDataExport'] ?? true,
      allowDataDeletion: map['allowDataDeletion'] ?? true,
      lastUpdated: DateTime.fromMillisecondsSinceEpoch(
        map['lastUpdated'] ?? DateTime.now().millisecondsSinceEpoch,
      ),
    );
  }
}
