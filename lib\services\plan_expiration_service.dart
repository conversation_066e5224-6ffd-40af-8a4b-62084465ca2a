import 'package:flutter/material.dart';
import '../models/user_subscription_model.dart';
import '../models/subscription_model.dart';
import '../screens/subscription/subscription_screen.dart';

class PlanExpirationService {
  
  static Future<bool> checkAndHandleExpiration(
    BuildContext context,
    UserSubscriptionModel? userSubscription,
  ) async {
    if (userSubscription == null) {
      _showUpgradeDialog(context, 'No subscription found. Please upgrade to continue.');
      return false;
    }

    final subscription = userSubscription.subscription;
    
    if (subscription.isExpired) {
      final planName = subscription.planDisplayName;
      final message = subscription.plan == SubscriptionPlan.free
          ? 'Your 7-day free trial has expired. Upgrade to continue using Alora.'
          : 'Your $planName subscription has expired. Please renew to continue.';
      
      _showUpgradeDialog(context, message);
      return false;
    }

    if (subscription.daysRemaining <= 2 && subscription.daysRemaining > 0) {
      final planName = subscription.planDisplayName;
      final daysLeft = subscription.daysRemaining;
      final message = subscription.plan == SubscriptionPlan.free
          ? 'Your free trial expires in $daysLeft day${daysLeft == 1 ? '' : 's'}. Upgrade now to continue.'
          : 'Your $planName subscription expires in $daysLeft day${daysLeft == 1 ? '' : 's'}. Renew now to avoid interruption.';
      
      _showExpirationWarningDialog(context, message);
    }

    return true;
  }

  static bool canAccessFeature(
    UserSubscriptionModel? userSubscription,
    String featureType,
  ) {
    if (userSubscription == null) return false;

    final subscription = userSubscription.subscription;
    
    if (subscription.isExpired) {
      return false;
    }

    switch (featureType) {
      case 'chat':
        return userSubscription.canUseChat;
      case 'voice':
        return userSubscription.canUseVoice;
      case 'breathing':
        return true;
      default:
        return false;
    }
  }

  static void _showUpgradeDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(Icons.warning_amber_rounded, color: Colors.orange, size: 28),
            const SizedBox(width: 12),
            const Text('Subscription Expired'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            const SizedBox(height: 16),
            const Text(
              'Choose a plan to continue:',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            _buildPlanOption('Basic Plan', '\$15/month', '13,000 chats + 80 voice'),
            _buildPlanOption('Premium Plan', '\$30/month', '20,000 chats + 300 voice'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const SubscriptionScreen(),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Upgrade Now'),
          ),
        ],
      ),
    );
  }

  static void _showExpirationWarningDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(Icons.schedule_rounded, color: Colors.blue, size: 28),
            const SizedBox(width: 12),
            const Text('Subscription Expiring'),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Remind Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const SubscriptionScreen(),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('Renew Now'),
          ),
        ],
      ),
    );
  }

  static Widget _buildPlanOption(String name, String price, String features) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(Icons.check_circle, color: Colors.green, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '$name - $price ($features)',
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  static Future<bool> blockFeatureAccess(
    BuildContext context,
    UserSubscriptionModel? userSubscription,
    String featureType,
  ) async {
    final hasValidPlan = await checkAndHandleExpiration(context, userSubscription);
    if (!hasValidPlan) {
      return false;
    }

    final canAccess = canAccessFeature(userSubscription, featureType);
    if (!canAccess) {
      _showFeatureBlockedDialog(context, featureType);
      return false;
    }

    return true;
  }

  static void _showFeatureBlockedDialog(BuildContext context, String featureType) {
    final featureName = featureType == 'chat' ? 'Chat' : 'Voice';
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(Icons.block_rounded, color: Colors.red, size: 28),
            const SizedBox(width: 12),
            Text('$featureName Unavailable'),
          ],
        ),
        content: Text(
          'You\'ve used all your $featureName credits. Upgrade your plan to continue using this feature.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const SubscriptionScreen(),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Upgrade'),
          ),
        ],
      ),
    );
  }
}
