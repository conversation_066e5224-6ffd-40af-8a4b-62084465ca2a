import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flight_fear_wellness_app/blocs/admin/admin_bloc.dart';
import 'package:flight_fear_wellness_app/blocs/admin/admin_event.dart';
import 'package:flight_fear_wellness_app/blocs/admin/admin_state.dart';
import 'package:flight_fear_wellness_app/utils/admin_guard.dart';
import 'package:flight_fear_wellness_app/utils/theme.dart';

class UsersManagementScreen extends StatefulWidget {
  const UsersManagementScreen({super.key});

  @override
  State<UsersManagementScreen> createState() => _UsersManagementScreenState();
}

class _UsersManagementScreenState extends State<UsersManagementScreen> with WidgetsBindingObserver {
  final TextEditingController _searchController = TextEditingController();
  String? _selectedPlanFilter;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    final adminBloc = context.read<AdminBloc>();

    adminBloc.add(StartRealtimeUpdates());

    if (adminBloc.state.users.isEmpty) {
      adminBloc.add(LoadAllUsers());
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _searchController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      context.read<AdminBloc>().add(LoadAllUsers());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Column(
        children: [
          _buildSearchHeader(context),
          
          Expanded(
            child: BlocListener<AdminBloc, AdminState>(
              listener: (context, state) {
                if (state.status == AdminStatus.error) {
                  _showErrorSnackBar(context, state.error ?? 'An error occurred');
                }
              },
              child: BlocBuilder<AdminBloc, AdminState>(
                builder: (context, state) {
                if (state.status == AdminStatus.loading) {
                  return const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                  )
                  );
                }

                if (state.status == AdminStatus.error) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline_rounded, size: 64, color: AppTheme.errorColor),
                        const SizedBox(height: 24),
                        Text('Error loading users:', style: Theme.of(context).textTheme.titleMedium),
                        const SizedBox(height: 8),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Text(state.error ?? 'Unknown error', 
                            textAlign: TextAlign.center,
                            style: TextStyle(color: AppTheme.textSecondary),
                        )),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: () => context.read<AdminBloc>().add(LoadAllUsers()),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                          ),),
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                final users = state.filteredUsers;
                if (users.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.search_off_rounded, size: 64, color: AppTheme.textSecondary),
                        const SizedBox(height: 16),
                        Text('No users found', style: Theme.of(context).textTheme.titleMedium),
                        const SizedBox(height: 8),
                        Text('Try adjusting your search or filters', style: TextStyle(color: AppTheme.textSecondary)),
                      ],
                    ),
                  );
                }

                return RefreshIndicator(
                  onRefresh: () async {
                    context.read<AdminBloc>().add(LoadAllUsers());
                  },
                  color: AppTheme.primaryColor,
                  child: ListView.separated(
                    padding: const EdgeInsets.all(16),
                    itemCount: users.length,
                    separatorBuilder: (context, index) => const SizedBox(height: 16),
                    itemBuilder: (context, index) {
                      final user = users[index];
                      return _buildUserCard(context, user);
                    },
                  ),
                );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 12,
            offset: const Offset(0, 4),
          )
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'User Management',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.w800,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search users by email or name...',
              prefixIcon: const Icon(Icons.search_rounded),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: AppTheme.borderColor),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: AppTheme.borderColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: AppTheme.primaryColor, width: 1.5),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            ),
            onChanged: (value) {
              context.read<AdminBloc>().add(SearchUsers(query: value));
            },
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Text('Filter by plan:', style: TextStyle(
                color: AppTheme.textSecondary,
                fontWeight: FontWeight.w500,
              )),
              const SizedBox(width: 10),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 13, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppTheme.borderColor),
                ),
                child: DropdownButton<String>(
                  value: _selectedPlanFilter,
                  hint: const Text('All Plans'),
                  underline: const SizedBox(),
                  icon: const Icon(Icons.keyboard_arrow_down_rounded),
                  borderRadius: BorderRadius.circular(12),
                  items: [
                    const DropdownMenuItem(value: null, child: Text('All Plans')),
                    const DropdownMenuItem(value: 'free', child: Text('Free')),
                    const DropdownMenuItem(value: 'basic', child: Text('Basic')),
                    const DropdownMenuItem(value: 'premium', child: Text('Premium')),
                  ],
                  onChanged: (value) {
                    setState(() => _selectedPlanFilter = value);
                    context.read<AdminBloc>().add(FilterUsersByPlan(planFilter: value));
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUserCard(BuildContext context, AdminUser user) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(14),
        side: const BorderSide(color: AppTheme.borderColor, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  backgroundColor: _getPlanColor(user.plan).withOpacity(0.1),
                  radius: 24,
                  child: Text(
                    user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w800,
                      color: _getPlanColor(user.plan),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              user.name,
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ),
                          if (_isAdminUser(user.email))
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.red.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.red),
                              ),
                              child: Text(
                                'ADMIN',
                                style: TextStyle(
                                  color: Colors.red,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w800,
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        user.email,
                        style: TextStyle(
                          color: AppTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 14),
            
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _getPlanColor(user.plan).withOpacity(0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: _getPlanColor(user.plan).withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getPlanColor(user.plan).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: _getPlanColor(user.plan)),
                    ),
                    child: Text(
                      '${user.plan.toUpperCase()} PLAN',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w800,
                        color: _getPlanColor(user.plan),
                      ),
                    ),
                  ),
                  const Spacer(),
                  _buildCreditInfo('Chat', user.credits['chat'] ?? 0, Icons.chat_bubble_outline_rounded),
                  const SizedBox(width: 16),
                  _buildCreditInfo('Voice', user.credits['voice'] ?? 0, Icons.mic_outlined),
                ],
              ),
            ),
            const SizedBox(height: 14),
            
            Row(
              children: [
                _buildActivityInfo('Last active', _formatDate(user.lastActive), Icons.access_time_rounded),
                const SizedBox(width: 20),
                _buildActivityInfo('Joined', _formatDate(user.createdAt), Icons.calendar_today_rounded),
                const Spacer(),
                _buildActionMenu(context, user),
              ],
            ),
            
            if (_isAdminUser(user.email))
              Padding(
                padding: const EdgeInsets.only(top: 12),
                child: Row(
                  children: [
                    Icon(Icons.shield_rounded, size: 16, color: Colors.red),
                    const SizedBox(width: 8),
                    Text(
                      'Administrator Account',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.red,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreditInfo(String label, int value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: AppTheme.textSecondary),
        const SizedBox(width: 4),
        Text(
          '$label: $value',
          style: TextStyle(
            fontSize: 12,
            color: AppTheme.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildActivityInfo(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: AppTheme.textSecondary),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: AppTheme.textSecondary,
              ),
            ),
            Text(
              value,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionMenu(BuildContext context, AdminUser user) {
    return PopupMenuButton<String>(
      icon: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppTheme.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(Icons.more_vert_rounded, color: AppTheme.primaryColor),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(color: AppTheme.borderColor),
      ),
      onSelected: (action) => _handleUserAction(context, user, action),
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'upgrade_basic',
          child: Row(
            children: [
              Icon(Icons.arrow_upward_rounded, size: 18, color: Colors.blue),
              const SizedBox(width: 12),
              const Text('Upgrade to Basic'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'upgrade_premium',
          child: Row(
            children: [
              Icon(Icons.star_rounded, size: 18, color: Colors.amber),
              const SizedBox(width: 12),
              const Text('Upgrade to Premium'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'downgrade_free',
          child: Row(
            children: [
              Icon(Icons.arrow_downward_rounded, size: 18, color: Colors.grey),
              const SizedBox(width: 12),
              const Text('Downgrade to Free'),
            ],
          ),
        ),
        const PopupMenuDivider(),
        PopupMenuItem(
          value: 'reset_credits',
          child: Row(
            children: [
              Icon(Icons.restart_alt_rounded, size: 18, color: AppTheme.primaryColor),
              const SizedBox(width: 12),
              const Text('Reset Credits'),
            ],
          ),
        ),
        const PopupMenuDivider(),
        PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete_outline_rounded, size: 18, color: Colors.red),
              const SizedBox(width: 12),
              const Text('Delete User'),
            ],
          ),
        ),
      ],
    );
  }

  Color _getPlanColor(String plan) {
    switch (plan) {
      case 'basic':
        return Colors.blue;
      case 'premium':
        return Colors.amber;
      default:
        return AppTheme.textSecondary;
    }
  }

  bool _isAdminUser(String email) {
    return AdminGuard.isAdminEmail(email);
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        if (difference.inMinutes == 0) {
          return 'Just now';
        }
        return '${difference.inMinutes}m ago';
      }
      return '${difference.inHours}h ago';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '${weeks}w ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '${months}mo ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '${years}y ago';
    }
  }

  void _handleUserAction(BuildContext context, AdminUser user, String action) {
    switch (action) {
      case 'upgrade_basic':
        _showConfirmationDialog(
          context,
          'Upgrade User',
          'Upgrade ${user.name} to Basic plan?',
          () => _executeUserAction(
            context,
            () => context.read<AdminBloc>().add(UpdateUserPlan(userId: user.id, newPlan: 'basic')),
            'User upgraded to Basic plan successfully',
          ),
        );
        break;
      case 'upgrade_premium':
        _showConfirmationDialog(
          context,
          'Upgrade User',
          'Upgrade ${user.name} to Premium plan?',
          () => _executeUserAction(
            context,
            () => context.read<AdminBloc>().add(UpdateUserPlan(userId: user.id, newPlan: 'premium')),
            'User upgraded to Premium plan successfully',
          ),
        );
        break;
      case 'downgrade_free':
        _showConfirmationDialog(
          context,
          'Downgrade User',
          'Downgrade ${user.name} to Free plan?',
          () => _executeUserAction(
            context,
            () => context.read<AdminBloc>().add(UpdateUserPlan(userId: user.id, newPlan: 'free')),
            'User downgraded to Free plan successfully',
          ),
        );
        break;
      case 'reset_credits':
        _showConfirmationDialog(
          context,
          'Reset Credits',
          'Reset credits for ${user.name} to plan limits?',
          () => _executeUserAction(
            context,
            () => context.read<AdminBloc>().add(ResetUserCredits(userId: user.id)),
            'User credits reset successfully',
          ),
        );
        break;
      case 'delete':
        _showDeleteConfirmationDialog(context, user);
        break;
    }
  }

  void _executeUserAction(BuildContext context, VoidCallback action, String successMessage) {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final adminBloc = context.read<AdminBloc>();

    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
            ),
            const SizedBox(width: 12),
            const Text('Processing...'),
          ],
        ),
        duration: const Duration(seconds: 2),
        backgroundColor: AppTheme.primaryColor,
      ),
    );

    action();

    Future.delayed(const Duration(milliseconds: 1500), () {
      if (mounted) {
        scaffoldMessenger.hideCurrentSnackBar();
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle_rounded, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text(successMessage)),
              ],
            ),
            backgroundColor: AppTheme.successColor,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );

        adminBloc.add(LoadAllUsers());
      }
    });
  }

  void _showConfirmationDialog(
    BuildContext context,
    String title,
    String content,
    VoidCallback onConfirm,
  ) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              Text(content, style: TextStyle(color: AppTheme.textSecondary)),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      foregroundColor: AppTheme.textSecondary,
                    ),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      onConfirm();
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: AppTheme.primaryColor,
                    ),
                    child: const Text('Confirm'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDeleteConfirmationDialog(BuildContext context, AdminUser user) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.warning_amber_rounded, size: 24, color: Colors.red),
                  const SizedBox(width: 12),
                  Text(
                    'Delete User',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text('Are you sure you want to delete ${user.name}?'),
              const SizedBox(height: 16),
              const Text(
                'This will permanently delete:',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              _buildDeleteItem('• User account and profile'),
              _buildDeleteItem('• All chat messages'),
              _buildDeleteItem('• Voice and video sessions'),
              _buildDeleteItem('• Subscription data'),
              const SizedBox(height: 16),
              const Text(
                'This action cannot be undone.',
                style: TextStyle(color: Colors.red, fontWeight: FontWeight.w700),
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      foregroundColor: AppTheme.textSecondary,
                    ),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _executeUserAction(
                        context,
                        () => context.read<AdminBloc>().add(DeleteUser(userId: user.id)),
                        'User deleted successfully',
                      );
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red,
                    ),
                    child: const Text('Delete'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDeleteItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Text(text, style: TextStyle(color: AppTheme.textSecondary)),
    );
  }

  void _showSuccessSnackBar(BuildContext context, String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle_rounded, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppTheme.successColor,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _showErrorSnackBar(BuildContext context, String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline_rounded, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppTheme.errorColor,
        duration: const Duration(seconds: 5),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}