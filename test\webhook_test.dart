import 'package:flutter_test/flutter_test.dart';
import 'package:flight_fear_wellness_app/services/webhook_processor.dart';
import 'package:flight_fear_wellness_app/models/payment_models.dart';

void main() {
  group('Webhook System Tests', () {
    test('WebhookProcessor can be instantiated', () {
      final processor = WebhookProcessor();
      expect(processor, isNotNull);
    });

    test('WebhookProcessor is singleton', () {
      final processor1 = WebhookProcessor();
      final processor2 = WebhookProcessor();
      expect(processor1, equals(processor2));
    });

    test('Payment providers are properly defined', () {
      expect(PaymentProvider.values.length, equals(3));
      expect(PaymentProvider.values, contains(PaymentProvider.lemonsqueezy));
      expect(PaymentProvider.values, contains(PaymentProvider.stripe));
      expect(PaymentProvider.values, contains(PaymentProvider.paypal));
    });

    test('Webhook processor handles provider mapping correctly', () {
      // Test that all providers have proper string representations
      for (final provider in PaymentProvider.values) {
        expect(provider.name, isNotEmpty);
        expect(provider.name, isA<String>());
      }
    });
  });
}
