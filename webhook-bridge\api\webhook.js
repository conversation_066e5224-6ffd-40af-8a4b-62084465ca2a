// Simple webhook bridge for Firebase Spark plan
// This can be deployed to Vercel, Netlify, or any free hosting service

const https = require('https');
const crypto = require('crypto');

// Firebase configuration - UPDATE THESE VALUES
const FIREBASE_PROJECT_ID = 'laki-avatar' // Replace with your actual Firebase project ID
const FIREBASE_DATABASE_URL = `https://${FIREBASE_PROJECT_ID}-default-rtdb.firebaseio.com`;
const WEBHOOK_SECRET = '211f7253476d9dc820a66447548f94d214de37e4ed264cbd7886df76fd5d09d9'; // Your actual LemonSqueezy webhook secret

/**
 * Main webhook handler function
 * This can be deployed as a serverless function
 */
module.exports = async (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-Signature');
  res.setHeader('Content-Type', 'application/json');

  try {
    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      res.status(200).end();
      return;
    }

    // Handle GET requests (health check)
    if (req.method === 'GET') {
      res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'LemonSqueezy Webhook Bridge',
        version: '1.0.0'
      });
      return;
    }

    // Handle POST requests (webhooks)
    if (req.method === 'POST') {
      await handleWebhook(req, res);
      return;
    }

    // Method not allowed
    res.status(405).json({ error: 'Method not allowed' });

  } catch (error) {
    console.error('Webhook handler error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * Handle webhook POST requests
 */
async function handleWebhook(req, res) {
  try {
    const body = JSON.stringify(req.body);
    const signature = req.headers['x-signature'];

    if (!req.body) {
      res.status(400).json({ error: 'Empty request body' });
      return;
    }

    // Extract event information
    const eventType = req.body.meta?.event_name;
    if (!eventType) {
      res.status(400).json({ error: 'Missing event_name' });
      return;
    }

    // Verify signature
    if (!verifySignature(body, signature)) {
      console.log('Invalid webhook signature');
      res.status(401).json({ error: 'Invalid signature' });
      return;
    }

    console.log(`Received ${eventType} webhook event`);

    // Store webhook event in Firebase Realtime Database
    const success = await storeWebhookEvent(eventType, req.body, signature);

    if (success) {
      res.status(200).json({
        status: 'success',
        event: eventType,
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(500).json({ error: 'Failed to store webhook event' });
    }

  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
}

/**
 * Verify webhook signature using HMAC-SHA256
 */
function verifySignature(payload, signature) {
  if (!signature) {
    console.log('No signature provided');
    return false; // In production, you might want to require signatures
  }

  try {
    const expectedSignature = crypto
      .createHmac('sha256', WEBHOOK_SECRET)
      .update(payload, 'utf8')
      .digest('hex');

    // LemonSqueezy signature format: "sha256=<hash>"
    const providedHash = signature.startsWith('sha256=') 
      ? signature.substring(7) 
      : signature;

    const isValid = crypto.timingSafeEqual(
      Buffer.from(expectedSignature, 'hex'),
      Buffer.from(providedHash, 'hex')
    );

    if (!isValid) {
      console.log('Signature mismatch');
      console.log('Expected:', expectedSignature);
      console.log('Provided:', providedHash);
    }

    return isValid;
  } catch (error) {
    console.error('Error verifying signature:', error);
    return false;
  }
}

/**
 * Store webhook event in Firebase Realtime Database
 */
async function storeWebhookEvent(eventType, data, signature) {
  return new Promise((resolve) => {
    try {
      const eventData = {
        eventType: eventType,
        data: data,
        timestamp: { '.sv': 'timestamp' },
        processed: false,
        signature: signature || null,
        receivedAt: new Date().toISOString()
      };

      // Generate a unique key for the event
      const eventKey = Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      const url = `${FIREBASE_DATABASE_URL}/webhooks/lemonsqueezy/${eventKey}.json`;

      const postData = JSON.stringify(eventData);

      const options = {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData)
        }
      };

      const req = https.request(url, options, (res) => {
        let responseData = '';
        
        res.on('data', (chunk) => {
          responseData += chunk;
        });

        res.on('end', () => {
          if (res.statusCode === 200) {
            console.log(`Successfully stored ${eventType} event in Firebase`);
            resolve(true);
          } else {
            console.error('Failed to store event in Firebase:', res.statusCode, responseData);
            resolve(false);
          }
        });
      });

      req.on('error', (error) => {
        console.error('Error storing event in Firebase:', error);
        resolve(false);
      });

      req.write(postData);
      req.end();

    } catch (error) {
      console.error('Error in storeWebhookEvent:', error);
      resolve(false);
    }
  });
}
