import 'package:equatable/equatable.dart';
import 'package:flight_fear_wellness_app/models/user_model.dart';

enum AuthStatus { unauthenticated, authenticated, loading, error, emailNotVerified }

class AuthState extends Equatable {
  final AuthStatus status;
  final UserModel? user;
  final String? error;
  final bool isPasswordResetSent;

  const AuthState({
    required this.status,
    this.user,
    this.error,
    this.isPasswordResetSent = false,
  });

  factory AuthState.initial() => const AuthState(status: AuthStatus.unauthenticated);
  factory AuthState.loading() => const AuthState(status: AuthStatus.loading);
  factory AuthState.authenticated(UserModel user) =>
      AuthState(status: AuthStatus.authenticated, user: user);
  factory AuthState.unauthenticated() => const AuthState(status: AuthStatus.unauthenticated);
  factory AuthState.error(String error) => AuthState(status: AuthStatus.error, error: error);
  factory AuthState.passwordResetSent() => const AuthState(
      status: AuthStatus.unauthenticated, isPasswordResetSent: true);
  factory AuthState.emailNotVerified() => const AuthState(status: AuthStatus.emailNotVerified);

  bool get isLoading => status == AuthStatus.loading;
  bool get isAuthenticated => status == AuthStatus.authenticated;
  bool get hasError => status == AuthStatus.error;
  bool get isEmailNotVerified => status == AuthStatus.emailNotVerified;

  @override
  List<Object?> get props => [status, user, error, isPasswordResetSent];
}