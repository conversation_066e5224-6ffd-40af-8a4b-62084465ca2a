import 'package:firebase_auth/firebase_auth.dart';
import 'package:flight_fear_wellness_app/utils/constants.dart';

class AdminGuard {
  static bool isCurrentUserAdmin() {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null || user.email == null) return false;
    
    return AppConstants.adminEmails.contains(user.email!.toLowerCase().trim());
  }
  
  static bool isAdminEmail(String email) {
    return AppConstants.adminEmails.contains(email.toLowerCase().trim());
  }
  
  static String? getCurrentAdminEmail() {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null || user.email == null) return null;
    
    final email = user.email!.toLowerCase().trim();
    return AppConstants.adminEmails.contains(email) ? email : null;
  }
  
  static void requireAdminAccess() {
    if (!isCurrentUserAdmin()) {
      throw Exception('Admin access required');
    }
  }
}
