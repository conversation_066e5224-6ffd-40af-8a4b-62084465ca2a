import '../models/payment_models.dart';
import '../models/subscription_model.dart';

/// Abstract interface defining the contract for payment providers.
///
/// This interface enables a strategy pattern for payment processing,
/// allowing the app to support multiple payment providers (Stripe, LemonSqueezy, PayPal)
/// without changing the business logic.
///
/// Implementations of this interface should be placed in lib/services/providers/
abstract class PaymentProviderInterface {
  /// Initialize the payment provider with configuration
  Future<void> initialize(PaymentConfig config);

  /// Create a new customer in the payment provider
  Future<PaymentResult> createCustomer({
    required String email,
    required String name,
    Map<String, dynamic> metadata = const {},
  });

  /// Create a subscription for a customer
  Future<PaymentResult> createSubscription({
    required String customerId,
    required SubscriptionPlan plan,
    Map<String, dynamic> metadata = const {},
  });

  /// Create a payment intent for one-time payments
  Future<PaymentResult> createPaymentIntent({
    required String customerId,
    required SubscriptionPlan plan,
    required double amount,
    required String currency,
    Map<String, dynamic> metadata = const {},
  });

  /// Retrieve subscription details by ID
  Future<ProviderSubscription?> getSubscription(String subscriptionId);

  /// Cancel an existing subscription
  Future<PaymentResult> cancelSubscription({
    required String subscriptionId,
    bool cancelAtPeriodEnd = true,
  });

  /// Update an existing subscription to a new plan
  Future<PaymentResult> updateSubscription({
    required String subscriptionId,
    required SubscriptionPlan newPlan,
  });

  /// Retrieve customer details by ID
  Future<PaymentCustomer?> getCustomer(String customerId);

  /// Get all invoices for a customer
  Future<List<PaymentInvoice>> getCustomerInvoices(String customerId);

  /// Verify webhook signature for security
  bool verifyWebhookSignature({
    required String payload,
    required String signature,
    required String secret,
  });

  /// Process incoming webhook events
  Future<PaymentResult> processWebhookEvent(WebhookEvent event);

  /// Generate checkout URL for subscription purchase
  String getCheckoutUrl({
    required SubscriptionPlan plan,
    required String customerEmail,
    String? customerId,
    Map<String, dynamic> metadata = const {},
  });

  /// Get customer portal URL for subscription management
  String? getCustomerPortalUrl(String customerId);

  /// Validate provider configuration
  bool validateConfig(PaymentConfig config);

  /// Provider name identifier
  String get providerName;

  /// List of features supported by this provider
  List<String> get supportedFeatures;
}