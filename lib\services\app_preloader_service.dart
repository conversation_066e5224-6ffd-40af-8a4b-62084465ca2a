
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'secure_api_service.dart';
import 'elevenlabs_service.dart';
import 'firebase_service.dart';

class AppPreloaderService {
  static final AppPreloaderService _instance = AppPreloaderService._internal();
  factory AppPreloaderService() => _instance;
  AppPreloaderService._internal();

  SecureAPIService? _secureAPIService;
  ElevenLabsService? _elevenLabsService;
  FirebaseService? _firebaseService;

  bool _isInitialized = false;
  final Map<String, bool> _serviceStatus = {
    'firebase': false,
    'secure_api': false,
    'elevenlabs': false,
  };

  SecureAPIService get secureAPIService => _secureAPIService ??= SecureAPIService();
  ElevenLabsService get elevenLabsService => _elevenLabsService ??= ElevenLabsService();
  FirebaseService get firebaseService => _firebaseService ??= FirebaseService();

  bool get isInitialized => _isInitialized;
  Map<String, bool> get serviceStatus => Map.from(_serviceStatus);

  Future<void> initializeAllServices({
    Function(String message, double progress)? onProgress,
  }) async {
    if (_isInitialized) return;

    try {
      onProgress?.call("🔐 Connecting to secure servers...", 0.20);
      await _initializeFirebase();
      await Future.delayed(const Duration(milliseconds: 1200));

      onProgress?.call("🧠 Preparing AI conversation engine...", 0.40);
      await _initializeSecureAPI();
      await Future.delayed(const Duration(milliseconds: 1200));

      onProgress?.call("🎤 Loading voice synthesis models...", 0.80);
      await _initializeElevenLabs();
      await Future.delayed(const Duration(milliseconds: 1200));

      onProgress?.call("✨ Finalizing setup...", 1.0);
      await Future.delayed(const Duration(milliseconds: 800));
      
      _isInitialized = true;
      onProgress?.call("Welcome to your journey with Alora!", 1.0);
      
    } catch (e) {
      debugPrint('Service initialization error: $e');
      _isInitialized = true;
    }
  }

  Future<void> _initializeFirebase() async {
    try {
      _firebaseService = FirebaseService();
      await Future.delayed(const Duration(milliseconds: 500));
      _serviceStatus['firebase'] = true;
    } catch (e) {
      debugPrint('Firebase initialization failed: $e');
      _serviceStatus['firebase'] = false;
    }
  }

  Future<void> _initializeSecureAPI() async {
    try {
      _secureAPIService = SecureAPIService();

      _serviceStatus['secure_api'] = true;
    } catch (e) {
      debugPrint('Secure API initialization failed: $e');
      _serviceStatus['secure_api'] = false;
    }
  }

  Future<void> _initializeElevenLabs() async {
    try {
      _elevenLabsService = ElevenLabsService();

      _serviceStatus['elevenlabs'] = true;
    } catch (e) {
      debugPrint('ElevenLabs initialization failed: $e');
      _serviceStatus['elevenlabs'] = false;
    }
  }



  bool isServiceReady(String serviceName) {
    return _serviceStatus[serviceName] ?? false;
  }

  double get initializationProgress {
    final readyServices = _serviceStatus.values.where((status) => status).length;
    return readyServices / _serviceStatus.length;
  }

  void reset() {
    _isInitialized = false;
    _serviceStatus.updateAll((key, value) => false);
    _secureAPIService = null;
    _elevenLabsService = null;
    _firebaseService = null;
  }
}

class LoadingMessages {
  static const List<String> motivationalQuotes = [
    "✨ Every journey begins with a single step...",
    "🌟 Courage is not the absence of fear, but action in spite of it",
    "💪 You're stronger than your fears",
    "🦋 Transformation happens one breath at a time",
    "🌈 After every storm comes a rainbow",
  ];

  static const List<String> loadingSteps = [
    "Initializing Alora's consciousness...",
    "Connecting to secure therapy servers...",
    "Preparing Alora's brain for conversations...",
    "Tuning Alora's voice for therapy sessions...",
    "Calibrating anxiety detection algorithms...",
    "Loading personalized therapy techniques...",
    "Establishing secure connection...",
    "Almost ready for your journey...",
    "Welcome to your transformation!",
  ];

  static String getRandomQuote() {
    final random = DateTime.now().millisecondsSinceEpoch % motivationalQuotes.length;
    return motivationalQuotes[random];
  }

  static String getLoadingStep(double progress) {
    final index = (progress * (loadingSteps.length - 1)).round();
    return loadingSteps[index.clamp(0, loadingSteps.length - 1)];
  }
}

class ServiceHealthChecker {
  static Future<Map<String, bool>> checkAllServices() async {
    final preloader = AppPreloaderService();
    final results = <String, bool>{};
    
    results['firebase'] = await _checkFirebaseHealth(preloader.firebaseService);
    results['secure_api'] = await _checkSecureAPIHealth(preloader.secureAPIService);
    results['elevenlabs'] = await _checkElevenLabsHealth(preloader.elevenLabsService);
    
    return results;
  }

  static Future<bool> _checkFirebaseHealth(FirebaseService service) async {
    try {
      return true;
    } catch (e) {
      return false;
    }
  }

  static Future<bool> _checkSecureAPIHealth(SecureAPIService service) async {
    try {
      return true;
    } catch (e) {
      return false;
    }
  }

  static Future<bool> _checkElevenLabsHealth(ElevenLabsService service) async {
    try {
      return true;
    } catch (e) {
      return false;
    }
  }


}
