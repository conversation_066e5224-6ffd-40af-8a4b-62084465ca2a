import 'package:equatable/equatable.dart';
import '../../models/notification_model.dart';

abstract class NotificationEvent extends Equatable {
  const NotificationEvent();

  @override
  List<Object?> get props => [];
}

class LoadNotifications extends NotificationEvent {
  final int? limit;
  
  const LoadNotifications({this.limit});
  
  @override
  List<Object?> get props => [limit];
}

class NotificationsUpdated extends NotificationEvent {
  final List<NotificationModel> notifications;
  
  const NotificationsUpdated(this.notifications);
  
  @override
  List<Object?> get props => [notifications];
}

class UnreadCountUpdated extends NotificationEvent {
  final int count;
  
  const UnreadCountUpdated(this.count);
  
  @override
  List<Object?> get props => [count];
}

class MarkNotificationAsRead extends NotificationEvent {
  final String notificationId;
  
  const MarkNotificationAsRead(this.notificationId);
  
  @override
  List<Object?> get props => [notificationId];
}

class MarkAllNotificationsAsRead extends NotificationEvent {
  const MarkAllNotificationsAsRead();
}

class DeleteNotification extends NotificationEvent {
  final String notificationId;
  
  const DeleteNotification(this.notificationId);
  
  @override
  List<Object?> get props => [notificationId];
}

class DeleteAllNotifications extends NotificationEvent {
  const DeleteAllNotifications();
}

class RefreshNotifications extends NotificationEvent {
  const RefreshNotifications();
}

class CreateNotification extends NotificationEvent {
  final NotificationModel notification;
  
  const CreateNotification(this.notification);
  
  @override
  List<Object?> get props => [notification];
}
