import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../models/export_summary_model.dart';
import '../models/memory_insights_model.dart';

class ExportHistoryService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Get current user ID
  static String? get _currentUserId => _auth.currentUser?.uid;

  /// Collection reference for export history
  static CollectionReference get _exportHistoryCollection =>
      _firestore.collection('user_export_history');

  /// Save export summary to history
  static Future<void> saveExportSummary(ExportSummary summary) async {
    if (_currentUserId == null) throw Exception('User not authenticated');

    await _exportHistoryCollection.doc(summary.id).set(summary.toMap());
  }

  /// Get user's export history
  static Future<List<ExportSummary>> getUserExportHistory({int limit = 10}) async {
    if (_currentUserId == null) throw Exception('User not authenticated');

    // Get all user exports without ordering to avoid index requirement
    final query = await _exportHistoryCollection
        .where('userId', isEqualTo: _currentUserId)
        .get();

    // Sort and limit in memory to avoid composite index requirement
    final allExports = query.docs
        .map((doc) => ExportSummary.fromMap(doc.data() as Map<String, dynamic>))
        .toList();

    // Sort by export date descending
    allExports.sort((a, b) => b.exportDate.compareTo(a.exportDate));

    // Apply limit
    return allExports.take(limit).toList();
  }

  /// Get last export for comparison
  static Future<ExportSummary?> getLastExport() async {
    if (_currentUserId == null) return null;

    try {
      // Get all user exports without ordering to avoid index requirement
      final query = await _exportHistoryCollection
          .where('userId', isEqualTo: _currentUserId)
          .get();

      if (query.docs.isEmpty) return null;

      // Sort and filter in memory to avoid composite index requirement
      final allExports = query.docs
          .map((doc) => ExportSummary.fromMap(doc.data() as Map<String, dynamic>))
          .toList();

      // Sort by export date descending
      allExports.sort((a, b) => b.exportDate.compareTo(a.exportDate));

      // Filter for 'ready' status
      final readyExports = allExports
          .where((export) => export.status == 'ready')
          .toList();

      return readyExports.isNotEmpty ? readyExports.first : null;
    } catch (e) {
      debugPrint('ExportHistoryService: Error getting last export: $e');
      return null;
    }
  }

  /// Update export status
  static Future<void> updateExportStatus(String exportId, String status, {String? filePath}) async {
    final updateData = {'status': status};
    if (filePath != null) {
      updateData['filePath'] = filePath;
    }

    await _exportHistoryCollection.doc(exportId).update(updateData);
  }

  /// Generate progress comparison between current and previous export
  static ProgressComparison? generateProgressComparison(
    MemoryInsights current,
    MemoryInsights previous,
  ) {
    // Calculate changes
    final anxietyChange = current.averageAnxietyLevel - previous.averageAnxietyLevel;
    final sessionChange = current.totalSessions - previous.totalSessions;
    final copingChange = current.copingSuccessRate - previous.copingSuccessRate;

    // Determine new strengths
    final newStrengths = <String>[];
    current.successfulStrategies.forEach((strategy, usageCount) {
      final previousCount = previous.successfulStrategies[strategy] ?? 0;
      if (usageCount > previousCount + 2) {
        newStrengths.add(strategy);
      }
    });

    // Determine improved areas
    final improvedAreas = <String>[];
    if (anxietyChange < -0.5) improvedAreas.add('Anxiety Management');
    if (copingChange > 0.1) improvedAreas.add('Coping Strategies');
    if (sessionChange > 5) improvedAreas.add('Engagement');

    // Determine concern areas
    final concernAreas = <String>[];
    if (anxietyChange > 0.5) concernAreas.add('Anxiety Levels');
    if (copingChange < -0.1) concernAreas.add('Coping Effectiveness');
    if (sessionChange < -3) concernAreas.add('App Usage');

    // Determine overall trend
    String overallTrend;
    if (anxietyChange < -0.3 && copingChange > 0.05) {
      overallTrend = 'improving';
    } else if (anxietyChange > 0.3 || copingChange < -0.1) {
      overallTrend = 'declining';
    } else {
      overallTrend = 'stable';
    }

    // Generate summary message
    String summaryMessage;
    switch (overallTrend) {
      case 'improving':
        summaryMessage = 'Great progress! Your anxiety levels have decreased and coping strategies are more effective.';
        break;
      case 'declining':
        summaryMessage = 'We notice some challenges. Consider increasing your session frequency or trying new coping strategies.';
        break;
      default:
        summaryMessage = 'You\'re maintaining steady progress. Keep up the consistent work!';
    }

    return ProgressComparison(
      anxietyLevelChange: anxietyChange,
      sessionCountChange: sessionChange,
      copingSuccessRateChange: copingChange,
      goalsCompletedChange: 0, // Will be calculated from therapy progress
      newStrengths: newStrengths,
      improvedAreas: improvedAreas,
      concernAreas: concernAreas,
      overallTrend: overallTrend,
      summaryMessage: summaryMessage,
    );
  }

  /// Create export ID
  static String createExportId(String userId) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${userId}_$timestamp';
  }

  /// Check if user has pending exports
  static Future<List<ExportSummary>> getPendingExports() async {
    if (_currentUserId == null) return [];

    try {
      // Get all user exports and filter in memory to avoid index requirement
      final query = await _exportHistoryCollection
          .where('userId', isEqualTo: _currentUserId)
          .get();

      return query.docs
          .map((doc) => ExportSummary.fromMap(doc.data() as Map<String, dynamic>))
          .where((export) => export.status == 'generating')
          .toList();
    } catch (e) {
      // If query fails, return empty list
      return [];
    }
  }

  /// Delete old exports (keep only last 20)
  static Future<void> cleanupOldExports() async {
    if (_currentUserId == null) return;

    try {
      // Get all user exports without ordering to avoid index requirement
      final query = await _exportHistoryCollection
          .where('userId', isEqualTo: _currentUserId)
          .get();

      if (query.docs.length > 20) {
        // Sort in memory by export date descending
        final allExports = query.docs
            .map((doc) => {
              'doc': doc,
              'exportDate': (doc.data() as Map<String, dynamic>)['exportDate'] as Timestamp?
            })
            .toList();

        allExports.sort((a, b) {
          final dateA = a['exportDate'] as Timestamp?;
          final dateB = b['exportDate'] as Timestamp?;
          if (dateA == null || dateB == null) return 0;
          return dateB.compareTo(dateA); // Descending order
        });

        // Delete old exports (keep only first 20)
        final batch = _firestore.batch();
        for (int i = 20; i < allExports.length; i++) {
          final doc = allExports[i]['doc'] as DocumentSnapshot;
          batch.delete(doc.reference);
        }
        await batch.commit();
      }
    } catch (e) {
      debugPrint('ExportHistoryService: Error cleaning up old exports: $e');
    }
  }
}
