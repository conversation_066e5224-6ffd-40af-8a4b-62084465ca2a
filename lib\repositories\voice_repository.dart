import 'dart:async';
import 'dart:io';
import 'package:audioplayers/audioplayers.dart';
import 'package:flight_fear_wellness_app/models/api_response.dart';
import 'package:flight_fear_wellness_app/models/chat_message.dart';
import 'package:flight_fear_wellness_app/models/user_memory_model.dart';
import 'package:flight_fear_wellness_app/services/elevenlabs_service.dart';
import 'package:flight_fear_wellness_app/services/secure_api_service.dart';
import 'package:flight_fear_wellness_app/services/crisis_intervention_service.dart';
import 'package:flight_fear_wellness_app/services/user_memory_service.dart';
import 'package:flight_fear_wellness_app/services/memory_analysis_service.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flight_fear_wellness_app/providers/subscription_provider.dart';

class VoiceRepository {
  final ElevenLabsService elevenLabsService;
  final AudioPlayer audioPlayer = AudioPlayer();
  final SecureAPIService _secureAPIService = SecureAPIService();
  final UserMemoryService _memoryService = UserMemoryService();
  final MemoryAnalysisService _analysisService = MemoryAnalysisService();
  final SubscriptionProvider? subscriptionProvider;

  VoiceRepository({
    required this.elevenLabsService,
    this.subscriptionProvider,
  });

  Future<MoodBasedVoiceResponse> generateVoiceAIResponse(
    String userId,
    String input,
    {List<ChatMessage>? conversationHistory}
  ) async {
    try {
      // Get user memory for context
      final memoryResponse = await _memoryService.getUserMemory(userId);
      final userMemory = memoryResponse.success ? memoryResponse.data : null;

      final historyForAPI = conversationHistory?.map((msg) => {
        'role': msg.type == MessageType.user ? 'user' : 'assistant',
        'content': msg.content,
      }).toList() ?? [];

      // Add user memory context to conversation history
      final contextualHistory = userMemory != null
          ? _addUserContextToHistory(historyForAPI, userMemory)
          : historyForAPI;

      final response = await _secureAPIService.processVoiceChatRequest(
        message: input,
        conversationHistory: contextualHistory,
      );

      if (!response['success']) {
        throw Exception(response['error'] ?? 'Failed to generate AI response');
      }

      final aiResponse = response['message'];
      final moodLevel = response['moodLevel'] ?? 'normal';

      if (subscriptionProvider != null && response['updatedCredits'] != null) {
        print('VoiceRepository: Updating subscription provider with chat credit data');
        print('VoiceRepository: Chat credit data received: ${response['updatedCredits']}');
        subscriptionProvider!.updateCreditsDirectly(response['updatedCredits']);
      } else {
        print('VoiceRepository: Warning - No subscription provider or chat credit data available');
      }

      final interventionOptions = CrisisInterventionService.getCrisisOptions(moodLevel);

      final shouldOfferDebate = CrisisInterventionService.shouldOfferDebate(input, historyForAPI);

      // Analyze conversation for memory insights
      if (conversationHistory != null) {
        final userMessage = ChatMessage(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          content: input,
          type: MessageType.user,
          timestamp: DateTime.now(),
          userId: userId,
        );
        final aiMessage = ChatMessage(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          content: aiResponse,
          type: MessageType.ai,
          timestamp: DateTime.now(),
          userId: userId,
        );
        final allMessages = [...conversationHistory, userMessage, aiMessage];
        await _analysisService.analyzeConversation(userId, allMessages, isVoiceSession: true);
      }

      return MoodBasedVoiceResponse(
        message: aiResponse,
        moodLevel: moodLevel,
        interventionOptions: interventionOptions,
        shouldOfferDebate: shouldOfferDebate,
        conversationId: DateTime.now().millisecondsSinceEpoch.toString(),
        tokensUsed: response['usage']?['total_tokens'] ?? 0,
      );
    } catch (e) {
      throw Exception('Voice AI response failed: $e');
    }
  }

  Future<ChatResponse> getAIResponse(String userId, String input, {List<ChatMessage>? conversationHistory}) async {
    try {
      final historyForAPI = conversationHistory?.map((msg) => {
        'role': msg.type == MessageType.user ? 'user' : 'assistant',
        'content': msg.content,
      }).toList() ?? [];

      final response = await _secureAPIService.processChatRequest(
        message: input,
        conversationHistory: historyForAPI,
      );

      if (!response['success']) {
        throw Exception(response['error'] ?? 'Failed to generate AI response');
      }

      if (subscriptionProvider != null && response['updatedCredits'] != null) {
        print('VoiceRepository: Updating subscription provider with chat credit data');
        print('VoiceRepository: Chat credit data received: ${response['updatedCredits']}');
        subscriptionProvider!.updateCreditsDirectly(response['updatedCredits']);
      } else {
        print('VoiceRepository: Warning - No subscription provider or chat credit data available');
      }

      return ChatResponse(
        message: response['message'],
        conversationId: DateTime.now().millisecondsSinceEpoch.toString(),
        tokensUsed: response['usage']?['total_tokens'] ?? 0,
      );
    } catch (e) {
      throw Exception('Voice AI response failed: $e');
    }
  }

  static Directory? _cachedDirectory;

  Future<VoiceResponse> getVoiceResponseOptimized(String userId, String input, {List<ChatMessage>? conversationHistory}) async {
    try {
      final startTime = DateTime.now();

      _cachedDirectory ??= await getTemporaryDirectory();
      final filePath = '${_cachedDirectory!.path}/voice_${DateTime.now().millisecondsSinceEpoch}.mp3';

      final historyForAPI = conversationHistory?.map((msg) => {
        'role': msg.type == MessageType.user ? 'user' : 'assistant',
        'content': msg.content,
      }).toList() ?? [];

      final response = await _secureAPIService.processVoiceCombinedRequest(
        message: input,
        conversationHistory: historyForAPI,
      );

      if (!response['success']) {
        throw Exception(response['error'] ?? 'Voice generation failed');
      }

      final audioData = response['audioData'] as List<int>;
      final file = File(filePath);
      await file.writeAsBytes(audioData, flush: true);

      final totalTime = DateTime.now().difference(startTime).inMilliseconds;
      print('🚀 VOICE REPOSITORY TIME: ${totalTime}ms');

      if (subscriptionProvider != null && response['updatedCredits'] != null) {
        subscriptionProvider!.updateCreditsDirectly(response['updatedCredits']);
      }

      return VoiceResponse(
        message: response['message'],
        audioFilePath: file.path,
        conversationId: DateTime.now().millisecondsSinceEpoch.toString(),
        tokensUsed: response['usage']?['total_tokens'] ?? 0,
        characters: response['characters'] ?? 0,
      );
    } catch (e) {
      throw Exception('Voice failed: ${e.toString().replaceAll('Exception: ', '')}');
    }
  }

  Future<String> textToSpeech(String text) async {
    try {
      final response = await _secureAPIService.processVoiceRequest(text: text);

      if (!response['success']) {
        throw Exception(response['error'] ?? 'Failed to generate voice');
      }

      final audioData = response['audioData'] as List<int>;
      final directory = await getTemporaryDirectory();
      final file = File('${directory.path}/voice_${DateTime.now().millisecondsSinceEpoch}.mp3');
      await file.writeAsBytes(audioData);

      if (subscriptionProvider != null && response['updatedCredits'] != null) {
        print('VoiceRepository: Updating subscription provider with new credit data');
        print('VoiceRepository: Credit data received: ${response['updatedCredits']}');
        subscriptionProvider!.updateCreditsDirectly(response['updatedCredits']);
      } else {
        print('VoiceRepository: Warning - No subscription provider or credit data available');
        if (subscriptionProvider == null) {
          print('VoiceRepository: Subscription provider is null');
        }
        if (response['updatedCredits'] == null) {
          print('VoiceRepository: Updated credits data is null');
        }
      }

      return file.path;
    } catch (e) {
      throw Exception('Voice generation failed: $e');
    }
  }

  Future<void> playAudio(String filePath) async {
    try {
      audioPlayer.stop();

      await Future.wait([
        audioPlayer.setVolume(1.0),
        audioPlayer.setPlaybackRate(1.0),
      ]);

      await audioPlayer.play(DeviceFileSource(filePath));
    } catch (e) {
      throw Exception('Audio playback failed: $e');
    }
  }

  Future<void> stopAudio() async {
    try {
      await audioPlayer.stop();
    } catch (e) {
    }
  }

  Future<Duration?> getAudioDuration(String filePath) async {
    try {
      await audioPlayer.setSource(DeviceFileSource(filePath));
      return await audioPlayer.getDuration();
    } catch (e) {
      return null;
    }
  }

  /// Add user memory context to conversation history
  List<Map<String, String>> _addUserContextToHistory(
    List<Map<String, String>> history,
    UserMemoryModel userMemory,
  ) {
    final contextMessage = _buildContextMessage(userMemory);
    if (contextMessage.isEmpty) return history;

    // Add context as a system message at the beginning
    return [
      {'role': 'system', 'content': contextMessage},
      ...history,
    ];
  }

  /// Build context message from user memory
  String _buildContextMessage(UserMemoryModel userMemory) {
    final contextParts = <String>[];

    // Personal information context
    if (userMemory.personalInfo.preferredName != null) {
      contextParts.add('User prefers to be called: ${userMemory.personalInfo.preferredName}');
    }

    if (userMemory.personalInfo.phobias.isNotEmpty) {
      contextParts.add('Known phobias/fears: ${userMemory.personalInfo.phobias.join(', ')}');
    }

    if (userMemory.personalInfo.copingMechanisms.isNotEmpty) {
      contextParts.add('Effective coping mechanisms: ${userMemory.personalInfo.copingMechanisms.join(', ')}');
    }

    // Aviation-specific context
    if (userMemory.aviationData.flightFears.isNotEmpty) {
      final fears = userMemory.aviationData.flightFears.entries
          .map((e) => '${e.key} (severity: ${e.value}/10)')
          .join(', ');
      contextParts.add('Flight-specific fears: $fears');
    }

    if (userMemory.aviationData.upcomingFlights.isNotEmpty) {
      final upcomingFlight = userMemory.aviationData.upcomingFlights.first;
      contextParts.add('Upcoming flight: ${upcomingFlight.departureAirport} to ${upcomingFlight.arrivalAirport} on ${upcomingFlight.flightDate.toString().split(' ')[0]}');
    }

    if (userMemory.aviationData.successfulCopingStrategies.isNotEmpty) {
      contextParts.add('Previously successful strategies: ${userMemory.aviationData.successfulCopingStrategies.join(', ')}');
    }

    // Behavioral patterns context
    if (userMemory.behavioralPatterns.preferredCopingMethods.isNotEmpty) {
      contextParts.add('Preferred coping methods: ${userMemory.behavioralPatterns.preferredCopingMethods.join(', ')}');
    }

    // Therapy progress context
    if (userMemory.therapyProgress.goals.isNotEmpty) {
      final activeGoals = userMemory.therapyProgress.goals
          .where((goal) => !goal.isCompleted)
          .map((goal) => goal.title)
          .join(', ');
      if (activeGoals.isNotEmpty) {
        contextParts.add('Current therapy goals: $activeGoals');
      }
    }

    if (contextParts.isEmpty) return '';

    return 'User Context: ${contextParts.join('. ')}.';
  }
}