import 'dart:io';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/memory_insights_model.dart';
import '../models/export_summary_model.dart';
import '../models/user_memory_model.dart';
import '../models/user_model.dart';

class IntelligentPdfGenerator {
  /// Generate intelligent wellness report PDF
  static Future<String> generateWellnessReport({
    required MemoryInsights insights,
    required UserMemoryModel userMemory,
    ProgressComparison? progressComparison,
  }) async {
    final pdf = pw.Document();

    // Get real user data from Firebase Auth and Firestore
    final realUserData = await _getRealUserData();
    final userName = realUserData?.name ?? _getUserName(userMemory);
    final userEmail = realUserData?.email ?? _getUserEmail(userMemory);

    // Debug: Print actual data being used
    print('PDF Generation Debug:');
    print('- User Name: $userName');
    print('- User Email: $userEmail');
    print('- Total Sessions: ${insights.totalSessions}');
    print('- Voice Sessions: ${insights.voiceSessions}');
    print('- Chat Sessions: ${insights.chatSessions}');
    print('- Average Anxiety: ${insights.averageAnxietyLevel}');
    print('- Weekly Trends: ${insights.weeklyAnxietyTrends}');
    print('- Detected Phobias: ${insights.detectedPhobias}');
    print('- Flight History: ${insights.flightHistory.length} flights');

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (context) => [
          _buildCoverSection(userName, insights.generatedAt),
          pw.SizedBox(height: 20),
          _buildPersonalSummary(userMemory, insights, realUserData),
          pw.SizedBox(height: 20),
          _buildAviationInsights(insights),
          pw.SizedBox(height: 20),
          _buildBehavioralPatterns(insights, userMemory),
          pw.SizedBox(height: 20),
          _buildTherapyProgress(userMemory, insights),
          if (progressComparison != null) ...[
            pw.SizedBox(height: 20),
            _buildProgressComparison(progressComparison),
          ],
          pw.SizedBox(height: 20),
          _buildPrivacyDisclosure(),
        ],
      ),
    );

    // Save PDF
    final directory = await getApplicationDocumentsDirectory();
    final fileName = 'ALORA_Wellness_Report_${DateTime.now().toString().split(' ')[0]}.pdf';
    final file = File('${directory.path}/$fileName');
    await file.writeAsBytes(await pdf.save());

    return file.path;
  }

  /// Get real user data from Firebase Auth and Firestore
  static Future<UserModel?> _getRealUserData() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return null;

      // Always use Firebase Auth data as primary source
      final authName = currentUser.displayName;
      final authEmail = currentUser.email;

      print('Firebase Auth data - Name: $authName, Email: $authEmail');

      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) {
        // Create user model from Firebase Auth data
        return UserModel(
          id: currentUser.uid,
          email: authEmail ?? 'No email available',
          name: authName ?? 'ALORA User',
          createdAt: currentUser.metadata.creationTime ?? DateTime.now(),
          lastActive: DateTime.now(),
        );
      }

      // Merge Firestore data with Auth data, prioritizing Auth for name/email
      final firestoreData = userDoc.data()!;
      return UserModel(
        id: currentUser.uid,
        email: authEmail ?? firestoreData['email'] ?? 'No email available',
        name: authName ?? firestoreData['name'] ?? 'ALORA User',
        createdAt: (firestoreData['createdAt'] as Timestamp?)?.toDate() ??
                   currentUser.metadata.creationTime ?? DateTime.now(),
        lastActive: (firestoreData['lastActive'] as Timestamp?)?.toDate() ?? DateTime.now(),
      );
    } catch (e) {
      print('Error getting real user data: $e');
      return null;
    }
  }

  /// Get user name with priority fallback
  static String _getUserName(UserMemoryModel userMemory) {
    // Priority 1: Preferred name from personal info
    if (userMemory.personalInfo.preferredName?.isNotEmpty == true) {
      return userMemory.personalInfo.preferredName!;
    }

    // Priority 2: Extract from conversation topics (simple NLP)
    for (final topic in userMemory.behavioralPatterns.conversationTopics) {
      final nameMatch = _extractNameFromText(topic);
      if (nameMatch != null) return nameMatch;
    }

    // Fallback: Anonymous user
    return 'ALORA User';
  }

  /// Get user email with fallback
  static String _getUserEmail(UserMemoryModel userMemory) {
    // Try to extract email from conversation topics
    for (final topic in userMemory.behavioralPatterns.conversationTopics) {
      final emailMatch = _extractEmailFromText(topic);
      if (emailMatch != null) return emailMatch;
    }

    // Fallback: placeholder
    return '<EMAIL>';
  }

  /// Simple name extraction from text
  static String? _extractNameFromText(String text) {
    final patterns = [
      RegExp(r"my name is (\w+)", caseSensitive: false),
      RegExp(r"i'm (\w+)", caseSensitive: false),
      RegExp(r"call me (\w+)", caseSensitive: false),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(text);
      if (match != null && match.group(1) != null) {
        return match.group(1)!;
      }
    }
    return null;
  }

  /// Simple email extraction from text
  static String? _extractEmailFromText(String text) {
    final emailPattern = RegExp(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b');
    final match = emailPattern.firstMatch(text);
    return match?.group(0);
  }

  /// Build cover page section
  static pw.Widget _buildCoverSection(String userName, DateTime exportDate) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.center,
      children: [
        pw.Text(
          'ALORA',
          style: pw.TextStyle(
            fontSize: 32,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.Text(
          'Flight Fear Wellness App',
          style: pw.TextStyle(
            fontSize: 16,
            fontStyle: pw.FontStyle.italic,
          ),
        ),
        pw.SizedBox(height: 40),
        pw.Text(
          'Your Wellness Memory Export',
          style: pw.TextStyle(
            fontSize: 24,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 20),
        pw.Text(
          userName,
          style: pw.TextStyle(
            fontSize: 20,
          ),
        ),
        pw.SizedBox(height: 40),
        pw.Container(
          padding: const pw.EdgeInsets.all(16),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(),
            borderRadius: pw.BorderRadius.circular(8),
          ),
          child: pw.Column(
            children: [
              pw.Text(
                'Export Information',
                style: pw.TextStyle(
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 8),
              pw.Text('Date: ${exportDate.day}/${exportDate.month}/${exportDate.year}', style: const pw.TextStyle(fontSize: 12)),
              pw.Text('Time: ${exportDate.hour}:${exportDate.minute.toString().padLeft(2, '0')}', style: const pw.TextStyle(fontSize: 12)),
              pw.Text('Provider: ALORA', style: const pw.TextStyle(fontSize: 12)),
            ],
          ),
        ),
        pw.SizedBox(height: 20),
        pw.Container(
          padding: const pw.EdgeInsets.all(16),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(),
            borderRadius: pw.BorderRadius.circular(8),
          ),
          child: pw.Text(
            'Confidentiality Note: This document contains personal data. Handle with care.',
            style: pw.TextStyle(
              fontSize: 12,
              fontStyle: pw.FontStyle.italic,
            ),
            textAlign: pw.TextAlign.center,
          ),
        ),
      ],
    );
  }

  /// Build personal information summary (minimal as per requirements)
  static pw.Widget _buildPersonalSummary(UserMemoryModel userMemory, MemoryInsights insights, UserModel? realUserData) {
    final userName = realUserData?.name ?? _getUserName(userMemory);
    final userEmail = realUserData?.email ?? _getUserEmail(userMemory);

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Personal Information Summary'),
        pw.SizedBox(height: 12),

        // User identification (minimal as per requirements)
        pw.Text('User Name: $userName', style: const pw.TextStyle(fontSize: 12)),
        pw.Text('Email: $userEmail', style: const pw.TextStyle(fontSize: 12)),
        pw.SizedBox(height: 8),

        // Basic usage summary
        pw.Text('Report Generated: ${insights.generatedAt.toString().split(' ')[0]}', style: const pw.TextStyle(fontSize: 11)),
        pw.Text('Total App Sessions: ${insights.totalSessions}', style: const pw.TextStyle(fontSize: 11)),
        if (insights.totalSessions > 0) ...[
          pw.Text('Voice Sessions: ${insights.voiceSessions}, Chat Sessions: ${insights.chatSessions}', style: const pw.TextStyle(fontSize: 11)),
        ],
        pw.SizedBox(height: 16),

        // Smart-detected phobias from chat history
        if (insights.detectedPhobias.isNotEmpty) ...[
          pw.Text('Known Phobias:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
          pw.SizedBox(height: 4),
          ...insights.detectedPhobias.entries.map((entry) =>
            pw.Padding(
              padding: const pw.EdgeInsets.only(left: 16, bottom: 2),
              child: pw.Text('- ${entry.key}', style: const pw.TextStyle(fontSize: 11)),
            ),
          ),
          pw.SizedBox(height: 12),
        ],

        // Smart-detected medical notes from conversations
        if (insights.medicalNotes.isNotEmpty) ...[
          pw.Text('Medical Notes:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
          pw.SizedBox(height: 4),
          ...insights.medicalNotes.map((note) =>
            pw.Padding(
              padding: const pw.EdgeInsets.only(left: 16, bottom: 2),
              child: pw.Text('- $note', style: const pw.TextStyle(fontSize: 11)),
            ),
          ),
          pw.SizedBox(height: 12),
        ],

        // Coping mechanisms that work
        if (insights.successfulStrategies.isNotEmpty) ...[
          pw.Text('Coping Mechanisms:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
          pw.SizedBox(height: 4),
          ...insights.successfulStrategies.entries.take(5).map((entry) =>
            pw.Padding(
              padding: const pw.EdgeInsets.only(left: 16, bottom: 2),
              child: pw.Text('• ${entry.key}', style: const pw.TextStyle(fontSize: 11)),
            ),
          ),
          pw.SizedBox(height: 12),
        ],

        // Detected triggers
        if (insights.detectedTriggers.isNotEmpty) ...[
          pw.Text('Triggers:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
          pw.SizedBox(height: 4),
          ...insights.detectedTriggers.entries.take(5).map((entry) =>
            pw.Padding(
              padding: const pw.EdgeInsets.only(left: 16, bottom: 2),
              child: pw.Text('• ${entry.key}', style: const pw.TextStyle(fontSize: 11)),
            ),
          ),
        ],
      ],
    );
  }

  /// Build aviation-related insights
  static pw.Widget _buildAviationInsights(MemoryInsights insights) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Aviation-Related Insights'),
        pw.SizedBox(height: 12),
        
        if (insights.flightHistory.isNotEmpty) ...[
          pw.Text('✈ Flight History:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
          pw.SizedBox(height: 8),
          ...insights.flightHistory.take(3).map((flight) => 
            pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 8),
              padding: const pw.EdgeInsets.all(8),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(),
                borderRadius: pw.BorderRadius.circular(4),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text('Flight Date: ${flight.flightDate.day}/${flight.flightDate.month}/${flight.flightDate.year}', 
                         style: const pw.TextStyle(fontSize: 11)),
                  pw.Text('Route: ${flight.route}', style: const pw.TextStyle(fontSize: 11)),
                  pw.Text('Anxiety Level: ${flight.anxietyLevel.toStringAsFixed(1)}/10', 
                         style: const pw.TextStyle(fontSize: 11)),
                  if (flight.strategiesUsed.isNotEmpty)
                    pw.Text('Used Strategy: ${flight.strategiesUsed.join(", ")}', 
                           style: const pw.TextStyle(fontSize: 11)),
                ],
              ),
            ),
          ),
          pw.SizedBox(height: 12),
        ],
        
        if (insights.detectedPhobias.isNotEmpty) ...[
          pw.Text('😟 Flight-Specific Fears:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
          pw.SizedBox(height: 4),
          ...insights.detectedPhobias.entries.map((entry) => 
            pw.Padding(
              padding: const pw.EdgeInsets.only(left: 16, bottom: 2),
              child: pw.Text('• ${entry.key}', style: const pw.TextStyle(fontSize: 11)),
            ),
          ),
          pw.SizedBox(height: 12),
        ],
        
        if (insights.successfulStrategies.isNotEmpty) ...[
          pw.Text('✅ Successful Strategies:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
          pw.SizedBox(height: 4),
          ...insights.successfulStrategies.entries.take(5).map((entry) =>
            pw.Padding(
              padding: const pw.EdgeInsets.only(left: 16, bottom: 2),
              child: pw.Text('• ${entry.key} (${entry.value} times)', style: const pw.TextStyle(fontSize: 11)),
            ),
          ),
        ],

        // Show message when no aviation data is available
        if (insights.flightHistory.isEmpty && insights.detectedPhobias.isEmpty && insights.successfulStrategies.isEmpty) ...[
          pw.Container(
            padding: const pw.EdgeInsets.all(12),
            decoration: pw.BoxDecoration(
              color: PdfColors.grey100,
              borderRadius: pw.BorderRadius.circular(8),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text('📊 Getting Started', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
                pw.SizedBox(height: 4),
                pw.Text('As you continue using ALORA and sharing your flight experiences, this section will show:', style: const pw.TextStyle(fontSize: 11)),
                pw.SizedBox(height: 4),
                pw.Text('• Your flight history and anxiety patterns', style: const pw.TextStyle(fontSize: 10)),
                pw.Text('• Identified fears and triggers', style: const pw.TextStyle(fontSize: 10)),
                pw.Text('• Successful coping strategies', style: const pw.TextStyle(fontSize: 10)),
                pw.Text('• Progress over time', style: const pw.TextStyle(fontSize: 10)),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// Build behavioral patterns section with charts
  static pw.Widget _buildBehavioralPatterns(MemoryInsights insights, UserMemoryModel userMemory) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Behavioral Patterns'),
        pw.SizedBox(height: 12),

        // Weekly anxiety trends (simple text chart)
        pw.Text('Weekly Anxiety Levels:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
        pw.SizedBox(height: 8),
        _buildSimpleChart(insights.weeklyAnxietyTrends),
        pw.SizedBox(height: 12),

        // Usage statistics from real user data
        pw.Text('Most Active Usage:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
        pw.SizedBox(height: 4),
        pw.Text('Total Sessions: ${userMemory.behavioralPatterns.sessionFrequency.values.fold(0, (total, sessions) => total + sessions)}', style: const pw.TextStyle(fontSize: 11)),
        pw.Text('Voice Sessions: ${userMemory.behavioralPatterns.engagementMetrics['voiceSessions'] ?? 0}', style: const pw.TextStyle(fontSize: 11)),
        pw.Text('Chat Sessions: ${userMemory.behavioralPatterns.engagementMetrics['chatSessions'] ?? 0}', style: const pw.TextStyle(fontSize: 11)),
        pw.SizedBox(height: 12),

        // Mood patterns
        if (insights.moodPatterns.isNotEmpty) ...[
          pw.Text('Mood Tags:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
          pw.SizedBox(height: 4),
          ...insights.moodPatterns.entries.map((entry) =>
            pw.Text('- ${entry.key}: ${entry.value} times', style: const pw.TextStyle(fontSize: 11)),
          ),
          pw.SizedBox(height: 12),
        ],

        // Usage frequency
        if (insights.totalSessions > 0) ...[
          pw.Text('Usage Frequency:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
          pw.SizedBox(height: 4),
          pw.Text('Sessions per week: ${(insights.totalSessions / 4).toStringAsFixed(1)}',
                 style: const pw.TextStyle(fontSize: 11)),
          pw.Text('Voice vs Chat ratio: ${insights.voiceSessions}:${insights.chatSessions}',
                 style: const pw.TextStyle(fontSize: 11)),
        ] else ...[
          pw.Container(
            padding: const pw.EdgeInsets.all(12),
            decoration: pw.BoxDecoration(
              color: PdfColors.grey100,
              borderRadius: pw.BorderRadius.circular(8),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text('📈 Building Your Profile', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
                pw.SizedBox(height: 4),
                pw.Text('Start chatting with ALORA to see your behavioral patterns here:', style: const pw.TextStyle(fontSize: 11)),
                pw.SizedBox(height: 4),
                pw.Text('• Weekly anxiety level trends', style: const pw.TextStyle(fontSize: 10)),
                pw.Text('• Usage patterns and preferences', style: const pw.TextStyle(fontSize: 10)),
                pw.Text('• Mood tracking over time', style: const pw.TextStyle(fontSize: 10)),
                pw.Text('• Conversation insights', style: const pw.TextStyle(fontSize: 10)),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// Build therapy goals & progress section (actual user progress)
  static pw.Widget _buildTherapyProgress(UserMemoryModel userMemory, MemoryInsights insights) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Therapy Goals & Progress'),
        pw.SizedBox(height: 12),

        // Overall progress based on actual data
        pw.Text('Overall Progress: ${(userMemory.therapyProgress.overallProgress * 100).toStringAsFixed(1)}%',
               style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 14)),
        pw.SizedBox(height: 12),

        // Goals based on actual therapy progress
        pw.Text('🎯 Current Goals:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
        pw.SizedBox(height: 4),

        if (userMemory.therapyProgress.goals.isNotEmpty) ...[
          ...userMemory.therapyProgress.goals.map((goal) =>
            pw.Padding(
              padding: const pw.EdgeInsets.only(left: 16, bottom: 2),
              child: pw.Text('• ${goal.description} - ${(goal.progress * 100).toStringAsFixed(0)}% complete',
                           style: const pw.TextStyle(fontSize: 11)),
            ),
          ),
        ] else ...[
          // Default goals based on app usage
          pw.Text('- Reduce flight anxiety - ${(100 - insights.averageAnxietyLevel * 10).clamp(0, 100).toStringAsFixed(0)}% progress',
                 style: const pw.TextStyle(fontSize: 11)),
          pw.Text('- Build coping strategies - ${(insights.copingSuccessRate * 100).toStringAsFixed(0)}% effective',
                 style: const pw.TextStyle(fontSize: 11)),
          pw.Text('- Regular app engagement - ${insights.totalSessions} sessions completed',
                 style: const pw.TextStyle(fontSize: 11)),
        ],

        pw.SizedBox(height: 12),

        // Milestones based on actual achievements
        pw.Text('🏁 Achievements:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
        pw.SizedBox(height: 4),

        if (userMemory.therapyProgress.milestones.isNotEmpty) ...[
          ...userMemory.therapyProgress.milestones.map((milestone) =>
            pw.Padding(
              padding: const pw.EdgeInsets.only(left: 16, bottom: 2),
              child: pw.Text('• ${milestone.description} (${milestone.achievedAt.day}/${milestone.achievedAt.month}/${milestone.achievedAt.year})',
                           style: const pw.TextStyle(fontSize: 11)),
            ),
          ),
        ] else ...[
          // Generate achievements based on actual usage
          pw.Text('• Completed ${insights.totalSessions} therapy sessions', style: const pw.TextStyle(fontSize: 11)),
          if (insights.successfulStrategies.isNotEmpty)
            pw.Text('• Successfully used ${insights.successfulStrategies.length} different coping strategies',
                   style: const pw.TextStyle(fontSize: 11)),
          if (insights.flightHistory.isNotEmpty)
            pw.Text('• Tracked ${insights.flightHistory.length} flight experiences',
                   style: const pw.TextStyle(fontSize: 11)),
          if (insights.averageAnxietyLevel < 7)
            pw.Text('• Maintained manageable anxiety levels (avg: ${insights.averageAnxietyLevel.toStringAsFixed(1)}/10)',
                   style: const pw.TextStyle(fontSize: 11)),
        ],

        pw.SizedBox(height: 12),

        // Behavioral improvements
        pw.Text('📈 Behavioral Improvements:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
        pw.SizedBox(height: 4),

        if (insights.strengths.isNotEmpty) ...[
          ...insights.strengths.map((strength) =>
            pw.Text('• $strength', style: const pw.TextStyle(fontSize: 11)),
          ),
        ] else ...[
          if (insights.copingSuccessRate > 0.5)
            pw.Text('• Effective use of coping strategies (${(insights.copingSuccessRate * 100).toStringAsFixed(0)}% success rate)',
                   style: const pw.TextStyle(fontSize: 11)),
          if (insights.totalSessions > 5)
            pw.Text('• Consistent engagement with therapy tools', style: const pw.TextStyle(fontSize: 11)),
          if (insights.moodPatterns.containsKey('calm') && (insights.moodPatterns['calm'] ?? 0) > (insights.moodPatterns['anxious'] ?? 0))
            pw.Text('• More calm moments than anxious ones in recent conversations', style: const pw.TextStyle(fontSize: 11)),
        ],
      ],
    );
  }

  /// Build progress comparison section
  static pw.Widget _buildProgressComparison(ProgressComparison comparison) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Progress Since Last Export'),
        pw.SizedBox(height: 12),

        // Overall trend
        pw.Container(
          padding: const pw.EdgeInsets.all(12),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(),
            borderRadius: pw.BorderRadius.circular(8),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text('Overall Trend: ${comparison.overallTrend.toUpperCase()}',
                     style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 14)),
              pw.SizedBox(height: 8),
              pw.Text(comparison.summaryMessage, style: const pw.TextStyle(fontSize: 12)),
            ],
          ),
        ),

        pw.SizedBox(height: 12),

        // Specific changes
        pw.Text('Key Changes:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
        pw.SizedBox(height: 4),

        if (comparison.anxietyLevelChange != 0)
          pw.Text('• Anxiety level ${comparison.anxietyLevelChange < 0 ? 'decreased' : 'increased'} by ${comparison.anxietyLevelChange.abs().toStringAsFixed(1)} points',
                 style: const pw.TextStyle(fontSize: 11)),

        if (comparison.sessionCountChange != 0)
          pw.Text('• Session count ${comparison.sessionCountChange > 0 ? 'increased' : 'decreased'} by ${comparison.sessionCountChange.abs()} sessions',
                 style: const pw.TextStyle(fontSize: 11)),

        if (comparison.copingSuccessRateChange != 0)
          pw.Text('• Coping success rate ${comparison.copingSuccessRateChange > 0 ? 'improved' : 'declined'} by ${(comparison.copingSuccessRateChange.abs() * 100).toStringAsFixed(1)}%',
                 style: const pw.TextStyle(fontSize: 11)),

        if (comparison.newStrengths.isNotEmpty) ...[
          pw.SizedBox(height: 8),
          pw.Text('New Strengths:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
          ...comparison.newStrengths.map((strength) =>
            pw.Text('• $strength', style: const pw.TextStyle(fontSize: 11)),
          ),
        ],

        if (comparison.improvedAreas.isNotEmpty) ...[
          pw.SizedBox(height: 8),
          pw.Text('Improved Areas:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
          ...comparison.improvedAreas.map((area) =>
            pw.Text('• $area', style: const pw.TextStyle(fontSize: 11)),
          ),
        ],
      ],
    );
  }

  /// Build privacy disclosure section
  static pw.Widget _buildPrivacyDisclosure() {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Privacy & Data Use Disclosure'),
        pw.SizedBox(height: 12),

        pw.Text('Why this data is stored:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
        pw.Text('- To provide personalized flight anxiety support', style: const pw.TextStyle(fontSize: 11)),
        pw.Text('- To track your progress and improve coping strategies', style: const pw.TextStyle(fontSize: 11)),
        pw.Text('- To enhance the AI\'s understanding of your needs', style: const pw.TextStyle(fontSize: 11)),

        pw.SizedBox(height: 8),

        pw.Text('How it is protected:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
        pw.Text('- All data is encrypted and stored securely', style: const pw.TextStyle(fontSize: 11)),
        pw.Text('- Access is limited to your account only', style: const pw.TextStyle(fontSize: 11)),
        pw.Text('- No data is shared with third parties without consent', style: const pw.TextStyle(fontSize: 11)),

        pw.SizedBox(height: 8),

        pw.Text('How you can manage your data:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12)),
        pw.Text('- Export your data anytime through the app', style: const pw.TextStyle(fontSize: 11)),
        pw.Text('- Request data deletion through Privacy Settings', style: const pw.TextStyle(fontSize: 11)),
        pw.Text('- Update or correct information in your profile', style: const pw.TextStyle(fontSize: 11)),
      ],
    );
  }

  /// Build simple text-based chart
  static pw.Widget _buildSimpleChart(Map<String, double> data) {
    if (data.isEmpty) {
      return pw.Text('No data available', style: const pw.TextStyle(fontSize: 11));
    }

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: data.entries.map((entry) {
        final barLength = (entry.value * 10).clamp(0, 50).toInt(); // Scale to 0-50 chars
        final bar = '█' * barLength;
        return pw.Padding(
          padding: const pw.EdgeInsets.only(bottom: 2),
          child: pw.Text('${entry.key}: $bar ${entry.value.toStringAsFixed(1)}',
                        style: const pw.TextStyle(fontSize: 10)),
        );
      }).toList(),
    );
  }

  /// Build section header
  static pw.Widget _buildSectionHeader(String title) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          title,
          style: pw.TextStyle(
            fontSize: 18,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 4),
        pw.Divider(),
      ],
    );
  }
}
