import 'package:equatable/equatable.dart';

enum AdminStatus { initial, loading, loaded, error }

class AdminUser extends Equatable {
  final String id;
  final String email;
  final String name;
  final DateTime createdAt;
  final DateTime lastActive;
  final String plan;
  final Map<String, int> credits;
  final String? profileImage;

  const AdminUser({
    required this.id,
    required this.email,
    required this.name,
    required this.createdAt,
    required this.lastActive,
    this.plan = 'free',
    this.credits = const {'chat': 25, 'voice': 5},
    this.profileImage,
  });

  factory AdminUser.fromFirestore(Map<String, dynamic> data, String id) {
    return AdminUser(
      id: id,
      email: data['email'] ?? '',
      name: data['name'] ?? '',
      createdAt: _parseDateTime(data['createdAt']),
      lastActive: _parseDateTime(data['lastActive']),
      plan: _parsePlan(data),
      credits: _parseCredits(data['credits']),
      profileImage: data['profileImage'],
    );
  }

  static String _parsePlan(Map<String, dynamic> data) {
    if (data.containsKey('plan') && data['plan'] is String) {
      return data['plan'];
    }

    if (data.containsKey('subscription') && data['subscription'] is Map<String, dynamic>) {
      final subscription = data['subscription'] as Map<String, dynamic>;
      if (subscription.containsKey('plan') && subscription['plan'] is String) {
        return subscription['plan'];
      }
    }

    return 'free';
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();

    if (value.runtimeType.toString() == 'Timestamp') {
      return value.toDate();
    }

    if (value is int) {
      return DateTime.fromMillisecondsSinceEpoch(value);
    }

    if (value is DateTime) {
      return value;
    }

    return DateTime.now();
  }

  static Map<String, int> _parseCredits(dynamic value) {
    const defaultCredits = {'chat': 25, 'voice': 5};

    if (value == null) return defaultCredits;

    if (value is Map<String, int>) {
      return value;
    }

    if (value is Map<String, dynamic>) {
      final Map<String, int> result = {};

      for (final entry in value.entries) {
        final key = entry.key;
        final creditValue = entry.value;

        if (key == 'resetDate') continue;

        if (creditValue is Map<String, dynamic>) {
          if (creditValue.containsKey('remaining')) {
            final remaining = creditValue['remaining'];
            if (remaining is int) {
              result[key] = remaining;
            } else if (remaining is double) {
              result[key] = remaining.round();
            }
          } else if (creditValue.containsKey('limit')) {
            final limit = creditValue['limit'];
            if (limit is int) {
              result[key] = limit;
            } else if (limit is double) {
              result[key] = limit.round();
            }
          }
        }
        else if (creditValue is int) {
          result[key] = creditValue;
        } else if (creditValue is double) {
          result[key] = creditValue.round();
        } else if (creditValue is String) {
          final parsed = int.tryParse(creditValue);
          if (parsed != null) {
            result[key] = parsed;
          }
        }
      }

      result['chat'] ??= defaultCredits['chat']!;
      result['voice'] ??= defaultCredits['voice']!;

      return result;
    }

    return defaultCredits;
  }

  @override
  List<Object?> get props => [id, email, name, createdAt, lastActive, plan, credits, profileImage];
}

class DashboardStats extends Equatable {
  final int totalUsers;
  final int activeUsers;
  final Map<String, int> planBreakdown;
  final int dailyUsage;
  final double totalRevenue;

  const DashboardStats({
    required this.totalUsers,
    required this.activeUsers,
    required this.planBreakdown,
    required this.dailyUsage,
    required this.totalRevenue,
  });

  @override
  List<Object?> get props => [totalUsers, activeUsers, planBreakdown, dailyUsage, totalRevenue];
}

class PlanConfiguration extends Equatable {
  final Map<String, Map<String, dynamic>> plans;

  const PlanConfiguration({required this.plans});

  factory PlanConfiguration.defaultConfig() {
    return const PlanConfiguration(
      plans: {
        'free': {
          'name': 'Free Plan',
          'price': 0,
          'credits': {'chat': 25, 'voice': 5},
          'duration': 7,
        },
        'basic': {
          'name': 'Basic Plan',
          'price': 15,
          'credits': {'chat': 13000, 'voice': 80},
          'duration': 30,
        },
        'premium': {
          'name': 'Premium Plan',
          'price': 30,
          'credits': {'chat': 25000, 'voice': 300},
          'duration': 30,
        },
      },
    );
  }

  @override
  List<Object?> get props => [plans];
}

class AdminState extends Equatable {
  final AdminStatus status;
  final List<AdminUser> users;
  final List<AdminUser> filteredUsers;
  final DashboardStats? dashboardStats;
  final PlanConfiguration? planConfiguration;
  final String? error;
  final String? searchQuery;
  final String? planFilter;

  const AdminState({
    required this.status,
    this.users = const [],
    this.filteredUsers = const [],
    this.dashboardStats,
    this.planConfiguration,
    this.error,
    this.searchQuery,
    this.planFilter,
  });

  factory AdminState.initial() => const AdminState(status: AdminStatus.initial);

  AdminState copyWith({
    AdminStatus? status,
    List<AdminUser>? users,
    List<AdminUser>? filteredUsers,
    DashboardStats? dashboardStats,
    PlanConfiguration? planConfiguration,
    String? error,
    String? searchQuery,
    String? planFilter,
  }) {
    return AdminState(
      status: status ?? this.status,
      users: users ?? this.users,
      filteredUsers: filteredUsers ?? this.filteredUsers,
      dashboardStats: dashboardStats ?? this.dashboardStats,
      planConfiguration: planConfiguration ?? this.planConfiguration,
      error: error ?? this.error,
      searchQuery: searchQuery ?? this.searchQuery,
      planFilter: planFilter ?? this.planFilter,
    );
  }

  @override
  List<Object?> get props => [
        status,
        users,
        filteredUsers,
        dashboardStats,
        planConfiguration,
        error,
        searchQuery,
        planFilter,
      ];
}
