import 'package:equatable/equatable.dart';
import 'package:flight_fear_wellness_app/blocs/admin/admin_state.dart';

abstract class AdminEvent extends Equatable {
  const AdminEvent();

  @override
  List<Object?> get props => [];
}

class LoadDashboardData extends AdminEvent {}

class RefreshDashboardData extends AdminEvent {}

class StartRealtimeUpdates extends AdminEvent {}

class StopRealtimeUpdates extends AdminEvent {}

class ResetAdminState extends AdminEvent {}

class DashboardDataUpdated extends AdminEvent {
  final DashboardStats stats;

  const DashboardDataUpdated({required this.stats});

  @override
  List<Object?> get props => [stats];
}

class UsersDataUpdated extends AdminEvent {
  final List<AdminUser> users;

  const UsersDataUpdated({required this.users});

  @override
  List<Object?> get props => [users];
}

class LoadAllUsers extends AdminEvent {}

class UpdateUserPlan extends AdminEvent {
  final String userId;
  final String newPlan;

  const UpdateUserPlan({
    required this.userId,
    required this.newPlan,
  });

  @override
  List<Object?> get props => [userId, newPlan];
}

class ResetUserCredits extends AdminEvent {
  final String userId;

  const ResetUserCredits({required this.userId});

  @override
  List<Object?> get props => [userId];
}

class DeleteUser extends AdminEvent {
  final String userId;

  const DeleteUser({required this.userId});

  @override
  List<Object?> get props => [userId];
}

class LoadPlanConfiguration extends AdminEvent {}

class UpdatePlanConfiguration extends AdminEvent {
  final Map<String, dynamic> planConfig;

  const UpdatePlanConfiguration({required this.planConfig});

  @override
  List<Object?> get props => [planConfig];
}

class SearchUsers extends AdminEvent {
  final String query;

  const SearchUsers({required this.query});

  @override
  List<Object?> get props => [query];
}

class FilterUsersByPlan extends AdminEvent {
  final String? planFilter;

  const FilterUsersByPlan({this.planFilter});

  @override
  List<Object?> get props => [planFilter];
}


