"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.paypalWebhook = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
const crypto = __importStar(require("crypto"));
// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
    admin.initializeApp();
}
const db = admin.firestore();
// PayPal webhook secret (set in Firebase Functions config)
const WEBHOOK_SECRET = ((_a = functions.config().paypal) === null || _a === void 0 ? void 0 : _a.webhook_secret) || process.env.PAYPAL_WEBHOOK_SECRET;
/**
 * Verify PayPal webhook signature
 * Note: PayPal uses a more complex verification process in production
 * This is a simplified version for basic verification
 */
function verifyWebhookSignature(payload, signature) {
    if (!WEBHOOK_SECRET) {
        console.error('PayPal webhook secret not configured');
        return false;
    }
    // PayPal signature verification is more complex than this
    // In production, you should use PayPal's SDK for proper verification
    const expectedSignature = crypto
        .createHmac('sha256', WEBHOOK_SECRET)
        .update(payload, 'utf8')
        .digest('hex');
    return crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature));
}
/**
 * Map PayPal plan ID to internal plan
 */
function mapPlanIdToPlan(planId) {
    const planMapping = {
        // Add your PayPal plan IDs here
        'P-basic-monthly': 'basic',
        'P-premium-monthly': 'premium',
        'P-basic-yearly': 'basic',
        'P-premium-yearly': 'premium',
    };
    return planMapping[planId] || 'free';
}
/**
 * Get credit limits for a plan
 */
function getCreditLimits(plan) {
    const limits = {
        free: { chatLimit: 25, voiceLimit: 5 },
        basic: { chatLimit: 13000, voiceLimit: 80 },
        premium: { chatLimit: 20000, voiceLimit: 300 },
    };
    return limits[plan] || limits.free;
}
/**
 * Find user by PayPal subscription ID
 */
async function findUserBySubscriptionId(subscriptionId) {
    const query = await db
        .collection('users')
        .where('payment.paypal.subscriptionId', '==', subscriptionId)
        .limit(1)
        .get();
    return query.empty ? null : query.docs[0].id;
}
/**
 * Find user by PayPal customer ID
 */
async function findUserByCustomerId(customerId) {
    const query = await db
        .collection('users')
        .where('payment.paypal.customerId', '==', customerId)
        .limit(1)
        .get();
    return query.empty ? null : query.docs[0].id;
}
/**
 * Handle subscription created
 */
async function handleSubscriptionCreated(data) {
    var _a;
    const subscription = data.resource || data;
    const subscriptionId = subscription.id;
    const planId = subscription.plan_id;
    const customerId = (_a = subscription.subscriber) === null || _a === void 0 ? void 0 : _a.payer_id;
    if (!planId) {
        console.error('No plan ID found in PayPal subscription');
        return;
    }
    // Try to find user by subscription ID first, then by customer ID
    let userId = await findUserBySubscriptionId(subscriptionId);
    if (!userId && customerId) {
        userId = await findUserByCustomerId(customerId);
    }
    if (!userId) {
        console.error(`User not found for PayPal subscription ${subscriptionId}`);
        return;
    }
    const plan = mapPlanIdToPlan(planId);
    const { chatLimit, voiceLimit } = getCreditLimits(plan);
    const now = new Date();
    // PayPal billing cycles can be complex, using a default 30-day cycle
    const endDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    try {
        await db.collection('users').doc(userId).update({
            subscription: {
                plan: plan,
                status: 'active',
                startDate: admin.firestore.Timestamp.fromDate(now),
                endDate: admin.firestore.Timestamp.fromDate(endDate),
                autoRenew: true,
                trialUsed: false,
                paymentPlatform: 'paypal',
                subscriptionId: subscriptionId,
                customerId: customerId || null,
            },
            credits: {
                chat: {
                    limit: chatLimit,
                    used: 0,
                    remaining: chatLimit,
                },
                voice: {
                    limit: voiceLimit,
                    used: 0,
                    remaining: voiceLimit,
                },
                resetDate: admin.firestore.Timestamp.fromDate(endDate),
            },
            payment: {
                paypal: {
                    customerId: customerId || null,
                    subscriptionId: subscriptionId,
                },
            },
        });
        console.log(`PayPal subscription created for user ${userId}, plan: ${plan}`);
    }
    catch (error) {
        console.error('Error handling PayPal subscription created:', error);
        throw error;
    }
}
/**
 * Handle subscription activated
 */
async function handleSubscriptionActivated(data) {
    const subscription = data.resource || data;
    const subscriptionId = subscription.id;
    const userId = await findUserBySubscriptionId(subscriptionId);
    if (!userId) {
        console.error(`User not found for PayPal subscription ${subscriptionId}`);
        return;
    }
    try {
        await db.collection('users').doc(userId).update({
            'subscription.status': 'active',
        });
        console.log(`PayPal subscription activated for user ${userId}`);
    }
    catch (error) {
        console.error('Error handling PayPal subscription activated:', error);
        throw error;
    }
}
/**
 * Handle subscription cancelled
 */
async function handleSubscriptionCancelled(data) {
    const subscription = data.resource || data;
    const subscriptionId = subscription.id;
    const userId = await findUserBySubscriptionId(subscriptionId);
    if (!userId) {
        console.error(`User not found for PayPal subscription ${subscriptionId}`);
        return;
    }
    try {
        await db.collection('users').doc(userId).update({
            'subscription.status': 'cancelled',
            'subscription.autoRenew': false,
        });
        console.log(`PayPal subscription cancelled for user ${userId}`);
    }
    catch (error) {
        console.error('Error handling PayPal subscription cancelled:', error);
        throw error;
    }
}
/**
 * Handle payment completed
 */
async function handlePaymentCompleted(data) {
    const payment = data.resource || data;
    const subscriptionId = payment.billing_agreement_id;
    if (!subscriptionId) {
        console.log('Payment completed but no subscription ID found');
        return;
    }
    const userId = await findUserBySubscriptionId(subscriptionId);
    if (!userId) {
        console.error(`User not found for PayPal subscription ${subscriptionId}`);
        return;
    }
    try {
        await db.collection('users').doc(userId).update({
            'subscription.status': 'active',
        });
        console.log(`PayPal payment completed for user ${userId}`);
    }
    catch (error) {
        console.error('Error handling PayPal payment completed:', error);
        throw error;
    }
}
/**
 * Main PayPal webhook handler
 */
exports.paypalWebhook = functions.https.onRequest(async (req, res) => {
    if (req.method !== 'POST') {
        res.status(405).send('Method Not Allowed');
        return;
    }
    try {
        const signature = req.get('PAYPAL-TRANSMISSION-SIG') || '';
        const payload = JSON.stringify(req.body);
        // Note: In production, you should implement proper PayPal webhook verification
        // using PayPal's SDK and certificate validation
        if (WEBHOOK_SECRET && !verifyWebhookSignature(payload, signature)) {
            console.error('Invalid PayPal webhook signature');
            res.status(401).send('Unauthorized');
            return;
        }
        const event = req.body;
        const eventType = event.event_type;
        console.log(`Received PayPal webhook: ${eventType}`);
        switch (eventType) {
            case 'BILLING.SUBSCRIPTION.CREATED':
                await handleSubscriptionCreated(event);
                break;
            case 'BILLING.SUBSCRIPTION.ACTIVATED':
                await handleSubscriptionActivated(event);
                break;
            case 'BILLING.SUBSCRIPTION.CANCELLED':
                await handleSubscriptionCancelled(event);
                break;
            case 'BILLING.SUBSCRIPTION.SUSPENDED':
                await handleSubscriptionCancelled(event); // Same logic as cancelled
                break;
            case 'PAYMENT.SALE.COMPLETED':
                await handlePaymentCompleted(event);
                break;
            default:
                console.log(`Unhandled PayPal webhook event: ${eventType}`);
        }
        res.status(200).send('OK');
    }
    catch (error) {
        console.error('PayPal webhook error:', error);
        res.status(500).send('Internal Server Error');
    }
});
//# sourceMappingURL=paypal-webhook.js.map