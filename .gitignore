# =============================================================================
# FLUTTER FLIGHT FEAR WELLNESS APP - GIT IGNORE CONFIGURATION
# =============================================================================

# =============================================================================
# SECURITY: ENVIRONMENT VARIABLES & API KEYS
# =============================================================================
# CRITICAL: Never commit real API keys or credentials to version control
.env
android/.env
.env.local
.env.production
.env.staging
*.env
!.env.example

# =============================================================================
# FLUTTER & DART
# =============================================================================
# Files and directories created by pub
.dart_tool/
.packages
build/
pubspec.lock

# If you're building an application, you may want to check-in your pubspec.lock
# pubspec.lock

# Directory created by dartdoc
doc/api/

# Avoid committing generated Javascript files:
*.dart.js
*.info.json      # Produced by the --dump-info flag.
*.js             # When generated by dart2js. Don't specify *.js if your
                 # project includes source files written in JavaScript.
*.js_
*.js.deps
*.js.map

# =============================================================================
# FLUTTER SPECIFIC
# =============================================================================
.flutter-plugins
.flutter-plugins-dependencies
.metadata

# =============================================================================
# ANDROID
# =============================================================================
**/android/**/gradle-wrapper.jar
**/android/.gradle
**/android/captures/
**/android/gradlew
**/android/gradlew.bat
**/android/local.properties
**/android/**/GeneratedPluginRegistrant.java
**/android/key.properties

# =============================================================================
# iOS
# =============================================================================
**/ios/**/*.mode1v3
**/ios/**/*.mode2v3
**/ios/**/*.moved-aside
**/ios/**/*.pbxuser
**/ios/**/*.perspectivev3
**/ios/**/*sync/
**/ios/**/.sconsign.dblite
**/ios/**/.tags*
**/ios/**/.vagrant/
**/ios/**/DerivedData/
**/ios/**/Icon?
**/ios/**/Pods/
**/ios/**/.symlinks/
**/ios/**/profile
**/ios/**/xcuserdata
**/ios/.generated/
**/ios/Flutter/App.framework
**/ios/Flutter/Flutter.framework
**/ios/Flutter/Flutter.podspec
**/ios/Flutter/Generated.xcconfig
**/ios/Flutter/ephemeral/
**/ios/Flutter/app.flx
**/ios/Flutter/app.zip
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/ServiceDefinitions.json
**/ios/Runner/GeneratedPluginRegistrant.*

# =============================================================================
# WEB
# =============================================================================
**/web/flutter_service_worker.js
**/web/manifest.json

# =============================================================================
# FIREBASE & BACKEND
# =============================================================================
# Firebase configuration files (keep templates, ignore actual configs)
firebase_options.dart
google-services.json
GoogleService-Info.plist
.firebase/
firebase-debug.log
firestore-debug.log

# =============================================================================
# IDE & EDITOR FILES
# =============================================================================
# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# VS Code related
.vscode/
.history/

# =============================================================================
# LOGS & TEMPORARY FILES
# =============================================================================
*.log
*.tmp
*.temp
.DS_Store
Thumbs.db

# =============================================================================
# PAYMENT & WEBHOOK SECRETS
# =============================================================================
# Payment provider configuration files
payment_config.json
webhook_secrets.json
stripe_config.json
lemonsqueezy_config.json
paypal_config.json

# =============================================================================
# ALLOWED FILES (WHITELIST)
# =============================================================================
# Unity WebGL Build (keep this for your avatar)
!WebGLBuild/
!WebGLBuild/**

# Environment template files (safe to commit)
!.env.example

# Configuration templates (safe to commit)
!**/config.example.*
!**/template.*
