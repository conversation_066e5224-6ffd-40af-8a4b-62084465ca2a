import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'user_model.dart';
import 'subscription_model.dart';
import 'credit_model.dart';

class UserSubscriptionModel extends Equatable {
  final UserModel user;
  final SubscriptionModel subscription;
  final CreditModel credits;
  final UsageStats usage;
  final PaymentInfo payment;

  const UserSubscriptionModel({
    required this.user,
    required this.subscription,
    required this.credits,
    required this.usage,
    required this.payment,
  });

  factory UserSubscriptionModel.newUser({
    required String id,
    required String email,
    required String name,
  }) {
    final now = DateTime.now();
    return UserSubscriptionModel(
      user: UserModel(
        id: id,
        email: email,
        name: name,
        createdAt: now,
        lastActive: now,
      ),
      subscription: SubscriptionModel.free(),
      credits: CreditModel.free(),
      usage: UsageStats.empty(),
      payment: PaymentInfo.empty(),
    );
  }

  factory UserSubscriptionModel.fromMap(Map<String, dynamic> data, String userId) {
    final user = UserModel.fromMap({
      'id': userId,
      'email': data['email'] ?? '',
      'name': data['name'] ?? '',
      'createdAt': data['createdAt'] ?? DateTime.now().millisecondsSinceEpoch,
      'lastActive': data['lastActiveAt'] ?? DateTime.now().millisecondsSinceEpoch,
      'preferences': data['preferences'],
    });

    return UserSubscriptionModel(
      user: user,
      subscription: SubscriptionModel.fromMap(data['subscription'] ?? {}, userCreatedAt: user.createdAt),
      credits: CreditModel.fromMap(data['credits'] ?? {}),
      usage: UsageStats.fromMap(data['usage'] ?? {}),
      payment: PaymentInfo.fromMap(data['payment'] ?? {}),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'email': user.email,
      'name': user.name,
      'createdAt': user.createdAt.millisecondsSinceEpoch,
      'lastActiveAt': user.lastActive.millisecondsSinceEpoch,
      'preferences': user.preferences,
      'subscription': subscription.toMap(),
      'credits': credits.toMap(),
      'usage': usage.toMap(),
      'payment': payment.toMap(),
    };
  }

  bool get isSubscriptionActive => subscription.isActive;
  bool get hasExpiredSubscription => subscription.isExpired;

  bool get canUseChat {
    if (subscription.plan == SubscriptionPlan.free) {
      return !subscription.isExpired && credits.hasChatCredits;
    }
    return isSubscriptionActive && credits.hasChatCredits;
  }

  bool get canUseVoice {
    if (subscription.plan == SubscriptionPlan.free) {
      return !subscription.isExpired && credits.hasVoiceCredits;
    }
    return isSubscriptionActive && credits.hasVoiceCredits;
  }

  bool get needsUpgrade => !canUseChat && !canUseVoice;

  String get planName => subscription.planDisplayName;
  double get planPrice => subscription.planPrice;
  int get daysRemaining => subscription.daysRemaining;

  UserSubscriptionModel useChatCredit() {
    return copyWith(
      credits: credits.useChatCredit(),
      usage: usage.incrementChat(),
    );
  }

  UserSubscriptionModel useVoiceCredit() {
    return copyWith(
      credits: credits.useVoiceCredit(),
      usage: usage.incrementVoice(),
    );
  }

  UserSubscriptionModel upgradeTo(SubscriptionPlan newPlan, {
    String? subscriptionId,
    String? customerId,
    PaymentPlatform? platform,
  }) {
    final now = DateTime.now();
    final newSubscription = SubscriptionModel(
      plan: newPlan,
      status: SubscriptionStatus.active,
      startDate: now,
      endDate: now.add(const Duration(days: 30)),
      autoRenew: true,
      trialUsed: subscription.trialUsed,
      paymentPlatform: platform,
      subscriptionId: subscriptionId,
      customerId: customerId,
    );

    return copyWith(
      subscription: newSubscription,
      credits: CreditModel.fromPlan(newPlan),
      payment: payment.copyWith(
        lemonsqueezyCustomerId: platform == PaymentPlatform.lemonsqueezy ? customerId : null,
        lemonsqueezySubscriptionId: platform == PaymentPlatform.lemonsqueezy ? subscriptionId : null,
        stripeCustomerId: platform == PaymentPlatform.stripe ? customerId : null,
        stripeSubscriptionId: platform == PaymentPlatform.stripe ? subscriptionId : null,
        paypalCustomerId: platform == PaymentPlatform.paypal ? customerId : null,
        paypalSubscriptionId: platform == PaymentPlatform.paypal ? subscriptionId : null,
      ),
    );
  }

  UserSubscriptionModel copyWith({
    UserModel? user,
    SubscriptionModel? subscription,
    CreditModel? credits,
    UsageStats? usage,
    PaymentInfo? payment,
  }) {
    return UserSubscriptionModel(
      user: user ?? this.user,
      subscription: subscription ?? this.subscription,
      credits: credits ?? this.credits,
      usage: usage ?? this.usage,
      payment: payment ?? this.payment,
    );
  }

  @override
  List<Object?> get props => [user, subscription, credits, usage, payment];

  @override
  String toString() {
    return 'UserSubscriptionModel(user: ${user.email}, plan: ${subscription.plan}, credits: ${credits.chatRemaining}/${credits.voiceRemaining})';
  }
}

class UsageStats extends Equatable {
  final int totalChatPrompts;
  final int totalVoicePrompts;
  final DateTime? lastChatAt;
  final DateTime? lastVoiceAt;

  const UsageStats({
    required this.totalChatPrompts,
    required this.totalVoicePrompts,
    this.lastChatAt,
    this.lastVoiceAt,
  });

  factory UsageStats.empty() {
    return const UsageStats(
      totalChatPrompts: 0,
      totalVoicePrompts: 0,
    );
  }

  factory UsageStats.fromMap(Map<String, dynamic> data) {
    return UsageStats(
      totalChatPrompts: data['totalChatPrompts'] ?? 0,
      totalVoicePrompts: data['totalVoicePrompts'] ?? 0,
      lastChatAt: data['lastChatAt'] != null 
          ? (data['lastChatAt'] as Timestamp).toDate() 
          : null,
      lastVoiceAt: data['lastVoiceAt'] != null 
          ? (data['lastVoiceAt'] as Timestamp).toDate() 
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'totalChatPrompts': totalChatPrompts,
      'totalVoicePrompts': totalVoicePrompts,
      'lastChatAt': lastChatAt != null ? Timestamp.fromDate(lastChatAt!) : null,
      'lastVoiceAt': lastVoiceAt != null ? Timestamp.fromDate(lastVoiceAt!) : null,
    };
  }

  UsageStats incrementChat() {
    return copyWith(
      totalChatPrompts: totalChatPrompts + 1,
      lastChatAt: DateTime.now(),
    );
  }

  UsageStats incrementVoice() {
    return copyWith(
      totalVoicePrompts: totalVoicePrompts + 1,
      lastVoiceAt: DateTime.now(),
    );
  }

  UsageStats copyWith({
    int? totalChatPrompts,
    int? totalVoicePrompts,
    DateTime? lastChatAt,
    DateTime? lastVoiceAt,
  }) {
    return UsageStats(
      totalChatPrompts: totalChatPrompts ?? this.totalChatPrompts,
      totalVoicePrompts: totalVoicePrompts ?? this.totalVoicePrompts,
      lastChatAt: lastChatAt ?? this.lastChatAt,
      lastVoiceAt: lastVoiceAt ?? this.lastVoiceAt,
    );
  }

  @override
  List<Object?> get props => [totalChatPrompts, totalVoicePrompts, lastChatAt, lastVoiceAt];
}

class PaymentInfo extends Equatable {
  final String? lemonsqueezyCustomerId;
  final String? lemonsqueezySubscriptionId;
  final String? stripeCustomerId;
  final String? stripeSubscriptionId;
  final String? paypalCustomerId;
  final String? paypalSubscriptionId;

  const PaymentInfo({
    this.lemonsqueezyCustomerId,
    this.lemonsqueezySubscriptionId,
    this.stripeCustomerId,
    this.stripeSubscriptionId,
    this.paypalCustomerId,
    this.paypalSubscriptionId,
  });

  factory PaymentInfo.empty() {
    return const PaymentInfo();
  }

  factory PaymentInfo.fromMap(Map<String, dynamic> data) {
    return PaymentInfo(
      lemonsqueezyCustomerId: data['lemonsqueezy']?['customerId'],
      lemonsqueezySubscriptionId: data['lemonsqueezy']?['subscriptionId'],
      stripeCustomerId: data['stripe']?['customerId'],
      stripeSubscriptionId: data['stripe']?['subscriptionId'],
      paypalCustomerId: data['paypal']?['customerId'],
      paypalSubscriptionId: data['paypal']?['subscriptionId'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'lemonsqueezy': {
        'customerId': lemonsqueezyCustomerId,
        'subscriptionId': lemonsqueezySubscriptionId,
      },
      'stripe': {
        'customerId': stripeCustomerId,
        'subscriptionId': stripeSubscriptionId,
      },
      'paypal': {
        'customerId': paypalCustomerId,
        'subscriptionId': paypalSubscriptionId,
      },
    };
  }

  PaymentInfo copyWith({
    String? lemonsqueezyCustomerId,
    String? lemonsqueezySubscriptionId,
    String? stripeCustomerId,
    String? stripeSubscriptionId,
    String? paypalCustomerId,
    String? paypalSubscriptionId,
  }) {
    return PaymentInfo(
      lemonsqueezyCustomerId: lemonsqueezyCustomerId ?? this.lemonsqueezyCustomerId,
      lemonsqueezySubscriptionId: lemonsqueezySubscriptionId ?? this.lemonsqueezySubscriptionId,
      stripeCustomerId: stripeCustomerId ?? this.stripeCustomerId,
      stripeSubscriptionId: stripeSubscriptionId ?? this.stripeSubscriptionId,
      paypalCustomerId: paypalCustomerId ?? this.paypalCustomerId,
      paypalSubscriptionId: paypalSubscriptionId ?? this.paypalSubscriptionId,
    );
  }

  @override
  List<Object?> get props => [
        lemonsqueezyCustomerId,
        lemonsqueezySubscriptionId,
        stripeCustomerId,
        stripeSubscriptionId,
        paypalCustomerId,
        paypalSubscriptionId,
      ];
}
