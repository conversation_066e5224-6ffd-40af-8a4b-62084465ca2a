import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

enum SubscriptionPlan { free, basic, premium }
enum SubscriptionStatus { active, expired, cancelled }
enum PaymentPlatform { lemonsqueezy, stripe, paypal }

class SubscriptionModel extends Equatable {
  final SubscriptionPlan plan;
  final SubscriptionStatus status;
  final DateTime startDate;
  final DateTime endDate;
  final bool autoRenew;
  final bool trialUsed;
  final DateTime? trialEndsAt;
  final PaymentPlatform? paymentPlatform;
  final String? subscriptionId;
  final String? customerId;

  const SubscriptionModel({
    required this.plan,
    required this.status,
    required this.startDate,
    required this.endDate,
    this.autoRenew = true,
    this.trialUsed = false,
    this.trialEndsAt,
    this.paymentPlatform,
    this.subscriptionId,
    this.customerId,
  });

  factory SubscriptionModel.free() {
    final now = DateTime.now();
    final trialEnd = now.add(const Duration(days: 7));
    return SubscriptionModel(
      plan: SubscriptionPlan.free,
      status: SubscriptionStatus.active,
      startDate: now,
      endDate: trialEnd,
      autoRenew: false,
      trialUsed: false,
      trialEndsAt: trialEnd,
    );
  }

  factory SubscriptionModel.freeWithCreationDate(DateTime? userCreatedAt) {
    final creationDate = userCreatedAt ?? DateTime.now();
    final trialEnd = creationDate.add(const Duration(days: 7));
    return SubscriptionModel(
      plan: SubscriptionPlan.free,
      status: SubscriptionStatus.active,
      startDate: creationDate,
      endDate: trialEnd,
      autoRenew: false,
      trialUsed: false,
      trialEndsAt: trialEnd,
    );
  }

  factory SubscriptionModel.fromMap(Map<String, dynamic> data, {DateTime? userCreatedAt}) {
    try {
      final now = DateTime.now();
      final userCreationDate = userCreatedAt ?? now;

      return SubscriptionModel(
        plan: SubscriptionPlan.values.firstWhere(
          (e) => e.name == data['plan'],
          orElse: () => SubscriptionPlan.free,
        ),
        status: SubscriptionStatus.values.firstWhere(
          (e) => e.name == data['status'],
          orElse: () => SubscriptionStatus.active,
        ),
        startDate: _parseDateTime(data['startDate']) ?? userCreationDate,
        endDate: _parseDateTime(data['endDate']) ?? userCreationDate.add(const Duration(days: 7)),
        autoRenew: data['autoRenew'] ?? true,
        trialUsed: data['trialUsed'] ?? false,
        trialEndsAt: _parseDateTime(data['trialEndsAt']) ?? userCreationDate.add(const Duration(days: 7)),
        paymentPlatform: data['paymentPlatform'] != null
            ? PaymentPlatform.values.firstWhere(
                (e) => e.name == data['paymentPlatform'],
                orElse: () => PaymentPlatform.lemonsqueezy,
              )
            : null,
        subscriptionId: data['subscriptionId'],
        customerId: data['customerId'],
      );
    } catch (e) {
      print('Error parsing subscription data: $e, using free plan defaults');
      return SubscriptionModel.freeWithCreationDate(userCreatedAt);
    }
  }

  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;

    try {
      if (value is Timestamp) {
        return value.toDate();
      } else if (value is int) {
        return DateTime.fromMillisecondsSinceEpoch(value);
      } else if (value is String) {
        return DateTime.parse(value);
      }
    } catch (e) {
      print('Error parsing DateTime: $e');
    }

    return null;
  }

  Map<String, dynamic> toMap() {
    return {
      'plan': plan.name,
      'status': status.name,
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'autoRenew': autoRenew,
      'trialUsed': trialUsed,
      'trialEndsAt': trialEndsAt != null ? Timestamp.fromDate(trialEndsAt!) : null,
      'paymentPlatform': paymentPlatform?.name,
      'subscriptionId': subscriptionId,
      'customerId': customerId,
    };
  }

  bool get isActive {
    return status == SubscriptionStatus.active && 
           DateTime.now().isBefore(endDate);
  }

  bool get isExpired {
    return DateTime.now().isAfter(endDate) || 
           status == SubscriptionStatus.expired;
  }

  int get daysRemaining {
    if (isExpired) return 0;
    return endDate.difference(DateTime.now()).inDays;
  }

  String get planDisplayName {
    switch (plan) {
      case SubscriptionPlan.free:
        return 'Free Plan';
      case SubscriptionPlan.basic:
        return 'Basic Plan';
      case SubscriptionPlan.premium:
        return 'Premium Plan';
    }
  }

  double get planPrice {
    switch (plan) {
      case SubscriptionPlan.free:
        return 0.0;
      case SubscriptionPlan.basic:
        return 15.0;
      case SubscriptionPlan.premium:
        return 30.0;
    }
  }

  SubscriptionModel copyWith({
    SubscriptionPlan? plan,
    SubscriptionStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    bool? autoRenew,
    bool? trialUsed,
    DateTime? trialEndsAt,
    PaymentPlatform? paymentPlatform,
    String? subscriptionId,
    String? customerId,
  }) {
    return SubscriptionModel(
      plan: plan ?? this.plan,
      status: status ?? this.status,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      autoRenew: autoRenew ?? this.autoRenew,
      trialUsed: trialUsed ?? this.trialUsed,
      trialEndsAt: trialEndsAt ?? this.trialEndsAt,
      paymentPlatform: paymentPlatform ?? this.paymentPlatform,
      subscriptionId: subscriptionId ?? this.subscriptionId,
      customerId: customerId ?? this.customerId,
    );
  }

  @override
  List<Object?> get props => [
        plan,
        status,
        startDate,
        endDate,
        autoRenew,
        trialUsed,
        trialEndsAt,
        paymentPlatform,
        subscriptionId,
        customerId,
      ];

  @override
  String toString() {
    return 'SubscriptionModel(plan: $plan, status: $status, startDate: $startDate, endDate: $endDate, autoRenew: $autoRenew, paymentPlatform: $paymentPlatform)';
  }
}
