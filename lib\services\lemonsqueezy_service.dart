import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:url_launcher/url_launcher.dart';
import '../models/subscription_model.dart';
import 'secure_storage_service.dart';

// Purpose: Handle LemonSqueezy payment processing and subscription management
class LemonSqueezyService {
  static const String _baseUrl = 'https://api.lemonsqueezy.com/v1';
  
  static String? _storeId;
  static String? _apiKey;
  
  static final Map<SubscriptionPlan, String> _productVariants = {
    SubscriptionPlan.basic: '',
    SubscriptionPlan.premium: '',
  };

  // Purpose: Initialize LemonSqueezy service with store credentials
  static Future<void> initialize({
    required String storeId,
    required String apiKey,
  }) async {
    _storeId = storeId;
    _apiKey = apiKey;
    
    await SecureStorageService.storeEncrypted('lemonsqueezy_store_id', storeId);
    await SecureStorageService.storeEncrypted('lemonsqueezy_api_key', apiKey);
  }

  static Future<void> loadCredentials() async {
    _storeId = await SecureStorageService.getDecrypted('lemonsqueezy_store_id');
    _apiKey = await SecureStorageService.getDecrypted('lemonsqueezy_api_key');
  }

  // Purpose: Create checkout URL for subscription plan purchases
  static Future<String> createCheckoutUrl({
    required SubscriptionPlan plan,
    required String userEmail,
    required String userId,
  }) async {
    if (_storeId == null || _apiKey == null) {
      await loadCredentials();
      if (_storeId == null || _apiKey == null) {
        throw Exception('LemonSqueezy not configured. Please run setup wizard.');
      }
    }

    if (_productVariants[plan]?.isEmpty ?? true) {
      await loadProductVariants();
    }

    final variantId = _productVariants[plan];
    if (variantId == null || variantId.isEmpty) {
      throw Exception('Product variant not configured for plan: ${plan.name}. Please complete setup.');
    }

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/checkouts'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Accept': 'application/vnd.api+json',
          'Content-Type': 'application/vnd.api+json',
        },
        body: json.encode({
          'data': {
            'type': 'checkouts',
            'attributes': {
              'checkout_data': {
                'email': userEmail,
                'custom': {
                  'user_id': userId,
                  'plan': plan.name,
                },
              },
            },
            'relationships': {
              'store': {
                'data': {
                  'type': 'stores',
                  'id': _storeId,
                },
              },
              'variant': {
                'data': {
                  'type': 'variants',
                  'id': variantId,
                },
              },
            },
          },
        }),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return data['data']['attributes']['url'];
      } else {
        throw Exception('Failed to create checkout: ${response.body}');
      }
    } catch (e) {
      throw Exception('Checkout creation failed: $e');
    }
  }

  static Future<void> launchCheckout({
    required SubscriptionPlan plan,
    required String userEmail,
    required String userId,
  }) async {
    try {
      final checkoutUrl = await createCheckoutUrl(
        plan: plan,
        userEmail: userEmail,
        userId: userId,
      );

      final uri = Uri.parse(checkoutUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        throw Exception('Could not launch checkout URL');
      }
    } catch (e) {
      throw Exception('Failed to launch checkout: $e');
    }
  }

  static Future<Map<String, dynamic>?> getSubscription(String subscriptionId) async {
    if (_apiKey == null) {
      await loadCredentials();
      if (_apiKey == null) return null;
    }

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/subscriptions/$subscriptionId'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Accept': 'application/vnd.api+json',
        },
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      }
    } catch (e) {
      print('Error getting subscription: $e');
    }
    return null;
  }

  static Future<List<Map<String, dynamic>>> getCustomerSubscriptions(String customerEmail) async {
    if (_apiKey == null) {
      await loadCredentials();
      if (_apiKey == null) return [];
    }

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/subscriptions?filter[user_email]=$customerEmail'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Accept': 'application/vnd.api+json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data['data'] ?? []);
      }
    } catch (e) {
      print('Error getting customer subscriptions: $e');
    }
    return [];
  }

  static Future<Map<String, dynamic>?> checkUserSubscription(String userEmail) async {
    try {
      final subscriptions = await getCustomerSubscriptions(userEmail);
      
      for (final subscription in subscriptions) {
        final attributes = subscription['attributes'];
        final status = attributes['status'];
        
        if (status == 'active') {
          return {
            'subscription_id': subscription['id'],
            'status': status,
            'variant_id': attributes['variant_id'],
            'renews_at': attributes['renews_at'],
            'ends_at': attributes['ends_at'],
            'plan': _mapVariantToPlan(attributes['variant_id'].toString()),
          };
        }
      }
    } catch (e) {
      print('Error checking user subscription: $e');
    }
    return null;
  }

  static SubscriptionPlan? _mapVariantToPlan(String variantId) {
    for (final entry in _productVariants.entries) {
      if (entry.value == variantId) {
        return entry.key;
      }
    }
    return null;
  }

  static Future<bool> testConnection() async {
    if (_apiKey == null) {
      await loadCredentials();
      if (_apiKey == null) return false;
    }

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/stores/$_storeId'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Accept': 'application/vnd.api+json',
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      print('LemonSqueezy connection test failed: $e');
      return false;
    }
  }

  static Future<Map<String, dynamic>?> getStoreInfo() async {
    if (_apiKey == null || _storeId == null) {
      await loadCredentials();
      if (_apiKey == null || _storeId == null) return null;
    }

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/stores/$_storeId'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Accept': 'application/vnd.api+json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['data']['attributes'];
      }
    } catch (e) {
      print('Error getting store info: $e');
    }
    return null;
  }

  static Future<void> updateProductVariants({
    required String basicVariantId,
    required String premiumVariantId,
  }) async {
    _productVariants[SubscriptionPlan.basic] = basicVariantId;
    _productVariants[SubscriptionPlan.premium] = premiumVariantId;

    await SecureStorageService.storeEncrypted('basic_variant_id', basicVariantId);
    await SecureStorageService.storeEncrypted('premium_variant_id', premiumVariantId);
  }

  static Future<void> loadProductVariants() async {
    final basicVariant = await SecureStorageService.getDecrypted('basic_variant_id');
    final premiumVariant = await SecureStorageService.getDecrypted('premium_variant_id');

    if (basicVariant != null) {
      _productVariants[SubscriptionPlan.basic] = basicVariant;
    }
    if (premiumVariant != null) {
      _productVariants[SubscriptionPlan.premium] = premiumVariant;
    }
  }

  static Future<List<Map<String, dynamic>>> getProducts() async {
    if (_apiKey == null || _storeId == null) {
      await loadCredentials();
      if (_apiKey == null || _storeId == null) return [];
    }

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/products?filter[store_id]=$_storeId'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Accept': 'application/vnd.api+json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data['data'] ?? []);
      }
    } catch (e) {
      print('Error getting products: $e');
    }
    return [];
  }
}
