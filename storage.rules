rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    function isAdmin() {
      return isAuthenticated() &&
        request.auth.token.email in ['<EMAIL>', '<EMAIL>'];
    }

    function isValidImageFile() {
      return request.resource.contentType.matches('image/.*') &&
        request.resource.size < 5 * 1024 * 1024; // 5MB limit
    }

    // Profile pictures - users can upload and read their own profile pictures
    match /profile_pictures/{userId}_{timestamp}.jpg {
      allow read: if isAuthenticated(); // Anyone can read profile pictures
      allow write: if isOwner(userId) && isValidImageFile();
      allow delete: if isOwner(userId) || isAdmin();
    }

    // Allow reading any profile picture (for viewing in app)
    match /profile_pictures/{allImages=**} {
      allow read: if isAuthenticated();
    }

    // Test connectivity (for debugging)
    match /test/{allFiles=**} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated(); // Allow for connectivity testing
    }

    // Admin access to all files
    match /{allPaths=**} {
      allow read, write: if isAdmin();
    }

    // Deny everything else
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
