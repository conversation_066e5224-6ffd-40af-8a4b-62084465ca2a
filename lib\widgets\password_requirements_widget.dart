import 'package:flutter/material.dart';
import '../utils/theme.dart';

class PasswordRequirement {
  final String text;
  final bool Function(String) validator;
  final IconData icon;

  PasswordRequirement({
    required this.text,
    required this.validator,
    required this.icon,
  });
}

class PasswordRequirementsWidget extends StatelessWidget {
  final String password;
  final bool showRequirements;

  const PasswordRequirementsWidget({
    super.key,
    required this.password,
    this.showRequirements = true,
  });

  static final List<PasswordRequirement> _requirements = [
    PasswordRequirement(
      text: 'At least 8 characters',
      validator: (password) => password.length >= 8,
      icon: Icons.straighten,
    ),
    PasswordRequirement(
      text: 'One uppercase letter (A-Z)',
      validator: (password) => password.contains(RegExp(r'[A-Z]')),
      icon: Icons.text_fields,
    ),
    PasswordRequirement(
      text: 'One lowercase letter (a-z)',
      validator: (password) => password.contains(RegExp(r'[a-z]')),
      icon: Icons.text_fields,
    ),
    PasswordRequirement(
      text: 'One number (0-9)',
      validator: (password) => password.contains(RegExp(r'[0-9]')),
      icon: Icons.numbers,
    ),
    PasswordRequirement(
      text: 'One special character (!@#\$%^&*)',
      validator: (password) => password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]')),
      icon: Icons.security,
    ),
  ];

  static bool isPasswordValid(String password) {
    return _requirements.every((req) => req.validator(password));
  }

  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    
    if (!isPasswordValid(value)) {
      return 'Password does not meet requirements';
    }
    
    return null;
  }

  @override
  Widget build(BuildContext context) {
    if (!showRequirements) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 16,
                color: AppTheme.textSecondary,
              ),
              const SizedBox(width: 8),
              Text(
                'Password Requirements',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textSecondary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ..._requirements.map((requirement) {
            final isValid = requirement.validator(password);
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: isValid ? Colors.green : Colors.grey.shade300,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      isValid ? Icons.check : requirement.icon,
                      size: 12,
                      color: isValid ? Colors.white : Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: AnimatedDefaultTextStyle(
                      duration: const Duration(milliseconds: 300),
                      style: TextStyle(
                        fontSize: 13,
                        color: isValid ? Colors.green.shade700 : AppTheme.textSecondary,
                        fontWeight: isValid ? FontWeight.w500 : FontWeight.normal,
                      ),
                      child: Text(requirement.text),
                    ),
                  ),
                ],
              ),
            );
          }),
          const SizedBox(height: 8),
          _buildPasswordStrengthIndicator(),
        ],
      ),
    );
  }

  Widget _buildPasswordStrengthIndicator() {
    final validCount = _requirements.where((req) => req.validator(password)).length;
    final strength = validCount / _requirements.length;
    
    Color strengthColor;
    String strengthText;
    
    if (strength <= 0.2) {
      strengthColor = Colors.red;
      strengthText = 'Very Weak';
    } else if (strength <= 0.4) {
      strengthColor = Colors.orange;
      strengthText = 'Weak';
    } else if (strength <= 0.6) {
      strengthColor = Colors.yellow.shade700;
      strengthText = 'Fair';
    } else if (strength <= 0.8) {
      strengthColor = Colors.blue;
      strengthText = 'Good';
    } else {
      strengthColor = Colors.green;
      strengthText = 'Strong';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Password Strength:',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              strengthText,
              style: TextStyle(
                fontSize: 12,
                color: strengthColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        Container(
          height: 4,
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(2),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: strength,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              decoration: BoxDecoration(
                color: strengthColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
