import 'package:equatable/equatable.dart';
import '../../models/notification_model.dart';

enum NotificationStatus {
  initial,
  loading,
  loaded,
  error,
}

class NotificationState extends Equatable {
  final NotificationStatus status;
  final List<NotificationModel> notifications;
  final int unreadCount;
  final String? error;
  final bool isRefreshing;

  const NotificationState({
    this.status = NotificationStatus.initial,
    this.notifications = const [],
    this.unreadCount = 0,
    this.error,
    this.isRefreshing = false,
  });

  factory NotificationState.initial() => const NotificationState();

  NotificationState copyWith({
    NotificationStatus? status,
    List<NotificationModel>? notifications,
    int? unreadCount,
    String? error,
    bool? isRefreshing,
  }) {
    return NotificationState(
      status: status ?? this.status,
      notifications: notifications ?? this.notifications,
      unreadCount: unreadCount ?? this.unreadCount,
      error: error ?? this.error,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }

  bool get hasNotifications => notifications.isNotEmpty;
  bool get hasUnreadNotifications => unreadCount > 0;
  
  List<NotificationModel> get unreadNotifications => 
      notifications.where((n) => !n.isRead).toList();
  
  List<NotificationModel> get readNotifications => 
      notifications.where((n) => n.isRead).toList();

  @override
  List<Object?> get props => [
        status,
        notifications,
        unreadCount,
        error,
        isRefreshing,
      ];
}
