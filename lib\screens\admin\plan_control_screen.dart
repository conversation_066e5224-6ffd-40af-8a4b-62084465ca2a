import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flight_fear_wellness_app/blocs/admin/admin_bloc.dart';
import 'package:flight_fear_wellness_app/blocs/admin/admin_event.dart';
import 'package:flight_fear_wellness_app/blocs/admin/admin_state.dart';
import 'package:flight_fear_wellness_app/utils/theme.dart';

class PlanControlScreen extends StatefulWidget {
  const PlanControlScreen({super.key});

  @override
  State<PlanControlScreen> createState() => _PlanControlScreenState();
}

class _PlanControlScreenState extends State<PlanControlScreen> {
  final Map<String, Map<String, TextEditingController>> _controllers = {};
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    context.read<AdminBloc>().add(LoadPlanConfiguration());
  }

  @override
  void dispose() {
    for (final planControllers in _controllers.values) {
      for (final controller in planControllers.values) {
        controller.dispose();
      }
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: BlocBuilder<AdminBloc, AdminState>(
        builder: (context, state) {
          if (state.status == AdminStatus.loading) {
            return const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),)
            );
          }

          if (state.status == AdminStatus.error) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline_rounded, size: 64, color: AppTheme.errorColor),
                  const SizedBox(height: 24),
                  Text('Error loading plan configuration:', style: Theme.of(context).textTheme.titleMedium),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Text(state.error ?? 'Unknown error', 
                      textAlign: TextAlign.center,
                      style: TextStyle(color: AppTheme.textSecondary),),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => context.read<AdminBloc>().add(LoadPlanConfiguration()),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                    ),),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final planConfig = state.planConfiguration;
          if (planConfig == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.price_change_rounded, size: 64, color: AppTheme.primaryColor),
                  const SizedBox(height: 16),
                  Text('No plan configuration available', style: Theme.of(context).textTheme.titleMedium),
                  const SizedBox(height: 8),
                  Text('Configure your subscription plans', style: TextStyle(color: AppTheme.textSecondary)),
                ],
              ),
            );
          }

          _initializeControllers(planConfig);

          return SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Card(
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                    side: const BorderSide(color: AppTheme.borderColor, width: 1),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info_outline_rounded, size: 24, color: AppTheme.primaryColor),
                            const SizedBox(width: 12),
                            Text(
                              'Plan Information',
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        _buildInfoItem('Changes to plan configuration will affect new subscriptions only'),
                        _buildInfoItem('Existing users keep current plan limits until renewal'),
                        _buildInfoItem('Price changes require updating payment gateway configuration'),
                        _buildInfoItem('Credit limits are enforced by the backend system'),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 32),
                
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Plan Configuration',
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.w800,
                      ),
                    ),
                    if (!_isEditing)
                      ElevatedButton.icon(
                        onPressed: () => setState(() => _isEditing = true),
                        icon: const Icon(Icons.edit_rounded),
                        label: const Text('Edit Plans'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 24),
                
                ...planConfig.plans.entries.map((entry) {
                  final planName = entry.key;
                  final planData = entry.value;
                  
                  return Container(
                    margin: const EdgeInsets.only(bottom: 24),
                    child: Card(
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                        side: const BorderSide(color: AppTheme.borderColor, width: 1),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: _getPlanColor(planName).withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(color: _getPlanColor(planName)),
                                  ),
                                  child: Text(
                                    planData['name'] ?? planName.toUpperCase(),
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w800,
                                      color: _getPlanColor(planName),
                                    ),
                                  ),
                                ),
                                const Spacer(),
                                if (_isEditing) ...[
                                  TextButton(
                                    onPressed: _cancelEditing,
                                    child: const Text('Cancel', style: TextStyle(color: AppTheme.textSecondary)),
                                  ),
                                  const SizedBox(width: 8),
                                  ElevatedButton(
                                    onPressed: () => _savePlanConfiguration(context),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppTheme.primaryColor,
                                      foregroundColor: Colors.white,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),)
                                    ),
                                    child: const Text('Save'),
                                  ),
                                ]
                              ],
                            ),
                            const SizedBox(height: 24),
                            
                            Row(
                              children: [
                                Expanded(
                                  child: _buildEditableField(
                                    'Price (\$)',
                                    planName,
                                    'price',
                                    planData['price'].toString(),
                                    Icons.attach_money_rounded,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: _buildEditableField(
                                    'Duration (days)',
                                    planName,
                                    'duration',
                                    planData['duration'].toString(),
                                    Icons.calendar_today_rounded,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 24),
                            
                            Row(
                              children: [
                                Icon(Icons.credit_card_rounded, size: 24, color: AppTheme.primaryColor),
                                const SizedBox(width: 8),
                                Text(
                                  'Credits',
                                  style: Theme.of(context).textTheme.titleMedium,
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            
                            Row(
                              children: [
                                Expanded(
                                  child: _buildEditableField(
                                    'Chat Credits',
                                    planName,
                                    'chat_credits',
                                    planData['credits']['chat'].toString(),
                                    Icons.chat_bubble_outline_rounded,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: _buildEditableField(
                                    'Voice Credits',
                                    planName,
                                    'voice_credits',
                                    planData['credits']['voice'].toString(),
                                    Icons.mic_outlined,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildInfoItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.only(top: 4),
            child: Icon(Icons.circle, size: 8, color: AppTheme.textSecondary),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                color: AppTheme.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditableField(String label, String planName, String field, String value, IconData icon) {
    final controller = _controllers[planName]![field]!;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 18, color: AppTheme.textSecondary),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppTheme.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _isEditing ? AppTheme.primaryColor : Colors.transparent,
              width: 1.5,
            ),
          ),
          child: TextField(
            controller: controller,
            enabled: _isEditing,
            keyboardType: TextInputType.number,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            decoration: InputDecoration(
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              filled: true,
              fillColor: _isEditing ? Colors.white : Colors.grey[50],
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _initializeControllers(PlanConfiguration planConfig) {
    for (final entry in planConfig.plans.entries) {
      final planName = entry.key;
      final planData = entry.value;
      
      if (!_controllers.containsKey(planName)) {
        _controllers[planName] = {
          'price': TextEditingController(text: planData['price'].toString()),
          'duration': TextEditingController(text: planData['duration'].toString()),
          'chat_credits': TextEditingController(text: planData['credits']['chat'].toString()),
          'voice_credits': TextEditingController(text: planData['credits']['voice'].toString()),
        };
      }
    }
  }

  void _cancelEditing() {
    setState(() => _isEditing = false);
    context.read<AdminBloc>().add(LoadPlanConfiguration());
  }

  void _savePlanConfiguration(BuildContext context) {
    try {
      final updatedPlans = <String, Map<String, dynamic>>{};
      
      for (final planName in _controllers.keys) {
        final controllers = _controllers[planName]!;
        
        updatedPlans[planName] = {
          'name': '${planName[0].toUpperCase()}${planName.substring(1)} Plan',
          'price': int.parse(controllers['price']!.text),
          'duration': int.parse(controllers['duration']!.text),
          'credits': {
            'chat': int.parse(controllers['chat_credits']!.text),
            'voice': int.parse(controllers['voice_credits']!.text),
          },
        };
      }
      
      context.read<AdminBloc>().add(UpdatePlanConfiguration(planConfig: updatedPlans));
      setState(() => _isEditing = false);
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle_rounded, color: Colors.white),
              const SizedBox(width: 8),
              const Text('Plan configuration updated successfully'),
            ],
          ),
          backgroundColor: AppTheme.successColor,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error_outline_rounded, color: Colors.white),
              const SizedBox(width: 8),
              Text('Error updating configuration: $e'),
            ],
          ),
          backgroundColor: AppTheme.errorColor,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
      );
    }
  }

  Color _getPlanColor(String plan) {
    switch (plan) {
      case 'basic':
        return Colors.blue;
      case 'premium':
        return Colors.amber;
      default:
        return AppTheme.primaryColor;
    }
  }
}