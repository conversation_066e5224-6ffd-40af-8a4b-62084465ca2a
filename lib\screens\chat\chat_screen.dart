import 'package:flight_fear_wellness_app/blocs/chat/chat_event.dart';
import 'package:flight_fear_wellness_app/blocs/chat/chat_state.dart';
import 'package:flight_fear_wellness_app/blocs/auth/auth_state.dart';
import 'package:flight_fear_wellness_app/utils/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flight_fear_wellness_app/blocs/auth/auth_bloc.dart';
import 'package:flight_fear_wellness_app/blocs/chat/chat_bloc.dart';
import 'package:flight_fear_wellness_app/models/user_model.dart';
import 'package:flight_fear_wellness_app/widgets/chat_bubble.dart';
import 'package:flight_fear_wellness_app/widgets/custom_text_field.dart';
import 'package:flight_fear_wellness_app/widgets/typing_indicator.dart';
import 'package:flight_fear_wellness_app/widgets/crisis_intervention_widget.dart';

// Purpose: Chat interface for AI-powered flight fear therapy conversations
class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final _messageController = TextEditingController();
  final _scrollController = ScrollController();
  late UserModel _currentUser;

  @override
  void initState() {
    super.initState();
    final authState = context.read<AuthBloc>().state;
    if (authState.status == AuthStatus.authenticated) {
      _currentUser = authState.user!;
      context.read<ChatBloc>().add(LoadChatHistoryEvent(_currentUser.id));
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // Purpose: Auto-scroll chat to show latest messages
  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Alora Chat', style: TextStyle(
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
        )),
        centerTitle: true,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.delete_outline_rounded),
            onPressed: () {
              context.read<ChatBloc>().add(ClearChatHistoryEvent(_currentUser.id));
            },
            tooltip: 'Clear chat history',
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white,
              AppTheme.primaryColor.withOpacity(0.03),
            ],
          ),
        ),
        child: BlocConsumer<ChatBloc, ChatState>(
          listener: (context, state) {
            if (state.messages.isNotEmpty) {
              WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToBottom());
            }

            if (state.error != null) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.error!),
                  backgroundColor: Colors.red,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),),
                  action: SnackBarAction(
                    label: 'Retry',
                    textColor: Colors.white,
                    onPressed: () {},
                  ),
                ),
              );
            }
          },
          builder: (context, state) {
            return Column(
              children: [
                Expanded(
                  child: state.messages.isEmpty && state.status == ChatStatus.initial
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.chat_bubble_outline_rounded,
                                size: 64,
                                color: AppTheme.primaryColor.withOpacity(0.2),
                              ),
                              const SizedBox(height: 24),
                              const Text(
                                'Start a Conversation',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 12),
                              Text(
                                'Alora is here to support you\nShare your thoughts or concerns',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: AppTheme.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        )
                      : Column(
                          children: [
                            Expanded(
                              child: ListView.builder(
                                controller: _scrollController,
                                padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
                                itemCount: state.messages.length + (state.isGeneratingResponse ? 1 : 0),
                                itemBuilder: (context, index) {
                                  if (index == state.messages.length && state.isGeneratingResponse) {
                                    return const Padding(
                                      padding: EdgeInsets.symmetric(vertical: 16, horizontal: 24),
                                      child: TypingIndicator(),
                                    );
                                  }

                                  final message = state.messages[index];
                                  final showAvatar = index == 0 ||
                                      index == state.messages.length - 1 ||
                                      message.type != state.messages[index - 1].type;

                                  return ChatBubble(
                                    message: message,
                                    showAvatar: showAvatar,
                                  );
                                },
                              ),
                            ),
                            if (state.crisisOptions.isNotEmpty)
                              CrisisInterventionWidget(
                                options: state.crisisOptions,
                                userId: _currentUser.id,
                                moodLevel: state.currentMoodLevel ?? 'normal',
                                onClose: () {
                                  context.read<ChatBloc>().add(
                                    CloseCrisisInterventionEvent(
                                      userId: _currentUser.id,
                                    ),
                                  );
                                },
                              ),
                            if (state.shouldOfferDebate && state.debateTopic?.isNotEmpty == true)
                              DebateOfferWidget(
                                topic: state.debateTopic!,
                                userId: _currentUser.id,
                              ),
                          ],
                        ),
                ),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 12,
                        offset: const Offset(0, -4),
                      ),
                    ],
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.grey.shade50,
                            borderRadius: BorderRadius.circular(24),
                          ),
                          child: CustomTextField(
                            controller: _messageController,
                            hintText: 'Type your message...',
                            isPassword: false,
                            textInputAction: TextInputAction.send,
                            onSubmitted: (value) => _sendMessage(),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              AppTheme.primaryColor,
                              AppTheme.primaryColor.withValues(alpha: 0.8),
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme.primaryColor.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: IconButton(
                          icon: const Icon(Icons.send_rounded, color: Colors.white),
                          onPressed: _sendMessage,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  // Purpose: Send user message to AI and handle response
  void _sendMessage() {
    final message = _messageController.text.trim();
    if (message.isNotEmpty) {
      context.read<ChatBloc>().add(
            SendMessageEvent(
              message: message,
              userId: _currentUser.id,
            ),
          );
      _messageController.clear();
    }
  }
}