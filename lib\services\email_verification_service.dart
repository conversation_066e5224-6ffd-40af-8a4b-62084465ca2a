import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/api_response.dart';
import '../utils/constants.dart';

class EmailVerificationService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Future<ApiResponse<bool>> checkEmailExistsInAuth(String email) async {
    try {
      if (!_isValidEmailFormat(email)) {
        return ApiResponse.success(false);
      }

      return ApiResponse.success(true);
    } catch (e) {
      return ApiResponse.error('Network error occurred');
    }
  }

   Future<ApiResponse<bool>> checkEmailAvailableForSignup(String email) async {
    try {
      if (!_isValidEmailFormat(email)) {
        return ApiResponse.success(false);
      }

      final firestoreResult = await checkEmailExistsInFirestore(email);

      if (firestoreResult.success) {
        final isAvailable = !firestoreResult.data!;
        return ApiResponse.success(isAvailable);
      }

      return ApiResponse.success(true);
    } catch (e) {
      return ApiResponse.error('Email check failed: ${e.toString()}');
    }
  }


  Future<ApiResponse<bool>> checkEmailExistsInFirestore(String email) async {
    try {
      print('DEBUG: Checking email in Firestore: ${email.trim().toLowerCase()}');

      final cleanEmail = email.trim().toLowerCase();
      final query = await _firestore
          .collection(AppConstants.usersCollection)
          .where('email', isEqualTo: cleanEmail)
          .limit(1)
          .get();

      print('DEBUG: Firestore query result - docs found: ${query.docs.length}');
      if (query.docs.isNotEmpty) {
        print('DEBUG: Found user with email: ${query.docs.first.data()}');
      }

       return ApiResponse.success(query.docs.isNotEmpty);
    } catch (e) {
      return ApiResponse.error('Firestore query error: ${e.toString()}');
    }
  }

  Future<ApiResponse<EmailValidationResult>> validateEmailForLogin(String email) async {
    try {
      if (!_isValidEmailFormat(email)) {
        return ApiResponse.success(EmailValidationResult(
          isValid: false,
          exists: false,
          message: 'Invalid email format',
          canProceed: false,
        ));
      }

      return ApiResponse.success(EmailValidationResult(
        isValid: true,
        exists: false,
        message: '',
        canProceed: true,
      ));
    } catch (e) {
      return ApiResponse.error('Email validation failed: ${e.toString()}');
    }
  }

  Future<ApiResponse<EmailValidationResult>> validateEmailForSignup(String email) async {
    try {
      if (!_isValidEmailFormat(email)) {
        return ApiResponse.success(EmailValidationResult(
          isValid: false,
          exists: false,
          message: 'Invalid email format',
          canProceed: false,
        ));
      }

      final availabilityResult = await checkEmailAvailableForSignup(email);
      if (!availabilityResult.success) {
        return ApiResponse.error(availabilityResult.error ?? 'Email validation failed');
      }

      final isAvailable = availabilityResult.data ?? false;

      return ApiResponse.success(EmailValidationResult(
        isValid: true,
        exists: !isAvailable,
        message: '',
        canProceed: isAvailable,
      ));
    } catch (e) {
      return ApiResponse.error('Email validation failed: ${e.toString()}');
    }
  }

  Future<ApiResponse<bool>> sendEmailVerification() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return ApiResponse.error('No user signed in');
      }

      if (user.emailVerified) {
        return ApiResponse.success(true);
      }

      await user.sendEmailVerification();
      return ApiResponse.success(true);
    } on FirebaseAuthException catch (e) {
      return ApiResponse.error('Failed to send verification email: ${e.message}');
    } catch (e) {
      return ApiResponse.error('Failed to send verification email: ${e.toString()}');
    }
  }

  Future<ApiResponse<bool>> isEmailVerified() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return ApiResponse.error('No user signed in');
      }

      await user.reload();
      final updatedUser = _auth.currentUser;
      
      return ApiResponse.success(updatedUser?.emailVerified ?? false);
    } catch (e) {
      return ApiResponse.error('Failed to check verification status: ${e.toString()}');
    }
  }

  Future<ApiResponse<bool>> resendVerificationEmail() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return ApiResponse.error('No user signed in');
      }

      await user.sendEmailVerification();
      return ApiResponse.success(true);
    } on FirebaseAuthException catch (e) {
      if (e.code == 'too-many-requests') {
        return ApiResponse.error('Too many requests. Please wait before requesting another verification email.');
      }
      return ApiResponse.error('Failed to resend verification email: ${e.message}');
    } catch (e) {
      return ApiResponse.error('Failed to resend verification email: ${e.toString()}');
    }
  }

  bool _isValidEmailFormat(String email) {
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    return emailRegex.hasMatch(email.trim());
  }

  bool _isValidDomain(String email) {
    final domain = email.split('@').last.toLowerCase();
    final invalidDomains = [
      'test.com', 'example.com', 'fake.com', 'invalid.com',
      'temp.com', 'throwaway.com', '10minutemail.com'
    ];
    return !invalidDomains.contains(domain);
  }
}

class EmailValidationResult {
  final bool isValid;
  final bool exists;
  final String message;
  final bool canProceed;

  EmailValidationResult({
    required this.isValid,
    required this.exists,
    required this.message,
    required this.canProceed,
  });
}
