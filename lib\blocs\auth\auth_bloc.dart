import 'package:bloc/bloc.dart';
import 'package:flight_fear_wellness_app/blocs/auth/auth_event.dart';
import 'package:flight_fear_wellness_app/blocs/auth/auth_state.dart';
import 'package:flight_fear_wellness_app/repositories/auth_repository.dart';
import 'package:flight_fear_wellness_app/services/notification_service.dart';
import 'package:flutter/foundation.dart';

// Purpose: Manage authentication state and handle auth-related events
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthRepository authRepository;

  AuthBloc({required this.authRepository}) : super(AuthState.initial()) {
    on<SignUpEvent>(_onSignUp);
    on<SignInEvent>(_onSignIn);
    on<SignOutEvent>(_onSignOut);
    on<ResetPasswordEvent>(_onResetPassword);
    on<CheckAuthenticationStatus>(_onCheckAuthStatus);
  }

 // Purpose: Handle user registration with email verification
 Future<void> _onSignUp(
  SignUpEvent event,
  Emitter<AuthState> emit,
) async {
  emit(AuthState.loading());
  try {
    final result = await authRepository.signUp(
      email: event.email,
      password: event.password,
      name: event.name,
    );

    // Handle the result synchronously to avoid emit after completion
    await result.fold(
      (error) async {
        debugPrint('SignUp Error: $error');
        emit(AuthState.error(error));
      },
      (user) async {
        debugPrint('User created successfully: ${user.email}');

        // Initialize notifications for new user
        try {
          debugPrint('AuthBloc: Initializing notifications for new user: ${user.email}');
          await NotificationService().initializeForUser(user.id, isNewUser: true);
          debugPrint('AuthBloc: ✅ Notifications initialized for new user: ${user.email}');
        } catch (e) {
          debugPrint('AuthBloc: ⚠️ Failed to initialize notifications for new user: $e');
          // Don't fail the signup process if notifications fail
        }

        emit(AuthState.authenticated(user));
      },
    );
  } catch (e) {
    debugPrint('Unexpected error: $e');
    emit(AuthState.error('An unexpected error occurred'));
  }
}

// Purpose: Handle user login and authentication
Future<void> _onSignIn(
  SignInEvent event,
  Emitter<AuthState> emit,
) async {
  print('AuthBloc: Starting sign in process for ${event.email}');

  emit(AuthState.loading());

  try {
    print('AuthBloc: Calling authRepository.signIn');
    final result = await authRepository.signIn(
      email: event.email,
      password: event.password,
    );

    print('AuthBloc: Sign in result received, processing...');

    // Handle the result synchronously to avoid emit after completion
    await result.fold(
      (error) async {
        print('AuthBloc: Sign in failed with error: $error');
        if (error == 'EMAIL_NOT_VERIFIED') {
          print('AuthBloc: ❌ Emitting emailNotVerified state - user will be redirected');
          emit(AuthState.emailNotVerified());
        } else {
          print('AuthBloc: ❌ Emitting error state: $error');
          emit(AuthState.error(error));
        }
      },
      (user) async {
        print('AuthBloc: ✅ Sign in successful for user: ${user.email}');

        // Initialize notifications for the authenticated user
        try {
          print('AuthBloc: Initializing notifications for user: ${user.email}');
          await NotificationService().initializeForUser(user.id, isNewUser: false);
          print('AuthBloc: ✅ Notifications initialized for user: ${user.email}');
        } catch (e) {
          print('AuthBloc: ⚠️ Failed to initialize notifications: $e');
          // Don't fail the login process if notifications fail
        }

        print('AuthBloc: ✅ Emitting authenticated state - user will access app');
        emit(AuthState.authenticated(user));
      },
    );
  } catch (e) {
    print('AuthBloc: Unexpected error during sign in: $e');
    emit(AuthState.error('Sign in failed: ${e.toString()}'));
  }
}

  // Purpose: Handle user logout and session cleanup
Future<void> _onSignOut(
    SignOutEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthState.loading());
    final result = await authRepository.signOut();
    result.fold(
      (error) => emit(AuthState.error(error)),
      (_) => emit(AuthState.unauthenticated()),
    );
  }

  Future<void> _onCheckAuthStatus(
    CheckAuthenticationStatus event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthState.loading());
    final result = await authRepository.getCurrentUser();
    result.fold(
      (error) => emit(AuthState.unauthenticated()),
      (user) => emit(AuthState.authenticated(user)),
    );
  }

  // Purpose: Handle password reset email sending
Future<void> _onResetPassword(
    ResetPasswordEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthState.loading());
    try {
      final result = await authRepository.resetPassword(email: event.email);

      result.fold(
        (error) {
          debugPrint('Password reset error: $error');
          emit(AuthState.error(error));
        },
        (success) {
          debugPrint('Password reset email sent successfully');
          emit(AuthState.passwordResetSent());
        },
      );
    } catch (e) {
      debugPrint('Unexpected error during password reset: $e');
      emit(AuthState.error('Password reset failed: ${e.toString()}'));
    }
  }

}