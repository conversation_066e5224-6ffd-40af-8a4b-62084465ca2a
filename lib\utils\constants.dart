import 'package:flutter_dotenv/flutter_dotenv.dart';

class AppConstants {
  // Purpose: Get API keys from environment variables with secure fallbacks
  // Priority: 1. Environment variables (.env) 2. Secure storage 3. Empty string (handled by services)
  static String get openAiApiKey => dotenv.env['OPENAI_API_KEY'] ?? '';
  static String get elevenLabsApiKey => dotenv.env['ELEVENLABS_API_KEY'] ?? '';
 
  
  static const String openAiBaseUrl = 'https://api.openai.com/v1';
  static const String elevenLabsBaseUrl = 'https://api.elevenlabs.io/v1';
  static const String didBaseUrl = 'https://api.d-id.com';
  
  static const String usersCollection = 'users';
  static const String chatMessagesCollection = 'chat_messages';
  static const String therapyContextCollection = 'therapy_contexts';
  static const String voiceSessionsCollection = 'voice_sessions';
  static const String videoSessionsCollection = 'video_sessions';
  static const String adminLogsCollection = 'admin_logs';
  static const String appConfigCollection = 'app_config';
  static const String userMemoryCollection = 'user_memory';
  static const String memoryAnalysisCollection = 'memory_analysis';

  static const List<String> adminEmails = [
    "owner1@mydomain.com2",
    "<EMAIL>"
  ];
  
  static const String appName = 'ALORA';
  static const int chatMessageLimit = 100;
  static const Duration apiTimeout = Duration(seconds: 30);

  static const String defaultVoiceId = 'EXAVITQu4vr4xnSDxMaL';
  static const Map<String, dynamic> voiceSettings = {
    'stability': 0.75,
    'similarity_boost': 0.75,
    'style': 0.5,
    'use_speaker_boost': true
  };
  
  
}