import 'package:flutter/material.dart';
import '../../utils/theme.dart';
import '../../services/secure_storage_service.dart';

class CreditWarningDialog extends StatefulWidget {
  final String title;
  final String message;
  final VoidCallback? onUpgrade;
  final bool showDontShowAgain;

  const CreditWarningDialog({
    super.key,
    required this.title,
    required this.message,
    this.onUpgrade,
    this.showDontShowAgain = true,
  });

  @override
  State<CreditWarningDialog> createState() => _CreditWarningDialogState();
}

class _CreditWarningDialogState extends State<CreditWarningDialog> {
  bool _dontShowAgain = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            color: Colors.orange,
            size: 28,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              widget.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.message,
            style: const TextStyle(
              fontSize: 16,
              height: 1.4,
            ),
          ),
          if (widget.showDontShowAgain) ...[
            const SizedBox(height: 16),
            Row(
              children: [
                Checkbox(
                  value: _dontShowAgain,
                  onChanged: (value) {
                    setState(() {
                      _dontShowAgain = value ?? false;
                    });
                  },
                  activeColor: AppTheme.primaryColor,
                ),
                const Expanded(
                  child: Text(
                    "Don't show again today",
                    style: TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
      actions: [
        TextButton(
          onPressed: () async {
            if (_dontShowAgain) {
              await _saveDontShowAgainPreference();
            }
            Navigator.of(context).pop();
          },
          child: const Text(
            'Cancel',
            style: TextStyle(
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        if (widget.onUpgrade != null)
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onUpgrade!();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
            ),
            child: const Text(
              'Upgrade',
              style: TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }

  Future<void> _saveDontShowAgainPreference() async {
    final today = DateTime.now();
    final dateKey = '${today.year}-${today.month}-${today.day}';
    await SecureStorageService.storeEncrypted(
      'credit_warning_suppressed_$dateKey',
      'true',
    );
  }

  static Future<bool> shouldSuppressWarning() async {
    final today = DateTime.now();
    final dateKey = '${today.year}-${today.month}-${today.day}';
    final suppressed = await SecureStorageService.getDecrypted(
      'credit_warning_suppressed_$dateKey',
    );
    return suppressed == 'true';
  }

  static Future<void> showWarningIfNeeded({
    required BuildContext context,
    required String title,
    required String message,
    VoidCallback? onUpgrade,
  }) async {
    final shouldSuppress = await shouldSuppressWarning();
    if (!shouldSuppress && context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => CreditWarningDialog(
          title: title,
          message: message,
          onUpgrade: onUpgrade,
        ),
      );
    }
  }
}
