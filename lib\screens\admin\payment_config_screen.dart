import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../services/payment_config_service.dart';
import '../../models/payment_models.dart';
import '../../services/secure_storage_service.dart';
import '../../utils/theme.dart';
import 'dart:convert';

class PaymentConfigScreen extends StatefulWidget {
  const PaymentConfigScreen({super.key});

  @override
  State<PaymentConfigScreen> createState() => _PaymentConfigScreenState();
}

class _PaymentConfigScreenState extends State<PaymentConfigScreen> {
  final _formKey = GlobalKey<FormState>();
  
  final _lemonApiKeyController = TextEditingController();
  final _lemonStoreIdController = TextEditingController();
  final _lemonWebhookSecretController = TextEditingController();
  final _lemonBasicPlanController = TextEditingController();
  final _lemonPremiumPlanController = TextEditingController();
  
  final _stripeSecretKeyController = TextEditingController();
  final _stripeWebhookSecretController = TextEditingController();
  final _stripeBasicPriceController = TextEditingController();
  final _stripePremiumPriceController = TextEditingController();
  
  final _paypalClientIdController = TextEditingController();
  final _paypalClientSecretController = TextEditingController();
  final _paypalWebhookIdController = TextEditingController();
  final _paypalBasicPlanController = TextEditingController();
  final _paypalPremiumPlanController = TextEditingController();
  
  Map<String, dynamic> _configStatus = {};
  Map<PaymentProvider, Map<String, String>> _existingCredentials = {};
  bool _isLoading = false;
  String? _editingProvider;
  final Map<String, TextEditingController> _originalValues = {};

  @override
  void initState() {
    super.initState();
    _loadConfigurationStatus();
  }

  @override
  void dispose() {
    _lemonApiKeyController.dispose();
    _lemonStoreIdController.dispose();
    _lemonWebhookSecretController.dispose();
    _lemonBasicPlanController.dispose();
    _lemonPremiumPlanController.dispose();
    
    _stripeSecretKeyController.dispose();
    _stripeWebhookSecretController.dispose();
    _stripeBasicPriceController.dispose();
    _stripePremiumPriceController.dispose();
    
    _paypalClientIdController.dispose();
    _paypalClientSecretController.dispose();
    _paypalWebhookIdController.dispose();
    _paypalBasicPlanController.dispose();
    _paypalPremiumPlanController.dispose();
    
    super.dispose();
  }

  Future<void> _loadConfigurationStatus() async {
    setState(() {
      _configStatus = PaymentConfigService.getConfigurationStatus();
    });
    await _loadExistingCredentials();
  }

  Future<void> _loadExistingCredentials() async {
    try {
      final configsJson = await SecureStorageService.getDecrypted('payment_configs');
      if (configsJson != null) {
        final configsMap = jsonDecode(configsJson) as Map<String, dynamic>;

        setState(() {
          _existingCredentials = _extractMaskedCredentials(configsMap);
        });
      }
    } catch (e) {
      debugPrint('Error loading existing credentials: $e');
    }
  }

  Map<PaymentProvider, Map<String, String>> _extractMaskedCredentials(Map<String, dynamic> configsMap) {
    final credentials = <PaymentProvider, Map<String, String>>{};

    for (final entry in configsMap.entries) {
      final providerName = entry.key;
      final config = entry.value as Map<String, dynamic>;

      PaymentProvider? provider;
      try {
        provider = PaymentProvider.values.firstWhere((p) => p.name == providerName);
      } catch (e) {
        continue;
      }

      final masked = <String, String>{};

      switch (provider) {
        case PaymentProvider.lemonsqueezy:
          masked['apiKey'] = _maskString(config['apiKey'] ?? '');
          masked['storeId'] = config['storeId'] ?? '';
          masked['webhookSecret'] = _maskString(config['webhookSecret'] ?? '');
          final planIds = config['planIds'] as Map<String, dynamic>? ?? {};
          masked['basicPlanId'] = planIds['basic'] ?? '';
          masked['premiumPlanId'] = planIds['premium'] ?? '';
          break;
        case PaymentProvider.stripe:
          masked['secretKey'] = _maskString(config['apiKey'] ?? '');
          masked['webhookSecret'] = _maskString(config['webhookSecret'] ?? '');
          final planIds = config['planIds'] as Map<String, dynamic>? ?? {};
          masked['basicPriceId'] = planIds['basic'] ?? '';
          masked['premiumPriceId'] = planIds['premium'] ?? '';
          break;
        case PaymentProvider.paypal:
          masked['clientId'] = _maskString(config['apiKey'] ?? '');
          masked['clientSecret'] = _maskString(config['secretKey'] ?? '');
          masked['webhookId'] = _maskString(config['webhookSecret'] ?? '');
          final planIds = config['planIds'] as Map<String, dynamic>? ?? {};
          masked['basicPlanId'] = planIds['basic'] ?? '';
          masked['premiumPlanId'] = planIds['premium'] ?? '';
          break;
      }

      credentials[provider] = masked;
    }

    return credentials;
  }

  String _maskString(String value) {
    if (value.isEmpty) return '';
    if (value.length <= 4) return '***';
    return '***${value.substring(value.length - 4)}';
  }

  Future<void> _configureLemonSqueezy() async {
    if (!PaymentConfigService.validateLemonSqueezyConfig(
      apiKey: _lemonApiKeyController.text,
      storeId: _lemonStoreIdController.text,
      webhookSecret: _lemonWebhookSecretController.text,
      basicPlanId: _lemonBasicPlanController.text,
      premiumPlanId: _lemonPremiumPlanController.text,
    )) {
      _showError('Please fill in all LemonSqueezy fields correctly');
      return;
    }

    setState(() => _isLoading = true);

    final success = await PaymentConfigService.configureLemonSqueezy(
      apiKey: _lemonApiKeyController.text,
      storeId: _lemonStoreIdController.text,
      webhookSecret: _lemonWebhookSecretController.text,
      basicPlanId: _lemonBasicPlanController.text,
      premiumPlanId: _lemonPremiumPlanController.text,
      isTestMode: true,
    );

    setState(() => _isLoading = false);

    if (success) {
      _showSuccess('LemonSqueezy configured successfully');
      _clearControllers();
      _loadConfigurationStatus();
    } else {
      _showError('Failed to configure LemonSqueezy');
    }
  }

  Future<void> _configureStripe() async {
    if (!PaymentConfigService.validateStripeConfig(
      secretKey: _stripeSecretKeyController.text,
      webhookSecret: _stripeWebhookSecretController.text,
      basicPriceId: _stripeBasicPriceController.text,
      premiumPriceId: _stripePremiumPriceController.text,
    )) {
      _showError('Please fill in all Stripe fields correctly');
      return;
    }

    setState(() => _isLoading = true);

    final success = await PaymentConfigService.configureStripe(
      secretKey: _stripeSecretKeyController.text,
      webhookSecret: _stripeWebhookSecretController.text,
      basicPriceId: _stripeBasicPriceController.text,
      premiumPriceId: _stripePremiumPriceController.text,
      isTestMode: true,
    );

    setState(() => _isLoading = false);

    if (success) {
      _showSuccess('Stripe configured successfully');
      _clearControllers();
      _loadConfigurationStatus();
    } else {
      _showError('Failed to configure Stripe');
    }
  }

  Future<void> _configurePayPal() async {
    if (!PaymentConfigService.validatePayPalConfig(
      clientId: _paypalClientIdController.text,
      clientSecret: _paypalClientSecretController.text,
      webhookId: _paypalWebhookIdController.text,
      basicPlanId: _paypalBasicPlanController.text,
      premiumPlanId: _paypalPremiumPlanController.text,
    )) {
      _showError('Please fill in all PayPal fields correctly');
      return;
    }

    setState(() => _isLoading = true);

    final success = await PaymentConfigService.configurePayPal(
      clientId: _paypalClientIdController.text,
      clientSecret: _paypalClientSecretController.text,
      webhookId: _paypalWebhookIdController.text,
      basicPlanId: _paypalBasicPlanController.text,
      premiumPlanId: _paypalPremiumPlanController.text,
      isTestMode: true,
    );

    setState(() => _isLoading = false);

    if (success) {
      _showSuccess('PayPal configured successfully');
      _clearControllers();
      _loadConfigurationStatus();
    } else {
      _showError('Failed to configure PayPal');
    }
  }

  Future<void> _updateLemonSqueezy() async {
    final existing = _existingCredentials[PaymentProvider.lemonsqueezy];
    if (existing == null) {
      _showError('No existing LemonSqueezy configuration found');
      return;
    }

    setState(() => _isLoading = true);

    final success = await PaymentConfigService.configureLemonSqueezy(
      apiKey: _lemonApiKeyController.text.isNotEmpty ? _lemonApiKeyController.text : existing['apiKey']!.replaceAll('***', ''),
      storeId: _lemonStoreIdController.text.isNotEmpty ? _lemonStoreIdController.text : existing['storeId']!,
      webhookSecret: _lemonWebhookSecretController.text.isNotEmpty ? _lemonWebhookSecretController.text : existing['webhookSecret']!.replaceAll('***', ''),
      basicPlanId: _lemonBasicPlanController.text.isNotEmpty ? _lemonBasicPlanController.text : existing['basicPlanId']!,
      premiumPlanId: _lemonPremiumPlanController.text.isNotEmpty ? _lemonPremiumPlanController.text : existing['premiumPlanId']!,
      isTestMode: true,
    );

    setState(() => _isLoading = false);

    if (success) {
      _showSuccess('LemonSqueezy updated successfully');
      _clearControllers();
      _loadConfigurationStatus();
    } else {
      _showError('Failed to update LemonSqueezy');
    }
  }

  Future<void> _updateStripe() async {
    final existing = _existingCredentials[PaymentProvider.stripe];
    if (existing == null) {
      _showError('No existing Stripe configuration found');
      return;
    }

    setState(() => _isLoading = true);

    final success = await PaymentConfigService.configureStripe(
      secretKey: _stripeSecretKeyController.text.isNotEmpty ? _stripeSecretKeyController.text : existing['secretKey']!.replaceAll('***', ''),
      webhookSecret: _stripeWebhookSecretController.text.isNotEmpty ? _stripeWebhookSecretController.text : existing['webhookSecret']!.replaceAll('***', ''),
      basicPriceId: _stripeBasicPriceController.text.isNotEmpty ? _stripeBasicPriceController.text : existing['basicPriceId']!,
      premiumPriceId: _stripePremiumPriceController.text.isNotEmpty ? _stripePremiumPriceController.text : existing['premiumPriceId']!,
      isTestMode: true,
    );

    setState(() => _isLoading = false);

    if (success) {
      _showSuccess('Stripe updated successfully');
      _clearControllers();
      _loadConfigurationStatus();
    } else {
      _showError('Failed to update Stripe');
    }
  }

  Future<void> _updatePayPal() async {
    final existing = _existingCredentials[PaymentProvider.paypal];
    if (existing == null) {
      _showError('No existing PayPal configuration found');
      return;
    }

    setState(() => _isLoading = true);

    final success = await PaymentConfigService.configurePayPal(
      clientId: _paypalClientIdController.text.isNotEmpty ? _paypalClientIdController.text : existing['clientId']!.replaceAll('***', ''),
      clientSecret: _paypalClientSecretController.text.isNotEmpty ? _paypalClientSecretController.text : existing['clientSecret']!.replaceAll('***', ''),
      webhookId: _paypalWebhookIdController.text.isNotEmpty ? _paypalWebhookIdController.text : existing['webhookId']!.replaceAll('***', ''),
      basicPlanId: _paypalBasicPlanController.text.isNotEmpty ? _paypalBasicPlanController.text : existing['basicPlanId']!,
      premiumPlanId: _paypalPremiumPlanController.text.isNotEmpty ? _paypalPremiumPlanController.text : existing['premiumPlanId']!,
      isTestMode: true,
    );

    setState(() => _isLoading = false);

    if (success) {
      _showSuccess('PayPal updated successfully');
      _clearControllers();
      _loadConfigurationStatus();
    } else {
      _showError('Failed to update PayPal');
    }
  }

  Future<void> _validateConfiguration(PaymentProvider provider) async {
    setState(() => _isLoading = true);

    final success = await PaymentConfigService.validateProviderConfiguration(provider);

    setState(() => _isLoading = false);

    if (success) {
      _showSuccess('${provider.name} configuration is valid!');
    } else {
      _showError('${provider.name} configuration validation failed. Please check your credentials.');
    }
  }

  void _clearControllers() {
    _lemonApiKeyController.clear();
    _lemonStoreIdController.clear();
    _lemonWebhookSecretController.clear();
    _lemonBasicPlanController.clear();
    _lemonPremiumPlanController.clear();

    _stripeSecretKeyController.clear();
    _stripeWebhookSecretController.clear();
    _stripeBasicPriceController.clear();
    _stripePremiumPriceController.clear();

    _paypalClientIdController.clear();
    _paypalClientSecretController.clear();
    _paypalWebhookIdController.clear();
    _paypalBasicPlanController.clear();
    _paypalPremiumPlanController.clear();
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _startEditing(String provider) {
    setState(() {
      _editingProvider = provider;
      // Store original values for cancel functionality
      switch (provider) {
        case 'lemonsqueezy':
          _originalValues['apiKey'] = TextEditingController(text: _lemonApiKeyController.text);
          _originalValues['storeId'] = TextEditingController(text: _lemonStoreIdController.text);
          _originalValues['webhookSecret'] = TextEditingController(text: _lemonWebhookSecretController.text);
          _originalValues['basicPlan'] = TextEditingController(text: _lemonBasicPlanController.text);
          _originalValues['premiumPlan'] = TextEditingController(text: _lemonPremiumPlanController.text);
          break;
        case 'stripe':
          _originalValues['secretKey'] = TextEditingController(text: _stripeSecretKeyController.text);
          _originalValues['webhookSecret'] = TextEditingController(text: _stripeWebhookSecretController.text);
          _originalValues['basicPrice'] = TextEditingController(text: _stripeBasicPriceController.text);
          _originalValues['premiumPrice'] = TextEditingController(text: _stripePremiumPriceController.text);
          break;
        case 'paypal':
          _originalValues['clientId'] = TextEditingController(text: _paypalClientIdController.text);
          _originalValues['clientSecret'] = TextEditingController(text: _paypalClientSecretController.text);
          _originalValues['webhookId'] = TextEditingController(text: _paypalWebhookIdController.text);
          _originalValues['basicPlan'] = TextEditingController(text: _paypalBasicPlanController.text);
          _originalValues['premiumPlan'] = TextEditingController(text: _paypalPremiumPlanController.text);
          break;
      }
    });
  }

  void _cancelEditing() {
    setState(() {
      // Restore original values
      switch (_editingProvider) {
        case 'lemonsqueezy':
          _lemonApiKeyController.text = _originalValues['apiKey']?.text ?? '';
          _lemonStoreIdController.text = _originalValues['storeId']?.text ?? '';
          _lemonWebhookSecretController.text = _originalValues['webhookSecret']?.text ?? '';
          _lemonBasicPlanController.text = _originalValues['basicPlan']?.text ?? '';
          _lemonPremiumPlanController.text = _originalValues['premiumPlan']?.text ?? '';
          break;
        case 'stripe':
          _stripeSecretKeyController.text = _originalValues['secretKey']?.text ?? '';
          _stripeWebhookSecretController.text = _originalValues['webhookSecret']?.text ?? '';
          _stripeBasicPriceController.text = _originalValues['basicPrice']?.text ?? '';
          _stripePremiumPriceController.text = _originalValues['premiumPrice']?.text ?? '';
          break;
        case 'paypal':
          _paypalClientIdController.text = _originalValues['clientId']?.text ?? '';
          _paypalClientSecretController.text = _originalValues['clientSecret']?.text ?? '';
          _paypalWebhookIdController.text = _originalValues['webhookId']?.text ?? '';
          _paypalBasicPlanController.text = _originalValues['basicPlan']?.text ?? '';
          _paypalPremiumPlanController.text = _originalValues['premiumPlan']?.text ?? '';
          break;
      }
      _editingProvider = null;
      _originalValues.clear();
    });
  }

  void _saveEditing(String provider) async {
    switch (provider) {
      case 'lemonsqueezy':
        await _configureLemonSqueezy();
        break;
      case 'stripe':
        await _configureStripe();
        break;
      case 'paypal':
        await _configurePayPal();
        break;
    }
    setState(() {
      _editingProvider = null;
      _originalValues.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Configuration'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                   
                    const SizedBox(height: 24),
                    _buildLemonSqueezySection(),
                    const SizedBox(height: 24),
                    _buildStripeSection(),
                    const SizedBox(height: 24),
                    _buildPayPalSection(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Configuration Status',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildProviderStatus('LemonSqueezy', _configStatus['lemonsqueezy']),
            _buildProviderStatus('Stripe', _configStatus['stripe']),
            _buildProviderStatus('PayPal', _configStatus['paypal']),
            const SizedBox(height: 24),
            _buildWebhookInfo(),
          ],
        ),
      ),
    );
  }


  Widget _buildProviderStatus(String name, Map<String, dynamic>? status) {
    final isConfigured = status?['configured'] ?? false;
    final isTestMode = status?['testMode'] ?? false;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isConfigured ? Icons.check_circle : Icons.cancel,
            color: isConfigured ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(name),
          const Spacer(),
          if (isConfigured)
            Chip(
              label: Text(isTestMode ? 'Test' : 'Live'),
              backgroundColor: isTestMode ? Colors.orange : Colors.green,
              labelStyle: const TextStyle(color: Colors.white, fontSize: 12),
            ),
        ],
      ),
    );
  }

  Widget _buildLemonSqueezySection() {
    final existing = _existingCredentials[PaymentProvider.lemonsqueezy];
    return _buildProviderSection(
      title: 'LemonSqueezy Configuration',
      provider: PaymentProvider.lemonsqueezy,
      existingCredentials: existing,
      fields: [
        _buildTextField('API Key', _lemonApiKeyController, isPassword: true, existingValue: existing?['apiKey']),
        _buildTextField('Store ID', _lemonStoreIdController, existingValue: existing?['storeId']),
        _buildTextField('Webhook Secret', _lemonWebhookSecretController, isPassword: true, existingValue: existing?['webhookSecret']),
        _buildTextField('Basic Plan ID', _lemonBasicPlanController, existingValue: existing?['basicPlanId']),
        _buildTextField('Premium Plan ID', _lemonPremiumPlanController, existingValue: existing?['premiumPlanId']),
      ],
      onSave: _configureLemonSqueezy,
      onUpdate: () => _updateLemonSqueezy(),
    );
  }

  Widget _buildStripeSection() {
    final existing = _existingCredentials[PaymentProvider.stripe];
    return _buildProviderSection(
      title: 'Stripe Configuration',
      provider: PaymentProvider.stripe,
      existingCredentials: existing,
      fields: [
        _buildTextField('Secret Key', _stripeSecretKeyController, isPassword: true, existingValue: existing?['secretKey']),
        _buildTextField('Webhook Secret', _stripeWebhookSecretController, isPassword: true, existingValue: existing?['webhookSecret']),
        _buildTextField('Basic Price ID', _stripeBasicPriceController, existingValue: existing?['basicPriceId']),
        _buildTextField('Premium Price ID', _stripePremiumPriceController, existingValue: existing?['premiumPriceId']),
      ],
      onSave: _configureStripe,
      onUpdate: () => _updateStripe(),
    );
  }

  Widget _buildPayPalSection() {
    final existing = _existingCredentials[PaymentProvider.paypal];
    return _buildProviderSection(
      title: 'PayPal Configuration',
      provider: PaymentProvider.paypal,
      existingCredentials: existing,
      fields: [
        _buildTextField('Client ID', _paypalClientIdController, existingValue: existing?['clientId']),
        _buildTextField('Client Secret', _paypalClientSecretController, isPassword: true, existingValue: existing?['clientSecret']),
        _buildTextField('Webhook ID', _paypalWebhookIdController, existingValue: existing?['webhookId']),
        _buildTextField('Basic Plan ID', _paypalBasicPlanController, existingValue: existing?['basicPlanId']),
        _buildTextField('Premium Plan ID', _paypalPremiumPlanController, existingValue: existing?['premiumPlanId']),
      ],
      onSave: _configurePayPal,
      onUpdate: () => _updatePayPal(),
    );
  }

  Widget _buildProviderSection({
    required String title,
    required PaymentProvider provider,
    required List<Widget> fields,
    required VoidCallback onSave,
    Map<String, String>? existingCredentials,
    VoidCallback? onUpdate,
  }) {
    final isConfigured = existingCredentials != null && existingCredentials.isNotEmpty;
    final providerName = provider.name;
    final isEditing = _editingProvider == providerName;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  title,
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                if (isConfigured) ...[
                  Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 20,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Configured',
                    style: TextStyle(
                      color: Colors.green,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: 16),

            if (isConfigured && !isEditing) ...[
              // Show masked credentials when configured and not editing
              ..._buildMaskedCredentials(provider, existingCredentials),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => _startEditing(providerName),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Update'),
                ),
              ),
            ] else ...[
              // Show input fields when not configured or editing
              ...fields,
              const SizedBox(height: 16),

              if (isEditing) ...[
                // Cancel and Save buttons when editing
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _cancelEditing,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Cancel'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => _saveEditing(providerName),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Save'),
                      ),
                    ),
                  ],
                ),
              ] else ...[
                // Save button when not configured
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: onSave,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Save Configuration'),
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  List<Widget> _buildMaskedCredentials(PaymentProvider provider, Map<String, String>? credentials) {
    if (credentials == null) return [];

    switch (provider) {
      case PaymentProvider.lemonsqueezy:
        return [
          _buildMaskedField('API Key', credentials['apiKey']),
          _buildMaskedField('Store ID', credentials['storeId']),
          _buildMaskedField('Webhook Secret', credentials['webhookSecret']),
          _buildMaskedField('Basic Plan ID', credentials['basicPlanId']),
          _buildMaskedField('Premium Plan ID', credentials['premiumPlanId']),
        ];
      case PaymentProvider.stripe:
        return [
          _buildMaskedField('Secret Key', credentials['secretKey']),
          _buildMaskedField('Webhook Secret', credentials['webhookSecret']),
          _buildMaskedField('Basic Price ID', credentials['basicPriceId']),
          _buildMaskedField('Premium Price ID', credentials['premiumPriceId']),
        ];
      case PaymentProvider.paypal:
        return [
          _buildMaskedField('Client ID', credentials['clientId']),
          _buildMaskedField('Client Secret', credentials['clientSecret']),
          _buildMaskedField('Webhook ID', credentials['webhookId']),
          _buildMaskedField('Basic Plan ID', credentials['basicPlanId']),
          _buildMaskedField('Premium Plan ID', credentials['premiumPlanId']),
        ];
    }
  }

  Widget _buildMaskedField(String label, String? value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: TextFormField(
        initialValue: value ?? '***',
        enabled: false,
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          filled: true,
          fillColor: Colors.grey[100],
        ),
      ),
    );
  }

  Widget _buildTextField(
    String label,
    TextEditingController controller, {
    bool isPassword = false,
    String? existingValue,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (existingValue != null && existingValue.isNotEmpty) ...[
            Text(
              'Current $label: $existingValue',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondary,
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 4),
          ],
          TextFormField(
            controller: controller,
            obscureText: isPassword,
            decoration: InputDecoration(
              labelText: existingValue != null && existingValue.isNotEmpty
                  ? 'New $label (leave empty to keep current)'
                  : label,
              border: const OutlineInputBorder(),
              helperText: existingValue != null && existingValue.isNotEmpty
                  ? 'Leave empty to keep current value'
                  : null,
            ),
            validator: (value) {
              if ((value == null || value.isEmpty) && (existingValue == null || existingValue.isEmpty)) {
                return 'Please enter $label';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildWebhookInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.webhook, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'Webhook Configuration',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.warning, color: Colors.orange.shade700, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Webhook Server Ready for Deployment',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Deploy the webhook_server/ folder to Railway, Heroku, or similar platform. '
                    'Complete deployment guide available in WEBHOOK_DEPLOYMENT_GUIDE.md',
                    style: TextStyle(fontSize: 13),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Webhook Setup Options:',
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildWebhookOption(
              '🚀 Railway Deployment (Recommended)',
              'Deploy webhook_server/ folder to Railway (free tier available)',
              'https://your-app-name.railway.app/lemonsqueezy-webhook',
              'cd webhook_server && railway login && railway init && railway up',
            ),
            const SizedBox(height: 8),
            _buildWebhookOption(
              '🔧 Heroku Deployment',
              'Deploy webhook_server/ folder to Heroku',
              'https://your-webhook-app.herokuapp.com/stripe-webhook',
              'heroku create your-app && git push heroku main',
            ),
            const SizedBox(height: 8),
            _buildWebhookOption(
              '📋 Complete Setup Guide',
              'Follow WEBHOOK_DEPLOYMENT_GUIDE.md for step-by-step instructions',
              'https://your-deployed-url.com/paypal-webhook',
              'Includes environment variables, testing, and provider configuration',
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info, color: Colors.blue.shade700, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Webhook Events to Enable',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '• Subscription created/updated/cancelled\n'
                    '• Payment succeeded/failed\n'
                    '• Invoice payment events\n'
                    '• Customer subscription events',
                    style: TextStyle(fontSize: 13),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWebhookOption(String title, String description, String exampleUrl, String instructions) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.copy, size: 18),
                onPressed: () => _copyToClipboard(exampleUrl),
                tooltip: 'Copy Example URL',
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 6),
          Text(
            'Example: $exampleUrl',
            style: const TextStyle(
              fontFamily: 'monospace',
              fontSize: 11,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            instructions,
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Webhook URL copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
