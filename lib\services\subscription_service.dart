import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:http/http.dart' as http;
import '../models/user_subscription_model.dart';
import '../models/subscription_model.dart';
import '../models/credit_model.dart';
import '../utils/subscription_constants.dart';
import 'secure_storage_service.dart';
import 'notification_service.dart';

// Purpose: Manage user subscriptions, credits, and payment processing
class SubscriptionService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final NotificationService _notificationService = NotificationService();

  Function()? _onCreditsUpdated;

  // Purpose: Set callback function for credit update notifications
  void setCreditsUpdatedCallback(Function()? callback) {
    _onCreditsUpdated = callback;
  }

  static const String _baseUrl = 'https://api.lemonsqueezy.com/v1';
  static String? _storeId;
  static String? _apiKey;

  // Purpose: Initialize subscription service with client API credentials
  static Future<void> initializeForClient({
    required String lemonsqueezyStoreId,
    required String lemonsqueezyApiKey,
    required String openaiApiKey,
    required String elevenLabsApiKey,
  }) async {
    _storeId = lemonsqueezyStoreId;
    _apiKey = lemonsqueezyApiKey;

    await SecureStorageService.storeEncrypted('lemonsqueezy_store_id', lemonsqueezyStoreId);
    await SecureStorageService.storeEncrypted('lemonsqueezy_api_key', lemonsqueezyApiKey);
    await SecureStorageService.storeEncrypted('openai_api_key', openaiApiKey);
    await SecureStorageService.storeEncrypted('elevenlabs_api_key', elevenLabsApiKey);

    await _initializeSubscriptionPlans();
  }

  // Purpose: Load stored LemonSqueezy credentials from secure storage
  static Future<void> loadCredentials() async {
    _storeId = await SecureStorageService.getDecrypted('lemonsqueezy_store_id');
    _apiKey = await SecureStorageService.getDecrypted('lemonsqueezy_api_key');
  }

  // Purpose: Initialize subscription plans from Firestore configuration
  static Future<void> _initializeSubscriptionPlans() async {
    final firestore = FirebaseFirestore.instance;

    for (final plan in SubscriptionPlan.values) {
      final config = SubscriptionConstants.getPlanConfig(plan);
      await firestore.collection('subscription_plans').doc(plan.name).set({
        'id': plan.name,
        'name': config['name'],
        'price': config['price'],
        'chatCredits': config['chatCredits'],
        'voiceCredits': config['voiceCredits'],
        'features': config['features'],
        'duration': SubscriptionConstants.getPlanDuration(plan),
        'active': true,
        'createdAt': FieldValue.serverTimestamp(),
      });
    }
  }

  // Purpose: Get current user's subscription data with credit information
  Future<UserSubscriptionModel?> getCurrentUserSubscription() async {
    final user = _auth.currentUser;
    if (user == null) {
      print('SubscriptionService: No current user found');
      return null;
    }

    try {
      print('SubscriptionService: Getting subscription for user ${user.uid}');

      final doc = await _firestore.collection('users').doc(user.uid).get();

      if (!doc.exists) {
        print('SubscriptionService: User document does not exist, creating new user subscription');
        return await _createNewUserSubscription(user);
      }

      final data = doc.data()!;
      print('SubscriptionService: User document exists, checking subscription data');

      if (!data.containsKey('subscription') || !data.containsKey('credits') || data.containsKey('plan')) {
        print('SubscriptionService: User missing subscription data or has legacy structure, migrating user');
        await migrateExistingUser();

        final updatedDoc = await _firestore.collection('users').doc(user.uid).get();
        if (updatedDoc.exists) {
          final userData = UserSubscriptionModel.fromMap(updatedDoc.data()!, user.uid);
          print('SubscriptionService: User migrated successfully');
          return userData;
        }
      }

      final userData = UserSubscriptionModel.fromMap(data, user.uid);
      print('SubscriptionService: Subscription data loaded - Plan: ${userData.subscription.plan}, Chat: ${userData.credits.chatRemaining}, Voice: ${userData.credits.voiceRemaining}');

      if (userData.subscription.paymentPlatform == PaymentPlatform.lemonsqueezy) {
        await _syncWithLemonSqueezy(userData);
      }

      return userData;
    } catch (e) {
      print('Error getting user subscription: $e');
      try {
        print('SubscriptionService: Attempting to create new subscription due to error');
        return await _createNewUserSubscription(user);
      } catch (createError) {
        print('SubscriptionService: Failed to create new subscription: $createError');
        return null;
      }
    }
  }

  // Purpose: Create new user subscription with default free plan and credits
  Future<UserSubscriptionModel> _createNewUserSubscription(User user) async {
    print('SubscriptionService: Creating new user subscription for ${user.uid}');

    final userSubscription = UserSubscriptionModel.newUser(
      id: user.uid,
      email: user.email ?? '',
      name: user.displayName ?? 'User',
    );

    print('SubscriptionService: New user subscription created - Plan: ${userSubscription.subscription.plan}, Chat: ${userSubscription.credits.chatRemaining}, Voice: ${userSubscription.credits.voiceRemaining}');

    final userData = userSubscription.toMap();

    await _firestore.collection('users').doc(user.uid).set(userData, SetOptions(merge: true));

    print('SubscriptionService: User subscription saved to Firestore with correct credit structure');

    await _notificationService.sendWelcomeNotification(user.uid);

    return userSubscription;
  }

  Stream<UserSubscriptionModel?> subscriptionStream() {
    final user = _auth.currentUser;
    if (user == null) return Stream.value(null);

    return _firestore
        .collection('users')
        .doc(user.uid)
        .snapshots()
        .map((doc) {
      if (!doc.exists) return null;
      return UserSubscriptionModel.fromMap(doc.data()!, user.uid);
    });
  }

  // Purpose: Check if user has sufficient chat credits
  Future<bool> canUseChat() async {
    final subscription = await getCurrentUserSubscription();
    return subscription?.canUseChat ?? false;
  }

  // Purpose: Check if user has sufficient voice credits
  Future<bool> canUseVoice() async {
    final subscription = await getCurrentUserSubscription();
    return subscription?.canUseVoice ?? false;
  }

  // Purpose: Get remaining credits for all feature types
  Future<Map<String, int>> getRemainingCredits() async {
    final subscription = await getCurrentUserSubscription();
    if (subscription == null) {
      return {'chat': 0, 'voice': 0};
    }

    return {
      'chat': subscription.credits.chatRemaining,
      'voice': subscription.credits.voiceRemaining,
    };
  }

  // Purpose: Check if user needs to upgrade their subscription plan
  Future<bool> needsUpgrade() async {
    final subscription = await getCurrentUserSubscription();
    return subscription?.needsUpgrade ?? true;
  }

  // Purpose: Get comprehensive subscription status including credits and plan details
  Future<Map<String, dynamic>> getSubscriptionStatus() async {
    final subscription = await getCurrentUserSubscription();
    if (subscription == null) {
      return {
        'plan': 'free',
        'status': 'expired',
        'daysRemaining': 0,
        'chatCredits': 0,
        'voiceCredits': 0,
        'needsUpgrade': true,
      };
    }

    return {
      'plan': subscription.subscription.plan.name,
      'status': subscription.subscription.status.name,
      'daysRemaining': subscription.daysRemaining,
      'chatCredits': subscription.credits.chatRemaining,
      'voiceCredits': subscription.credits.voiceRemaining,
      'needsUpgrade': subscription.needsUpgrade,
      'planPrice': subscription.planPrice,
      'planName': subscription.planName,
      'isActive': subscription.isSubscriptionActive,
    };
  }

  List<Map<String, dynamic>> getAvailablePlans() {
    return [
      {
        'id': 'free',
        'name': 'Free Plan',
        'price': 0.0,
        'duration': '7 days',
        'chatCredits': 25,
        'voiceCredits': 5,
        'features': [
          '25 chat messages',
          '5 voice messages',
          'Basic support',
        ],
        'popular': false,
        'available': true,
      },
      {
        'id': 'basic',
        'name': 'Basic Plan',
        'price': 15.0,
        'duration': '30 days',
        'chatCredits': 13000,
        'voiceCredits': 80,
        'features': [
          '13,000 chat messages',
          '80 voice messages',
          'Chat history',
          'Email support',
        ],
        'popular': true,
        'available': true,
      },
      {
        'id': 'premium',
        'name': 'Premium Plan',
        'price': 30.0,
        'duration': '30 days',
        'chatCredits': 20000,
        'voiceCredits': 300,
        'features': [
          '20,000 chat messages',
          '300 voice messages',
          'Priority support',
          'Advanced features',
          'Export conversations',
        ],
        'popular': false,
        'available': true,
      },
    ];
  }

  // Purpose: Migrate existing user data to new subscription structure
  Future<void> migrateExistingUser() async {
    final user = _auth.currentUser;
    if (user == null) {
      print('SubscriptionService: No current user for migration');
      return;
    }

    try {
      print('SubscriptionService: Migrating existing user ${user.uid}');

      final doc = await _firestore.collection('users').doc(user.uid).get();

      if (doc.exists) {
        final data = doc.data()!;

        if (data.containsKey('subscription') && data.containsKey('credits') && !data.containsKey('plan')) {
          print('SubscriptionService: User ${user.uid} already has subscription data');
          return;
        }

        print('SubscriptionService: Creating subscription data for existing user');

        String userPlan = 'free';
        if (data.containsKey('plan')) {
          userPlan = data['plan'] ?? 'free';
          print('SubscriptionService: Found legacy plan: $userPlan');
        }

        DateTime userCreatedAt = DateTime.now();
        if (data.containsKey('createdAt')) {
          try {
            if (data['createdAt'] is int) {
              userCreatedAt = DateTime.fromMillisecondsSinceEpoch(data['createdAt']);
            } else if (data['createdAt'] is Timestamp) {
              userCreatedAt = (data['createdAt'] as Timestamp).toDate();
            }
          } catch (e) {
            print('SubscriptionService: Error parsing user createdAt: $e');
          }
        }

        final endDate = userPlan == 'free'
            ? userCreatedAt.add(const Duration(days: 7))
            : DateTime.now().add(const Duration(days: 30));

        final subscription = SubscriptionModel(
          plan: SubscriptionPlan.values.firstWhere(
            (e) => e.name == userPlan,
            orElse: () => SubscriptionPlan.free,
          ),
          status: SubscriptionStatus.active,
          startDate: userCreatedAt,
          endDate: endDate,
          autoRenew: userPlan != 'free',
          trialUsed: userPlan != 'free',
          trialEndsAt: userPlan == 'free' ? endDate : null,
        );

        final credits = CreditModel.fromPlan(subscription.plan);
        final usage = UsageStats.empty();
        final payment = PaymentInfo.empty();

        final updateData = {
          'subscription': subscription.toMap(),
          'credits': credits.toMap(),
          'usage': usage.toMap(),
          'payment': payment.toMap(),
          'migratedAt': FieldValue.serverTimestamp(),
        };

        if (data.containsKey('plan')) {
          updateData['plan'] = FieldValue.delete();
        }

        await _firestore.collection('users').doc(user.uid).update(updateData);

        print('SubscriptionService: User ${user.uid} migrated to subscription system with plan ${subscription.plan.name} - ${credits.chatRemaining} chat and ${credits.voiceRemaining} voice credits');
      } else {
        print('SubscriptionService: User document does not exist, creating new subscription');
        await _createNewUserSubscription(user);
      }
    } catch (e) {
      print('SubscriptionService: Error migrating user: $e');
      try {
        await _createNewUserSubscription(user);
      } catch (createError) {
        print('SubscriptionService: Failed to create subscription after migration error: $createError');
      }
    }
  }

  Future<void> updateLastActive() async {
    final user = _auth.currentUser;
    if (user == null) return;

    try {
      await _firestore.collection('users').doc(user.uid).update({
        'lastActiveAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('Error updating last active: $e');
    }
  }

  Future<void> logUsage(String type, bool success, {String? error}) async {
    final user = _auth.currentUser;
    if (user == null) return;

    try {
      await _firestore.collection('usage_logs').add({
        'userId': user.uid,
        'type': type,
        'timestamp': FieldValue.serverTimestamp(),
        'success': success,
        'error': error,
      });
    } catch (e) {
      print('Error logging usage: $e');
    }
  }

  Future<bool> isSubscriptionExpired() async {
    final subscription = await getCurrentUserSubscription();
    return subscription?.hasExpiredSubscription ?? true;
  }

  Future<void> _syncWithLemonSqueezy(UserSubscriptionModel userData) async {
    if (_apiKey == null || _storeId == null) {
      await loadCredentials();
    }

    if (_apiKey == null || userData.subscription.subscriptionId == null) {
      return;
    }

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/subscriptions/${userData.subscription.subscriptionId}'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Accept': 'application/vnd.api+json',
          'Content-Type': 'application/vnd.api+json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final subscription = data['data'];

        final isActive = subscription['attributes']['status'] == 'active';
        final expiresAt = DateTime.parse(subscription['attributes']['renews_at'] ??
                                       subscription['attributes']['ends_at']);

        if (!isActive || expiresAt.isBefore(DateTime.now())) {
          await _downgradeToFree(userData.user.id);
        }
      }
    } catch (e) {
      print('Error syncing with LemonSqueezy: $e');
    }
  }

  Future<void> _downgradeToFree(String userId) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'subscription.plan': SubscriptionPlan.free.name,
        'subscription.status': SubscriptionStatus.expired.name,
        'subscription.expiresAt': FieldValue.serverTimestamp(),
        'credits.chatRemaining': SubscriptionConstants.getPlanConfig(SubscriptionPlan.free)['chatCredits'],
        'credits.voiceRemaining': SubscriptionConstants.getPlanConfig(SubscriptionPlan.free)['voiceCredits'],
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('Error downgrading user to free: $e');
    }
  }

  Future<String?> getLemonSqueezyCheckoutUrl(SubscriptionPlan plan) async {
    if (_apiKey == null || _storeId == null) {
      await loadCredentials();
    }

    if (_apiKey == null) return null;

    try {
      final user = _auth.currentUser;
      if (user == null) return null;

      final planConfig = SubscriptionConstants.getPlanConfig(plan);
      final variantId = planConfig['lemonsqueezy_variant_id'];

      if (variantId == null) return null;

      final response = await http.post(
        Uri.parse('$_baseUrl/checkouts'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Accept': 'application/vnd.api+json',
          'Content-Type': 'application/vnd.api+json',
        },
        body: json.encode({
          'data': {
            'type': 'checkouts',
            'attributes': {
              'checkout_data': {
                'email': user.email,
                'name': user.displayName ?? 'User',
                'custom': {
                  'user_id': user.uid,
                  'plan': plan.name,
                }
              }
            },
            'relationships': {
              'store': {
                'data': {
                  'type': 'stores',
                  'id': _storeId,
                }
              },
              'variant': {
                'data': {
                  'type': 'variants',
                  'id': variantId,
                }
              }
            }
          }
        }),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return data['data']['attributes']['url'];
      }
    } catch (e) {
      print('Error creating LemonSqueezy checkout: $e');
    }

    return null;
  }

  Future<void> logEvent(String eventName, Map<String, dynamic> parameters) async {
    try {
      await _firestore.collection('analytics_events').add({
        'event': eventName,
        'parameters': parameters,
        'timestamp': FieldValue.serverTimestamp(),
        'userId': _auth.currentUser?.uid,
      });
    } catch (e) {
      print('Error logging event: $e');
    }
  }

  Future<bool> validateAndDeductCredit(String creditType) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        print('SubscriptionService: No current user for credit validation');
        return false;
      }

      final subscription = await getCurrentUserSubscription();
      if (subscription == null) {
        print('SubscriptionService: No subscription found for credit validation');
        return false;
      }

      bool canAccess = false;
      int currentUsed = 0;
      int currentLimit = 0;

      if (creditType == 'chat') {
        canAccess = subscription.canUseChat;
        currentUsed = subscription.credits.chatUsed;
        currentLimit = subscription.credits.chatLimit;
        print('SubscriptionService: Chat access check - Can access: $canAccess, Remaining: ${subscription.credits.chatRemaining}, Used: $currentUsed, Limit: $currentLimit, Trial expired: ${subscription.subscription.isExpired}');
      } else if (creditType == 'voice') {
        canAccess = subscription.canUseVoice;
        currentUsed = subscription.credits.voiceUsed;
        currentLimit = subscription.credits.voiceLimit;
        print('SubscriptionService: Voice access check - Can access: $canAccess, Remaining: ${subscription.credits.voiceRemaining}, Used: $currentUsed, Limit: $currentLimit, Trial expired: ${subscription.subscription.isExpired}');
      }

      if (!canAccess) {
        if (subscription.subscription.plan == SubscriptionPlan.free && subscription.subscription.isExpired) {
          print('SubscriptionService: Access denied - Free trial has expired');
        } else {
          print('SubscriptionService: Access denied - No $creditType credits available or subscription inactive');
        }
        return false;
      }

      final newUsed = currentUsed + 1;
      final newRemaining = (currentLimit - newUsed).clamp(0, currentLimit);

      final updateData = <String, dynamic>{};
      if (creditType == 'chat') {
        updateData['credits.chat.used'] = newUsed;
        updateData['credits.chat.remaining'] = newRemaining;
        print('SubscriptionService: Updating chat credits - New Used: $newUsed, New Remaining: $newRemaining');
      } else if (creditType == 'voice') {
        updateData['credits.voice.used'] = newUsed;
        updateData['credits.voice.remaining'] = newRemaining;
        print('SubscriptionService: Updating voice credits - New Used: $newUsed, New Remaining: $newRemaining');
      }

      updateData['updatedAt'] = FieldValue.serverTimestamp();

      await _firestore.collection('users').doc(user.uid).update(updateData);
      print('SubscriptionService: Credit deduction completed successfully');

      await logEvent('credit_used', {
        'creditType': creditType,
        'userId': user.uid,
        'newUsed': newUsed,
        'newRemaining': newRemaining,
        'timestamp': DateTime.now().toIso8601String(),
      });

      final usagePercentage = (newUsed / currentLimit * 100);
      if (usagePercentage >= 80 && newRemaining > 0) {
        await _notificationService.sendCreditWarningNotification(
          user.uid,
          creditType,
          newRemaining,
          currentLimit,
        );
      }

      print('SubscriptionService: Notifying UI of credit changes');
      _onCreditsUpdated?.call();

      return true;
    } catch (e) {
      print('SubscriptionService: Error validating/deducting credit: $e');
      return false;
    }
  }

  // Purpose: Generate LemonSqueezy checkout URL for plan upgrades
  String getLemonSqueezyUpgradeUrl(String planId) {
    return 'https://your-store.lemonsqueezy.com/checkout/buy/$planId';
  }
}
