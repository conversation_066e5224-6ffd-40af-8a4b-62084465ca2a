{"database": {"rules": "database.rules.json"}, "firestore": {"rules": "firestore_security_rules.txt", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}]}