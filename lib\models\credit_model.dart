import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'subscription_model.dart';

class CreditModel extends Equatable {
  final int chatLimit;
  final int chatUsed;
  final int voiceLimit;
  final int voiceUsed;
  final DateTime resetDate;

  const CreditModel({
    required this.chatLimit,
    required this.chatUsed,
    required this.voiceLimit,
    required this.voiceUsed,
    required this.resetDate,
  });

  factory CreditModel.free() {
    final now = DateTime.now();
    return CreditModel(
      chatLimit: 25,
      chatUsed: 0,
      voiceLimit: 5,
      voiceUsed: 0,
      resetDate: now.add(const Duration(days: 7)),
    );
  }

  factory CreditModel.basic() {
    final now = DateTime.now();
    return CreditModel(
      chatLimit: 13000,
      chatUsed: 0,
      voiceLimit: 80,
      voiceUsed: 0,
      resetDate: now.add(const Duration(days: 30)),
    );
  }

  factory CreditModel.premium() {
    final now = DateTime.now();
    return CreditModel(
      chatLimit: 20000,
      chatUsed: 0,
      voiceLimit: 300,
      voiceUsed: 0,
      resetDate: now.add(const Duration(days: 30)),
    );
  }

  factory CreditModel.fromPlan(SubscriptionPlan plan) {
    switch (plan) {
      case SubscriptionPlan.free:
        return CreditModel.free();
      case SubscriptionPlan.basic:
        return CreditModel.basic();
      case SubscriptionPlan.premium:
        return CreditModel.premium();
    }
  }

  factory CreditModel.fromMap(Map<String, dynamic> data) {
    try {
      int chatLimit, chatUsed, voiceLimit, voiceUsed;
      DateTime resetDate;

      if (data.containsKey('chat') && data['chat'] is Map) {
        chatLimit = data['chat']['limit'] ?? 25;
        chatUsed = data['chat']['used'] ?? 0;
        voiceLimit = data['voice']['limit'] ?? 5;
        voiceUsed = data['voice']['used'] ?? 0;
      } else {
        chatLimit = 25;
        chatUsed = 0;
        voiceLimit = 5;
        voiceUsed = 0;
      }

      try {
        if (data.containsKey('resetDate') && data['resetDate'] != null) {
          final resetDateValue = data['resetDate'];
          if (resetDateValue is Timestamp) {
            resetDate = resetDateValue.toDate();
          } else if (resetDateValue is int) {
            resetDate = DateTime.fromMillisecondsSinceEpoch(resetDateValue);
          } else if (resetDateValue is String) {
            resetDate = DateTime.parse(resetDateValue);
          } else {
            resetDate = DateTime.now().add(const Duration(days: 7));
          }
        } else {
          resetDate = DateTime.now().add(const Duration(days: 7));
        }
      } catch (e) {
        print('Error parsing resetDate: $e, using default');
        resetDate = DateTime.now().add(const Duration(days: 7));
      }

      return CreditModel(
        chatLimit: chatLimit,
        chatUsed: chatUsed,
        voiceLimit: voiceLimit,
        voiceUsed: voiceUsed,
        resetDate: resetDate,
      );
    } catch (e) {
      print('Error parsing credit data: $e, using free plan defaults');
      return CreditModel.free();
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'chat': {
        'limit': chatLimit,
        'used': chatUsed,
        'remaining': chatRemaining,
      },
      'voice': {
        'limit': voiceLimit,
        'used': voiceUsed,
        'remaining': voiceRemaining,
      },
      'resetDate': Timestamp.fromDate(resetDate),
    };
  }

  int get chatRemaining => (chatLimit - chatUsed).clamp(0, chatLimit);
  int get voiceRemaining => (voiceLimit - voiceUsed).clamp(0, voiceLimit);

  bool get hasChatCredits => chatRemaining > 0;

  bool get hasVoiceCredits => voiceRemaining > 0;

  bool get hasExpired => DateTime.now().isAfter(resetDate);

  double get chatUsagePercentage {
    if (chatLimit == 0) return 0.0;
    return (chatUsed / chatLimit).clamp(0.0, 1.0);
  }

  double get voiceUsagePercentage {
    if (voiceLimit == 0) return 0.0;
    return (voiceUsed / voiceLimit).clamp(0.0, 1.0);
  }

  bool get isNearChatLimit => chatUsagePercentage >= 0.8;
  bool get isNearVoiceLimit => voiceUsagePercentage >= 0.8;

  int get daysUntilReset {
    if (hasExpired) return 0;
    return resetDate.difference(DateTime.now()).inDays;
  }

  CreditModel useChatCredit({int count = 1}) {
    return copyWith(
      chatUsed: (chatUsed + count).clamp(0, chatLimit),
    );
  }

  CreditModel useVoiceCredit({int count = 1}) {
    return copyWith(
      voiceUsed: (voiceUsed + count).clamp(0, voiceLimit),
    );
  }

  CreditModel resetCredits(SubscriptionPlan plan) {
    return CreditModel.fromPlan(plan);
  }

  CreditModel copyWith({
    int? chatLimit,
    int? chatUsed,
    int? voiceLimit,
    int? voiceUsed,
    DateTime? resetDate,
  }) {
    return CreditModel(
      chatLimit: chatLimit ?? this.chatLimit,
      chatUsed: chatUsed ?? this.chatUsed,
      voiceLimit: voiceLimit ?? this.voiceLimit,
      voiceUsed: voiceUsed ?? this.voiceUsed,
      resetDate: resetDate ?? this.resetDate,
    );
  }

  @override
  List<Object?> get props => [
        chatLimit,
        chatUsed,
        voiceLimit,
        voiceUsed,
        resetDate,
      ];

  @override
  String toString() {
    return 'CreditModel(chatLimit: $chatLimit, chatUsed: $chatUsed, voiceLimit: $voiceLimit, voiceUsed: $voiceUsed, resetDate: $resetDate)';
  }
}
