# Use official Dart runtime as base image
FROM dart:stable AS build

# Set working directory
WORKDIR /app

# Copy pubspec files
COPY pubspec.* ./

# Install dependencies
RUN dart pub get

# Copy source code
COPY . .

# Compile the application
RUN dart compile exe webhook_receiver.dart -o webhook_receiver

# Use minimal runtime image
FROM scratch
COPY --from=build /runtime/ /
COPY --from=build /app/webhook_receiver /app/

# Expose port
EXPOSE 8080

# Run the application
ENTRYPOINT ["/app/webhook_receiver"]
