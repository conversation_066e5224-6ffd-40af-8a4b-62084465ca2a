import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'subscription_model.dart';

enum InvoiceStatus { pending, paid, failed, cancelled, refunded }

class InvoiceModel extends Equatable {
  final String id;
  final String invoiceNumber;
  final String userId;
  final String userEmail;
  final String userName;
  final SubscriptionPlan plan;
  final double amount;
  final double taxAmount;
  final double totalAmount;
  final String currency;
  final InvoiceStatus status;
  final String? checkoutUrl;
  final String? paymentId;
  final DateTime createdAt;
  final DateTime? paidAt;
  final DateTime? updatedAt;
  final DateTime dueDate;
  final DateTime billingPeriodStart;
  final DateTime billingPeriodEnd;
  final List<InvoiceItem> items;
  final Map<String, dynamic>? metadata;

  const InvoiceModel({
    required this.id,
    required this.invoiceNumber,
    required this.userId,
    required this.userEmail,
    required this.userName,
    required this.plan,
    required this.amount,
    this.taxAmount = 0.0,
    required this.currency,
    required this.status,
    this.checkoutUrl,
    this.paymentId,
    required this.createdAt,
    this.paidAt,
    this.updatedAt,
    required this.dueDate,
    required this.billingPeriodStart,
    required this.billingPeriodEnd,
    required this.items,
    this.metadata,
  }) : totalAmount = amount + taxAmount;

  factory InvoiceModel.fromMap(Map<String, dynamic> data, String id) {
    return InvoiceModel(
      id: id,
      invoiceNumber: data['invoiceNumber'] ?? '',
      userId: data['userId'] ?? '',
      userEmail: data['userEmail'] ?? '',
      userName: data['userName'] ?? '',
      plan: SubscriptionPlan.values.firstWhere(
        (e) => e.name == data['plan'],
        orElse: () => SubscriptionPlan.free,
      ),
      amount: (data['amount'] ?? 0.0).toDouble(),
      taxAmount: (data['taxAmount'] ?? 0.0).toDouble(),
      currency: data['currency'] ?? 'USD',
      status: InvoiceStatus.values.firstWhere(
        (e) => e.name == data['status'],
        orElse: () => InvoiceStatus.pending,
      ),
      checkoutUrl: data['checkoutUrl'],
      paymentId: data['paymentId'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      paidAt: data['paidAt'] != null 
          ? (data['paidAt'] as Timestamp).toDate() 
          : null,
      updatedAt: data['updatedAt'] != null 
          ? (data['updatedAt'] as Timestamp).toDate() 
          : null,
      dueDate: (data['dueDate'] as Timestamp).toDate(),
      billingPeriodStart: (data['billingPeriodStart'] as Timestamp).toDate(),
      billingPeriodEnd: (data['billingPeriodEnd'] as Timestamp).toDate(),
      items: (data['items'] as List<dynamic>?)
          ?.map((item) => InvoiceItem.fromMap(item))
          .toList() ?? [],
      metadata: data['metadata'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'invoiceNumber': invoiceNumber,
      'userId': userId,
      'userEmail': userEmail,
      'userName': userName,
      'plan': plan.name,
      'amount': amount,
      'taxAmount': taxAmount,
      'totalAmount': totalAmount,
      'currency': currency,
      'status': status.name,
      'checkoutUrl': checkoutUrl,
      'paymentId': paymentId,
      'createdAt': Timestamp.fromDate(createdAt),
      'paidAt': paidAt != null ? Timestamp.fromDate(paidAt!) : null,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'dueDate': Timestamp.fromDate(dueDate),
      'billingPeriodStart': Timestamp.fromDate(billingPeriodStart),
      'billingPeriodEnd': Timestamp.fromDate(billingPeriodEnd),
      'items': items.map((item) => item.toMap()).toList(),
      'metadata': metadata,
    };
  }

  bool get isPaid => status == InvoiceStatus.paid;
  bool get isPending => status == InvoiceStatus.pending;
  bool get isFailed => status == InvoiceStatus.failed;
  bool get isCancelled => status == InvoiceStatus.cancelled;
  bool get isRefunded => status == InvoiceStatus.refunded;
  
  bool get isOverdue {
    return isPending && DateTime.now().isAfter(dueDate);
  }

  int get daysUntilDue {
    if (!isPending) return 0;
    final difference = dueDate.difference(DateTime.now()).inDays;
    return difference > 0 ? difference : 0;
  }

  int get daysOverdue {
    if (!isOverdue) return 0;
    return DateTime.now().difference(dueDate).inDays;
  }

  String get statusDisplayName {
    switch (status) {
      case InvoiceStatus.pending:
        return 'Pending Payment';
      case InvoiceStatus.paid:
        return 'Paid';
      case InvoiceStatus.failed:
        return 'Payment Failed';
      case InvoiceStatus.cancelled:
        return 'Cancelled';
      case InvoiceStatus.refunded:
        return 'Refunded';
    }
  }

  String get planDisplayName {
    switch (plan) {
      case SubscriptionPlan.free:
        return 'Free Plan';
      case SubscriptionPlan.basic:
        return 'Basic Plan';
      case SubscriptionPlan.premium:
        return 'Premium Plan';
    }
  }

  InvoiceModel copyWith({
    String? id,
    String? invoiceNumber,
    String? userId,
    String? userEmail,
    String? userName,
    SubscriptionPlan? plan,
    double? amount,
    double? taxAmount,
    String? currency,
    InvoiceStatus? status,
    String? checkoutUrl,
    String? paymentId,
    DateTime? createdAt,
    DateTime? paidAt,
    DateTime? updatedAt,
    DateTime? dueDate,
    DateTime? billingPeriodStart,
    DateTime? billingPeriodEnd,
    List<InvoiceItem>? items,
    Map<String, dynamic>? metadata,
  }) {
    return InvoiceModel(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      userId: userId ?? this.userId,
      userEmail: userEmail ?? this.userEmail,
      userName: userName ?? this.userName,
      plan: plan ?? this.plan,
      amount: amount ?? this.amount,
      taxAmount: taxAmount ?? this.taxAmount,
      currency: currency ?? this.currency,
      status: status ?? this.status,
      checkoutUrl: checkoutUrl ?? this.checkoutUrl,
      paymentId: paymentId ?? this.paymentId,
      createdAt: createdAt ?? this.createdAt,
      paidAt: paidAt ?? this.paidAt,
      updatedAt: updatedAt ?? this.updatedAt,
      dueDate: dueDate ?? this.dueDate,
      billingPeriodStart: billingPeriodStart ?? this.billingPeriodStart,
      billingPeriodEnd: billingPeriodEnd ?? this.billingPeriodEnd,
      items: items ?? this.items,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        invoiceNumber,
        userId,
        userEmail,
        userName,
        plan,
        amount,
        taxAmount,
        currency,
        status,
        checkoutUrl,
        paymentId,
        createdAt,
        paidAt,
        updatedAt,
        dueDate,
        billingPeriodStart,
        billingPeriodEnd,
        items,
        metadata,
      ];

  @override
  String toString() {
    return 'InvoiceModel(id: $id, invoiceNumber: $invoiceNumber, plan: $plan, amount: $amount, status: $status)';
  }
}

class InvoiceItem extends Equatable {
  final String description;
  final int quantity;
  final double unitPrice;
  final double totalPrice;

  const InvoiceItem({
    required this.description,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
  });

  factory InvoiceItem.fromMap(Map<String, dynamic> data) {
    return InvoiceItem(
      description: data['description'] ?? '',
      quantity: data['quantity'] ?? 1,
      unitPrice: (data['unitPrice'] ?? 0.0).toDouble(),
      totalPrice: (data['totalPrice'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'description': description,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'totalPrice': totalPrice,
    };
  }

  @override
  List<Object?> get props => [description, quantity, unitPrice, totalPrice];

  @override
  String toString() {
    return 'InvoiceItem(description: $description, quantity: $quantity, unitPrice: $unitPrice, totalPrice: $totalPrice)';
  }
}
