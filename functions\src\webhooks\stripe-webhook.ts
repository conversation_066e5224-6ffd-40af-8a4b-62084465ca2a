import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import * as crypto from 'crypto';

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

// Stripe webhook secret (set in Firebase Functions config)
const WEBHOOK_SECRET = functions.config().stripe?.webhook_secret || process.env.STRIPE_WEBHOOK_SECRET;

/**
 * Verify Stripe webhook signature
 */
function verifyWebhookSignature(payload: string, signature: string): boolean {
  if (!WEBHOOK_SECRET) {
    console.error('Stripe webhook secret not configured');
    return false;
  }

  const elements = signature.split(',');
  const signatureHash = elements.find(element => element.startsWith('v1='))?.split('=')[1];
  
  if (!signatureHash) {
    return false;
  }

  const expectedSignature = crypto
    .createHmac('sha256', WEBHOOK_SECRET)
    .update(payload, 'utf8')
    .digest('hex');

  return crypto.timingSafeEqual(
    Buffer.from(signatureHash, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  );
}

/**
 * Map Stripe price ID to plan
 */
function mapPriceIdToPlan(priceId: string): string {
  const priceMapping: { [key: string]: string } = {
    // Add your Stripe price IDs here
    'price_basic_monthly': 'basic',
    'price_premium_monthly': 'premium',
    'price_basic_yearly': 'basic',
    'price_premium_yearly': 'premium',
  };
  
  return priceMapping[priceId] || 'free';
}

/**
 * Get credit limits for a plan
 */
function getCreditLimits(plan: string): { chatLimit: number; voiceLimit: number } {
  const limits: { [key: string]: { chatLimit: number; voiceLimit: number } } = {
    free: { chatLimit: 25, voiceLimit: 5 },
    basic: { chatLimit: 13000, voiceLimit: 80 },
    premium: { chatLimit: 20000, voiceLimit: 300 },
  };
  
  return limits[plan] || limits.free;
}

/**
 * Find user by Stripe customer ID
 */
async function findUserByCustomerId(customerId: string): Promise<string | null> {
  const query = await db
    .collection('users')
    .where('payment.stripe.customerId', '==', customerId)
    .limit(1)
    .get();
  
  return query.empty ? null : query.docs[0].id;
}

/**
 * Find user by Stripe subscription ID
 */
async function findUserBySubscriptionId(subscriptionId: string): Promise<string | null> {
  const query = await db
    .collection('users')
    .where('payment.stripe.subscriptionId', '==', subscriptionId)
    .limit(1)
    .get();
  
  return query.empty ? null : query.docs[0].id;
}

/**
 * Handle subscription created
 */
async function handleSubscriptionCreated(data: any): Promise<void> {
  const subscription = data.object;
  const customerId = subscription.customer;
  const subscriptionId = subscription.id;
  const priceId = subscription.items.data[0]?.price?.id;
  
  if (!priceId) {
    console.error('No price ID found in subscription');
    return;
  }

  const userId = await findUserByCustomerId(customerId);
  if (!userId) {
    console.error(`User not found for Stripe customer ${customerId}`);
    return;
  }

  const plan = mapPriceIdToPlan(priceId);
  const { chatLimit, voiceLimit } = getCreditLimits(plan);
  const now = new Date();
  const endDate = new Date(subscription.current_period_end * 1000);

  try {
    await db.collection('users').doc(userId).update({
      subscription: {
        plan: plan,
        status: 'active',
        startDate: admin.firestore.Timestamp.fromDate(now),
        endDate: admin.firestore.Timestamp.fromDate(endDate),
        autoRenew: subscription.cancel_at_period_end === false,
        trialUsed: subscription.trial_end ? true : false,
        paymentPlatform: 'stripe',
        subscriptionId: subscriptionId,
        customerId: customerId,
      },
      credits: {
        chat: {
          limit: chatLimit,
          used: 0,
          remaining: chatLimit,
        },
        voice: {
          limit: voiceLimit,
          used: 0,
          remaining: voiceLimit,
        },
        resetDate: admin.firestore.Timestamp.fromDate(endDate),
      },
      payment: {
        stripe: {
          customerId: customerId,
          subscriptionId: subscriptionId,
        },
      },
    });

    console.log(`Stripe subscription created for user ${userId}, plan: ${plan}`);
  } catch (error) {
    console.error('Error handling Stripe subscription created:', error);
    throw error;
  }
}

/**
 * Handle subscription updated
 */
async function handleSubscriptionUpdated(data: any): Promise<void> {
  const subscription = data.object;
  const subscriptionId = subscription.id;

  const userId = await findUserBySubscriptionId(subscriptionId);
  if (!userId) {
    console.error(`User not found for Stripe subscription ${subscriptionId}`);
    return;
  }

  const status = subscription.status === 'active' ? 'active' : 'cancelled';
  const endDate = new Date(subscription.current_period_end * 1000);

  try {
    await db.collection('users').doc(userId).update({
      'subscription.status': status,
      'subscription.endDate': admin.firestore.Timestamp.fromDate(endDate),
      'subscription.autoRenew': subscription.cancel_at_period_end === false,
    });

    console.log(`Stripe subscription updated for user ${userId}, status: ${status}`);
  } catch (error) {
    console.error('Error handling Stripe subscription updated:', error);
    throw error;
  }
}

/**
 * Handle subscription deleted/cancelled
 */
async function handleSubscriptionDeleted(data: any): Promise<void> {
  const subscription = data.object;
  const subscriptionId = subscription.id;

  const userId = await findUserBySubscriptionId(subscriptionId);
  if (!userId) {
    console.error(`User not found for Stripe subscription ${subscriptionId}`);
    return;
  }

  try {
    await db.collection('users').doc(userId).update({
      'subscription.status': 'cancelled',
      'subscription.autoRenew': false,
    });

    console.log(`Stripe subscription cancelled for user ${userId}`);
  } catch (error) {
    console.error('Error handling Stripe subscription cancelled:', error);
    throw error;
  }
}

/**
 * Handle invoice payment succeeded
 */
async function handleInvoicePaymentSucceeded(data: any): Promise<void> {
  const invoice = data.object;
  const subscriptionId = invoice.subscription;

  if (!subscriptionId) {
    console.log('Invoice payment succeeded but no subscription ID found');
    return;
  }

  const userId = await findUserBySubscriptionId(subscriptionId);
  if (!userId) {
    console.error(`User not found for Stripe subscription ${subscriptionId}`);
    return;
  }

  try {
    await db.collection('users').doc(userId).update({
      'subscription.status': 'active',
    });

    console.log(`Stripe payment succeeded for user ${userId}`);
  } catch (error) {
    console.error('Error handling Stripe payment succeeded:', error);
    throw error;
  }
}

/**
 * Handle invoice payment failed
 */
async function handleInvoicePaymentFailed(data: any): Promise<void> {
  const invoice = data.object;
  const subscriptionId = invoice.subscription;

  if (!subscriptionId) {
    console.log('Invoice payment failed but no subscription ID found');
    return;
  }

  const userId = await findUserBySubscriptionId(subscriptionId);
  if (!userId) {
    console.error(`User not found for Stripe subscription ${subscriptionId}`);
    return;
  }

  try {
    await db.collection('users').doc(userId).update({
      'subscription.status': 'past_due',
    });

    console.log(`Stripe payment failed for user ${userId}`);
  } catch (error) {
    console.error('Error handling Stripe payment failed:', error);
    throw error;
  }
}

/**
 * Main Stripe webhook handler
 */
export const stripeWebhook = functions.https.onRequest(async (req, res) => {
  if (req.method !== 'POST') {
    res.status(405).send('Method Not Allowed');
    return;
  }

  try {
    const signature = req.get('Stripe-Signature') || '';
    const payload = JSON.stringify(req.body);

    if (!verifyWebhookSignature(payload, signature)) {
      console.error('Invalid Stripe webhook signature');
      res.status(401).send('Unauthorized');
      return;
    }

    const event = req.body;
    const eventType = event.type;

    console.log(`Received Stripe webhook: ${eventType}`);

    switch (eventType) {
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data);
        break;
      
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data);
        break;
      
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data);
        break;
      
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data);
        break;
      
      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data);
        break;
      
      default:
        console.log(`Unhandled Stripe webhook event: ${eventType}`);
    }

    res.status(200).send('OK');
  } catch (error) {
    console.error('Stripe webhook error:', error);
    res.status(500).send('Internal Server Error');
  }
});
