import 'package:equatable/equatable.dart';

/// Comprehensive user memory model for storing personal context and preferences
class UserMemoryModel extends Equatable {
  final String userId;
  final PersonalInformation personalInfo;
  final AviationData aviationData;
  final BehavioralPatterns behavioralPatterns;
  final TherapyProgress therapyProgress;
  final DateTime createdAt;
  final DateTime lastUpdated;
  final Map<String, dynamic>? metadata;

  const UserMemoryModel({
    required this.userId,
    required this.personalInfo,
    required this.aviationData,
    required this.behavioralPatterns,
    required this.therapyProgress,
    required this.createdAt,
    required this.lastUpdated,
    this.metadata,
  });

  factory UserMemoryModel.empty(String userId) {
    final now = DateTime.now();
    return UserMemoryModel(
      userId: userId,
      personalInfo: PersonalInformation.empty(),
      aviationData: AviationData.empty(),
      behavioralPatterns: BehavioralPatterns.empty(),
      therapyProgress: TherapyProgress.empty(),
      createdAt: now,
      lastUpdated: now,
    );
  }

  factory UserMemoryModel.fromMap(Map<String, dynamic> map, String userId) {
    return UserMemoryModel(
      userId: userId,
      personalInfo: PersonalInformation.fromMap(map['personalInfo'] ?? {}),
      aviationData: AviationData.fromMap(map['aviationData'] ?? {}),
      behavioralPatterns: BehavioralPatterns.fromMap(map['behavioralPatterns'] ?? {}),
      therapyProgress: TherapyProgress.fromMap(map['therapyProgress'] ?? {}),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? DateTime.now().millisecondsSinceEpoch),
      lastUpdated: DateTime.fromMillisecondsSinceEpoch(map['lastUpdated'] ?? DateTime.now().millisecondsSinceEpoch),
      metadata: map['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'personalInfo': personalInfo.toMap(),
      'aviationData': aviationData.toMap(),
      'behavioralPatterns': behavioralPatterns.toMap(),
      'therapyProgress': therapyProgress.toMap(),
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastUpdated': lastUpdated.millisecondsSinceEpoch,
      'metadata': metadata,
    };
  }

  UserMemoryModel copyWith({
    String? userId,
    PersonalInformation? personalInfo,
    AviationData? aviationData,
    BehavioralPatterns? behavioralPatterns,
    TherapyProgress? therapyProgress,
    DateTime? createdAt,
    DateTime? lastUpdated,
    Map<String, dynamic>? metadata,
  }) {
    return UserMemoryModel(
      userId: userId ?? this.userId,
      personalInfo: personalInfo ?? this.personalInfo,
      aviationData: aviationData ?? this.aviationData,
      behavioralPatterns: behavioralPatterns ?? this.behavioralPatterns,
      therapyProgress: therapyProgress ?? this.therapyProgress,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? DateTime.now(),
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
    userId, personalInfo, aviationData, behavioralPatterns, 
    therapyProgress, createdAt, lastUpdated, metadata
  ];
}

/// Personal information and mental health context
class PersonalInformation extends Equatable {
  final List<String> phobias;
  final List<String> medicalConditions;
  final List<String> mentalHealthConcerns;
  final Map<String, String> triggers; // trigger -> description
  final List<String> previousTherapy;
  final String? preferredName;
  final int? age;
  final String? occupation;
  final Map<String, dynamic> personalityTraits;
  final List<String> copingMechanisms;

  const PersonalInformation({
    required this.phobias,
    required this.medicalConditions,
    required this.mentalHealthConcerns,
    required this.triggers,
    required this.previousTherapy,
    this.preferredName,
    this.age,
    this.occupation,
    required this.personalityTraits,
    required this.copingMechanisms,
  });

  factory PersonalInformation.empty() {
    return const PersonalInformation(
      phobias: [],
      medicalConditions: [],
      mentalHealthConcerns: [],
      triggers: {},
      previousTherapy: [],
      personalityTraits: {},
      copingMechanisms: [],
    );
  }

  factory PersonalInformation.fromMap(Map<String, dynamic> map) {
    return PersonalInformation(
      phobias: List<String>.from(map['phobias'] ?? []),
      medicalConditions: List<String>.from(map['medicalConditions'] ?? []),
      mentalHealthConcerns: List<String>.from(map['mentalHealthConcerns'] ?? []),
      triggers: Map<String, String>.from(map['triggers'] ?? {}),
      previousTherapy: List<String>.from(map['previousTherapy'] ?? []),
      preferredName: map['preferredName'] as String?,
      age: map['age'] as int?,
      occupation: map['occupation'] as String?,
      personalityTraits: Map<String, dynamic>.from(map['personalityTraits'] ?? {}),
      copingMechanisms: List<String>.from(map['copingMechanisms'] ?? []),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'phobias': phobias,
      'medicalConditions': medicalConditions,
      'mentalHealthConcerns': mentalHealthConcerns,
      'triggers': triggers,
      'previousTherapy': previousTherapy,
      'preferredName': preferredName,
      'age': age,
      'occupation': occupation,
      'personalityTraits': personalityTraits,
      'copingMechanisms': copingMechanisms,
    };
  }

  PersonalInformation copyWith({
    List<String>? phobias,
    List<String>? medicalConditions,
    List<String>? mentalHealthConcerns,
    Map<String, String>? triggers,
    List<String>? previousTherapy,
    String? preferredName,
    int? age,
    String? occupation,
    Map<String, dynamic>? personalityTraits,
    List<String>? copingMechanisms,
  }) {
    return PersonalInformation(
      phobias: phobias ?? this.phobias,
      medicalConditions: medicalConditions ?? this.medicalConditions,
      mentalHealthConcerns: mentalHealthConcerns ?? this.mentalHealthConcerns,
      triggers: triggers ?? this.triggers,
      previousTherapy: previousTherapy ?? this.previousTherapy,
      preferredName: preferredName ?? this.preferredName,
      age: age ?? this.age,
      occupation: occupation ?? this.occupation,
      personalityTraits: personalityTraits ?? this.personalityTraits,
      copingMechanisms: copingMechanisms ?? this.copingMechanisms,
    );
  }

  @override
  List<Object?> get props => [
    phobias, medicalConditions, mentalHealthConcerns, triggers, 
    previousTherapy, preferredName, age, occupation, personalityTraits, copingMechanisms
  ];
}

/// Aviation-specific data and flight-related information
class AviationData extends Equatable {
  final List<FlightExperience> flightHistory;
  final List<String> preferredAirlines;
  final List<String> preferredAircraftTypes;
  final Map<String, int> flightFears; // fear -> severity (1-10)
  final List<String> comfortableAirports;
  final List<String> uncomfortableAirports;
  final List<UpcomingFlight> upcomingFlights;
  final Map<String, String> aviationPreferences;
  final List<String> successfulCopingStrategies;

  const AviationData({
    required this.flightHistory,
    required this.preferredAirlines,
    required this.preferredAircraftTypes,
    required this.flightFears,
    required this.comfortableAirports,
    required this.uncomfortableAirports,
    required this.upcomingFlights,
    required this.aviationPreferences,
    required this.successfulCopingStrategies,
  });

  factory AviationData.empty() {
    return const AviationData(
      flightHistory: [],
      preferredAirlines: [],
      preferredAircraftTypes: [],
      flightFears: {},
      comfortableAirports: [],
      uncomfortableAirports: [],
      upcomingFlights: [],
      aviationPreferences: {},
      successfulCopingStrategies: [],
    );
  }

  factory AviationData.fromMap(Map<String, dynamic> map) {
    return AviationData(
      flightHistory: (map['flightHistory'] as List<dynamic>?)
          ?.map((e) => FlightExperience.fromMap(e as Map<String, dynamic>))
          .toList() ?? [],
      preferredAirlines: List<String>.from(map['preferredAirlines'] ?? []),
      preferredAircraftTypes: List<String>.from(map['preferredAircraftTypes'] ?? []),
      flightFears: Map<String, int>.from(map['flightFears'] ?? {}),
      comfortableAirports: List<String>.from(map['comfortableAirports'] ?? []),
      uncomfortableAirports: List<String>.from(map['uncomfortableAirports'] ?? []),
      upcomingFlights: (map['upcomingFlights'] as List<dynamic>?)
          ?.map((e) => UpcomingFlight.fromMap(e as Map<String, dynamic>))
          .toList() ?? [],
      aviationPreferences: Map<String, String>.from(map['aviationPreferences'] ?? {}),
      successfulCopingStrategies: List<String>.from(map['successfulCopingStrategies'] ?? []),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'flightHistory': flightHistory.map((e) => e.toMap()).toList(),
      'preferredAirlines': preferredAirlines,
      'preferredAircraftTypes': preferredAircraftTypes,
      'flightFears': flightFears,
      'comfortableAirports': comfortableAirports,
      'uncomfortableAirports': uncomfortableAirports,
      'upcomingFlights': upcomingFlights.map((e) => e.toMap()).toList(),
      'aviationPreferences': aviationPreferences,
      'successfulCopingStrategies': successfulCopingStrategies,
    };
  }

  AviationData copyWith({
    List<FlightExperience>? flightHistory,
    List<String>? preferredAirlines,
    List<String>? preferredAircraftTypes,
    Map<String, int>? flightFears,
    List<String>? comfortableAirports,
    List<String>? uncomfortableAirports,
    List<UpcomingFlight>? upcomingFlights,
    Map<String, String>? aviationPreferences,
    List<String>? successfulCopingStrategies,
  }) {
    return AviationData(
      flightHistory: flightHistory ?? this.flightHistory,
      preferredAirlines: preferredAirlines ?? this.preferredAirlines,
      preferredAircraftTypes: preferredAircraftTypes ?? this.preferredAircraftTypes,
      flightFears: flightFears ?? this.flightFears,
      comfortableAirports: comfortableAirports ?? this.comfortableAirports,
      uncomfortableAirports: uncomfortableAirports ?? this.uncomfortableAirports,
      upcomingFlights: upcomingFlights ?? this.upcomingFlights,
      aviationPreferences: aviationPreferences ?? this.aviationPreferences,
      successfulCopingStrategies: successfulCopingStrategies ?? this.successfulCopingStrategies,
    );
  }

  @override
  List<Object?> get props => [
    flightHistory, preferredAirlines, preferredAircraftTypes, flightFears,
    comfortableAirports, uncomfortableAirports, upcomingFlights,
    aviationPreferences, successfulCopingStrategies
  ];
}

/// Individual flight experience record
class FlightExperience extends Equatable {
  final String id;
  final String departureAirport;
  final String arrivalAirport;
  final String airline;
  final String aircraftType;
  final DateTime flightDate;
  final int anxietyLevel; // 1-10 scale
  final String? experience; // positive/negative/neutral
  final List<String> whatWorked;
  final List<String> whatDidntWork;
  final String? notes;

  const FlightExperience({
    required this.id,
    required this.departureAirport,
    required this.arrivalAirport,
    required this.airline,
    required this.aircraftType,
    required this.flightDate,
    required this.anxietyLevel,
    this.experience,
    required this.whatWorked,
    required this.whatDidntWork,
    this.notes,
  });

  factory FlightExperience.fromMap(Map<String, dynamic> map) {
    return FlightExperience(
      id: map['id'] ?? '',
      departureAirport: map['departureAirport'] ?? '',
      arrivalAirport: map['arrivalAirport'] ?? '',
      airline: map['airline'] ?? '',
      aircraftType: map['aircraftType'] ?? '',
      flightDate: DateTime.fromMillisecondsSinceEpoch(map['flightDate'] ?? 0),
      anxietyLevel: map['anxietyLevel'] ?? 5,
      experience: map['experience'] as String?,
      whatWorked: List<String>.from(map['whatWorked'] ?? []),
      whatDidntWork: List<String>.from(map['whatDidntWork'] ?? []),
      notes: map['notes'] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'departureAirport': departureAirport,
      'arrivalAirport': arrivalAirport,
      'airline': airline,
      'aircraftType': aircraftType,
      'flightDate': flightDate.millisecondsSinceEpoch,
      'anxietyLevel': anxietyLevel,
      'experience': experience,
      'whatWorked': whatWorked,
      'whatDidntWork': whatDidntWork,
      'notes': notes,
    };
  }

  @override
  List<Object?> get props => [
    id, departureAirport, arrivalAirport, airline, aircraftType,
    flightDate, anxietyLevel, experience, whatWorked, whatDidntWork, notes
  ];
}

/// Upcoming flight information
class UpcomingFlight extends Equatable {
  final String id;
  final String departureAirport;
  final String arrivalAirport;
  final String airline;
  final DateTime flightDate;
  final String? flightNumber;
  final int currentAnxietyLevel; // 1-10 scale
  final List<String> concerns;
  final List<String> preparationStrategies;

  const UpcomingFlight({
    required this.id,
    required this.departureAirport,
    required this.arrivalAirport,
    required this.airline,
    required this.flightDate,
    this.flightNumber,
    required this.currentAnxietyLevel,
    required this.concerns,
    required this.preparationStrategies,
  });

  factory UpcomingFlight.fromMap(Map<String, dynamic> map) {
    return UpcomingFlight(
      id: map['id'] ?? '',
      departureAirport: map['departureAirport'] ?? '',
      arrivalAirport: map['arrivalAirport'] ?? '',
      airline: map['airline'] ?? '',
      flightDate: DateTime.fromMillisecondsSinceEpoch(map['flightDate'] ?? 0),
      flightNumber: map['flightNumber'] as String?,
      currentAnxietyLevel: map['currentAnxietyLevel'] ?? 5,
      concerns: List<String>.from(map['concerns'] ?? []),
      preparationStrategies: List<String>.from(map['preparationStrategies'] ?? []),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'departureAirport': departureAirport,
      'arrivalAirport': arrivalAirport,
      'airline': airline,
      'flightDate': flightDate.millisecondsSinceEpoch,
      'flightNumber': flightNumber,
      'currentAnxietyLevel': currentAnxietyLevel,
      'concerns': concerns,
      'preparationStrategies': preparationStrategies,
    };
  }

  @override
  List<Object?> get props => [
    id, departureAirport, arrivalAirport, airline, flightDate,
    flightNumber, currentAnxietyLevel, concerns, preparationStrategies
  ];
}

/// Behavioral patterns and user interaction data
class BehavioralPatterns extends Equatable {
  final Map<String, List<DateTime>> anxietyTriggerTimes; // trigger -> timestamps
  final Map<String, int> sessionFrequency; // day_of_week -> count
  final List<String> preferredCopingMethods;
  final Map<String, double> copingEffectiveness; // method -> effectiveness (0-1)
  final List<String> conversationTopics;
  final Map<String, int> moodPatterns; // mood -> frequency
  final List<DateTime> highAnxietyPeriods;
  final List<DateTime> lowAnxietyPeriods;
  final Map<String, dynamic> engagementMetrics;

  const BehavioralPatterns({
    required this.anxietyTriggerTimes,
    required this.sessionFrequency,
    required this.preferredCopingMethods,
    required this.copingEffectiveness,
    required this.conversationTopics,
    required this.moodPatterns,
    required this.highAnxietyPeriods,
    required this.lowAnxietyPeriods,
    required this.engagementMetrics,
  });

  factory BehavioralPatterns.empty() {
    return const BehavioralPatterns(
      anxietyTriggerTimes: {},
      sessionFrequency: {},
      preferredCopingMethods: [],
      copingEffectiveness: {},
      conversationTopics: [],
      moodPatterns: {},
      highAnxietyPeriods: [],
      lowAnxietyPeriods: [],
      engagementMetrics: {},
    );
  }

  factory BehavioralPatterns.fromMap(Map<String, dynamic> map) {
    return BehavioralPatterns(
      anxietyTriggerTimes: (map['anxietyTriggerTimes'] as Map<String, dynamic>?)
          ?.map((key, value) => MapEntry(
              key,
              (value as List<dynamic>)
                  .map((e) => DateTime.fromMillisecondsSinceEpoch(e as int))
                  .toList())) ?? {},
      sessionFrequency: Map<String, int>.from(map['sessionFrequency'] ?? {}),
      preferredCopingMethods: List<String>.from(map['preferredCopingMethods'] ?? []),
      copingEffectiveness: Map<String, double>.from(map['copingEffectiveness'] ?? {}),
      conversationTopics: List<String>.from(map['conversationTopics'] ?? []),
      moodPatterns: Map<String, int>.from(map['moodPatterns'] ?? {}),
      highAnxietyPeriods: (map['highAnxietyPeriods'] as List<dynamic>?)
          ?.map((e) => DateTime.fromMillisecondsSinceEpoch(e as int))
          .toList() ?? [],
      lowAnxietyPeriods: (map['lowAnxietyPeriods'] as List<dynamic>?)
          ?.map((e) => DateTime.fromMillisecondsSinceEpoch(e as int))
          .toList() ?? [],
      engagementMetrics: Map<String, dynamic>.from(map['engagementMetrics'] ?? {}),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'anxietyTriggerTimes': anxietyTriggerTimes.map((key, value) =>
          MapEntry(key, value.map((e) => e.millisecondsSinceEpoch).toList())),
      'sessionFrequency': sessionFrequency,
      'preferredCopingMethods': preferredCopingMethods,
      'copingEffectiveness': copingEffectiveness,
      'conversationTopics': conversationTopics,
      'moodPatterns': moodPatterns,
      'highAnxietyPeriods': highAnxietyPeriods.map((e) => e.millisecondsSinceEpoch).toList(),
      'lowAnxietyPeriods': lowAnxietyPeriods.map((e) => e.millisecondsSinceEpoch).toList(),
      'engagementMetrics': engagementMetrics,
    };
  }

  BehavioralPatterns copyWith({
    Map<String, List<DateTime>>? anxietyTriggerTimes,
    Map<String, int>? sessionFrequency,
    List<String>? preferredCopingMethods,
    Map<String, double>? copingEffectiveness,
    List<String>? conversationTopics,
    Map<String, int>? moodPatterns,
    List<DateTime>? highAnxietyPeriods,
    List<DateTime>? lowAnxietyPeriods,
    Map<String, dynamic>? engagementMetrics,
  }) {
    return BehavioralPatterns(
      anxietyTriggerTimes: anxietyTriggerTimes ?? this.anxietyTriggerTimes,
      sessionFrequency: sessionFrequency ?? this.sessionFrequency,
      preferredCopingMethods: preferredCopingMethods ?? this.preferredCopingMethods,
      copingEffectiveness: copingEffectiveness ?? this.copingEffectiveness,
      conversationTopics: conversationTopics ?? this.conversationTopics,
      moodPatterns: moodPatterns ?? this.moodPatterns,
      highAnxietyPeriods: highAnxietyPeriods ?? this.highAnxietyPeriods,
      lowAnxietyPeriods: lowAnxietyPeriods ?? this.lowAnxietyPeriods,
      engagementMetrics: engagementMetrics ?? this.engagementMetrics,
    );
  }

  @override
  List<Object?> get props => [
    anxietyTriggerTimes, sessionFrequency, preferredCopingMethods,
    copingEffectiveness, conversationTopics, moodPatterns,
    highAnxietyPeriods, lowAnxietyPeriods, engagementMetrics
  ];
}

/// Therapy progress and milestone tracking
class TherapyProgress extends Equatable {
  final List<TherapyGoal> goals;
  final List<TherapyMilestone> milestones;
  final Map<String, double> progressMetrics; // metric -> score (0-1)
  final List<String> completedExercises;
  final Map<String, int> exerciseEffectiveness; // exercise -> rating (1-10)
  final List<DateTime> breakthroughMoments;
  final List<DateTime> setbackMoments;
  final Map<String, String> insights; // date -> insight
  final double overallProgress; // 0-1 scale
  final DateTime? lastAssessment;

  const TherapyProgress({
    required this.goals,
    required this.milestones,
    required this.progressMetrics,
    required this.completedExercises,
    required this.exerciseEffectiveness,
    required this.breakthroughMoments,
    required this.setbackMoments,
    required this.insights,
    required this.overallProgress,
    this.lastAssessment,
  });

  factory TherapyProgress.empty() {
    return const TherapyProgress(
      goals: [],
      milestones: [],
      progressMetrics: {},
      completedExercises: [],
      exerciseEffectiveness: {},
      breakthroughMoments: [],
      setbackMoments: [],
      insights: {},
      overallProgress: 0.0,
    );
  }

  factory TherapyProgress.fromMap(Map<String, dynamic> map) {
    return TherapyProgress(
      goals: (map['goals'] as List<dynamic>?)
          ?.map((e) => TherapyGoal.fromMap(e as Map<String, dynamic>))
          .toList() ?? [],
      milestones: (map['milestones'] as List<dynamic>?)
          ?.map((e) => TherapyMilestone.fromMap(e as Map<String, dynamic>))
          .toList() ?? [],
      progressMetrics: Map<String, double>.from(map['progressMetrics'] ?? {}),
      completedExercises: List<String>.from(map['completedExercises'] ?? []),
      exerciseEffectiveness: Map<String, int>.from(map['exerciseEffectiveness'] ?? {}),
      breakthroughMoments: (map['breakthroughMoments'] as List<dynamic>?)
          ?.map((e) => DateTime.fromMillisecondsSinceEpoch(e as int))
          .toList() ?? [],
      setbackMoments: (map['setbackMoments'] as List<dynamic>?)
          ?.map((e) => DateTime.fromMillisecondsSinceEpoch(e as int))
          .toList() ?? [],
      insights: Map<String, String>.from(map['insights'] ?? {}),
      overallProgress: (map['overallProgress'] as num?)?.toDouble() ?? 0.0,
      lastAssessment: map['lastAssessment'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['lastAssessment'] as int)
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'goals': goals.map((e) => e.toMap()).toList(),
      'milestones': milestones.map((e) => e.toMap()).toList(),
      'progressMetrics': progressMetrics,
      'completedExercises': completedExercises,
      'exerciseEffectiveness': exerciseEffectiveness,
      'breakthroughMoments': breakthroughMoments.map((e) => e.millisecondsSinceEpoch).toList(),
      'setbackMoments': setbackMoments.map((e) => e.millisecondsSinceEpoch).toList(),
      'insights': insights,
      'overallProgress': overallProgress,
      'lastAssessment': lastAssessment?.millisecondsSinceEpoch,
    };
  }

  TherapyProgress copyWith({
    List<TherapyGoal>? goals,
    List<TherapyMilestone>? milestones,
    Map<String, double>? progressMetrics,
    List<String>? completedExercises,
    Map<String, int>? exerciseEffectiveness,
    List<DateTime>? breakthroughMoments,
    List<DateTime>? setbackMoments,
    Map<String, String>? insights,
    double? overallProgress,
    DateTime? lastAssessment,
  }) {
    return TherapyProgress(
      goals: goals ?? this.goals,
      milestones: milestones ?? this.milestones,
      progressMetrics: progressMetrics ?? this.progressMetrics,
      completedExercises: completedExercises ?? this.completedExercises,
      exerciseEffectiveness: exerciseEffectiveness ?? this.exerciseEffectiveness,
      breakthroughMoments: breakthroughMoments ?? this.breakthroughMoments,
      setbackMoments: setbackMoments ?? this.setbackMoments,
      insights: insights ?? this.insights,
      overallProgress: overallProgress ?? this.overallProgress,
      lastAssessment: lastAssessment ?? this.lastAssessment,
    );
  }

  @override
  List<Object?> get props => [
    goals, milestones, progressMetrics, completedExercises,
    exerciseEffectiveness, breakthroughMoments, setbackMoments,
    insights, overallProgress, lastAssessment
  ];
}

/// Individual therapy goal
class TherapyGoal extends Equatable {
  final String id;
  final String title;
  final String description;
  final DateTime createdAt;
  final DateTime? targetDate;
  final bool isCompleted;
  final DateTime? completedAt;
  final double progress; // 0-1 scale
  final List<String> steps;
  final Map<String, bool> stepCompletion;

  const TherapyGoal({
    required this.id,
    required this.title,
    required this.description,
    required this.createdAt,
    this.targetDate,
    required this.isCompleted,
    this.completedAt,
    required this.progress,
    required this.steps,
    required this.stepCompletion,
  });

  factory TherapyGoal.fromMap(Map<String, dynamic> map) {
    return TherapyGoal(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      targetDate: map['targetDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['targetDate'] as int)
          : null,
      isCompleted: map['isCompleted'] ?? false,
      completedAt: map['completedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['completedAt'] as int)
          : null,
      progress: (map['progress'] as num?)?.toDouble() ?? 0.0,
      steps: List<String>.from(map['steps'] ?? []),
      stepCompletion: Map<String, bool>.from(map['stepCompletion'] ?? {}),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'targetDate': targetDate?.millisecondsSinceEpoch,
      'isCompleted': isCompleted,
      'completedAt': completedAt?.millisecondsSinceEpoch,
      'progress': progress,
      'steps': steps,
      'stepCompletion': stepCompletion,
    };
  }

  @override
  List<Object?> get props => [
    id, title, description, createdAt, targetDate,
    isCompleted, completedAt, progress, steps, stepCompletion
  ];
}

/// Therapy milestone achievement
class TherapyMilestone extends Equatable {
  final String id;
  final String title;
  final String description;
  final DateTime achievedAt;
  final String category; // e.g., 'flight_anxiety', 'coping_skills', 'confidence'
  final int significance; // 1-10 scale
  final String? notes;

  const TherapyMilestone({
    required this.id,
    required this.title,
    required this.description,
    required this.achievedAt,
    required this.category,
    required this.significance,
    this.notes,
  });

  factory TherapyMilestone.fromMap(Map<String, dynamic> map) {
    return TherapyMilestone(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      achievedAt: DateTime.fromMillisecondsSinceEpoch(map['achievedAt'] ?? 0),
      category: map['category'] ?? '',
      significance: map['significance'] ?? 5,
      notes: map['notes'] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'achievedAt': achievedAt.millisecondsSinceEpoch,
      'category': category,
      'significance': significance,
      'notes': notes,
    };
  }

  @override
  List<Object?> get props => [
    id, title, description, achievedAt, category, significance, notes
  ];
}
