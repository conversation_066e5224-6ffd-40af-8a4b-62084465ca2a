import 'package:flutter/foundation.dart';
import '../models/subscription_model.dart';
import '../services/dynamic_plan_service.dart';

class SubscriptionConstants {
  // Static fallback configurations - used when dynamic config is unavailable
  static const Map<SubscriptionPlan, Map<String, dynamic>> _staticPlanConfigs = {
    SubscriptionPlan.free: {
      'id': 'free',
      'name': 'Free Plan',
      'price': 0.0,
      'chatCredits': 25,
      'voiceCredits': 5,
      'durationDays': 7,
      'description': 'Perfect for trying out the app',
      'features': [
        '25 chat messages',
        '5 voice messages',
        '7-day access',
        'Breathing Excercise',
      ],
      'popular': false,
      'color': 0xFF9E9E9E,
      'available': true,
    },
    SubscriptionPlan.basic: {
      'id': 'basic',
      'name': 'Basic Plan',
      'price': 15.0,
      'chatCredits': 13000,
      'voiceCredits': 80,
      'durationDays': 30,
      'description': 'Great for regular users',
      'features': [
        '13,000 chat messages',
        '80 voice messages',
        'Chat history',
        '30-day access',
        'Breathing Excercise',
      ],
      'popular': true,
      'color': 0xFF2196F3,
      'available': true,
      'lemonsqueezyProductId': 'YOUR_BASIC_PLAN_ID',
    },
    SubscriptionPlan.premium: {
      'id': 'premium',
      'name': 'Premium Plan',
      'price': 30.0,
      'chatCredits': 20000,
      'voiceCredits': 300,
      'durationDays': 30,
      'description': 'Best value for power users',
      'features': [
        '20,000 chat messages',
        '300 voice messages',
        'Chat History',
        '30-days access',
        'Breathing Excercise',
      ],
      'popular': false,
      'color': 0xFFFF9800,
      'available': true,
      'lemonsqueezyProductId': 'YOUR_PREMIUM_PLAN_ID',
    },
  };

  static const Map<SubscriptionPlan, Map<String, int>> creditLimits = {
    SubscriptionPlan.free: {'chat': 25, 'voice': 5},
    SubscriptionPlan.basic: {'chat': 13000, 'voice': 80},
    SubscriptionPlan.premium: {'chat': 20000, 'voice': 300},
  };

  static const Map<SubscriptionPlan, int> planDurations = {
    SubscriptionPlan.free: 7,
    SubscriptionPlan.basic: 30,
    SubscriptionPlan.premium: 30,
  };

  static const Map<SubscriptionPlan, double> planPrices = {
    SubscriptionPlan.free: 0.0,
    SubscriptionPlan.basic: 15.0,
    SubscriptionPlan.premium: 30.0,
  };

  // Dynamic plan configuration methods - these use real-time data from Firestore
  static Map<String, dynamic> getPlanConfig(SubscriptionPlan plan) {
    try {
      final dynamicService = DynamicPlanService();

      // Check if the service is initialized and has data
      if (dynamicService.isInitialized) {
        final config = dynamicService.getPlanConfig(plan);

        // Verify the config has essential data (less strict validation)
        if (config.isNotEmpty && config['name'] != null) {
          // Merge dynamic config with static fallback for missing fields
          final staticConfig = _staticPlanConfigs[plan] ?? _staticPlanConfigs[SubscriptionPlan.free]!;
          final mergedConfig = Map<String, dynamic>.from(staticConfig);

          // Override with dynamic values where available
          config.forEach((key, value) {
            if (value != null) {
              mergedConfig[key] = value;
            }
          });

          return mergedConfig;
        }
      }
    } catch (e) {
      debugPrint('SubscriptionConstants: Error getting dynamic config: $e');
    }

    // Fallback to static configuration if dynamic fails or is incomplete
    final staticConfig = _staticPlanConfigs[plan] ?? _staticPlanConfigs[SubscriptionPlan.free]!;
    return Map<String, dynamic>.from(staticConfig);
  }

  static int getChatLimit(SubscriptionPlan plan) {
    final config = getPlanConfig(plan);
    final credits = config['chatCredits'] ?? creditLimits[plan]?['chat'] ?? 0;
    // Ensure we return an int
    if (credits is double) return credits.toInt();
    return credits as int;
  }

  static int getVoiceLimit(SubscriptionPlan plan) {
    final config = getPlanConfig(plan);
    final credits = config['voiceCredits'] ?? creditLimits[plan]?['voice'] ?? 0;
    // Ensure we return an int
    if (credits is double) return credits.toInt();
    return credits as int;
  }

  static int getPlanDuration(SubscriptionPlan plan) {
    final config = getPlanConfig(plan);
    return config['durationDays'] ?? planDurations[plan] ?? 7;
  }

  static double getPlanPrice(SubscriptionPlan plan) {
    final config = getPlanConfig(plan);
    return (config['price'] ?? planPrices[plan] ?? 0.0).toDouble();
  }

  static String getPlanName(SubscriptionPlan plan) {
    return getPlanConfig(plan)['name'] ?? 'Unknown Plan';
  }

  static List<String> getPlanFeatures(SubscriptionPlan plan) {
    return List<String>.from(getPlanConfig(plan)['features'] ?? []);
  }

  static bool isPlanPopular(SubscriptionPlan plan) {
    return getPlanConfig(plan)['popular'] ?? false;
  }

  static int getPlanColor(SubscriptionPlan plan) {
    return getPlanConfig(plan)['color'] ?? 0xFF9E9E9E;
  }

  static bool isPlanAvailable(SubscriptionPlan plan) {
    return getPlanConfig(plan)['available'] ?? false;
  }

  static String? getLemonSqueezyProductId(SubscriptionPlan plan) {
    return getPlanConfig(plan)['lemonsqueezyProductId'];
  }

  // New methods for real-time plan updates
  static Stream<Map<SubscriptionPlan, Map<String, dynamic>>> get planConfigStream {
    return DynamicPlanService().planConfigStream;
  }

  static Future<void> initializeDynamicPlans() async {
    await DynamicPlanService().initialize();
  }

  static Future<void> refreshPlanConfigurations() async {
    await DynamicPlanService().refresh();
  }

  // Utility method to check if plan configurations support a feature
  static bool planSupportsFeature(SubscriptionPlan plan, String feature) {
    final features = getPlanFeatures(plan);
    return features.any((f) => f.toLowerCase().contains(feature.toLowerCase()));
  }

  // Method to get all available plans with current configurations
  static List<Map<String, dynamic>> getAllAvailablePlans() {
    return SubscriptionPlan.values
        .where((plan) => isPlanAvailable(plan))
        .map((plan) => {
              'plan': plan,
              ...getPlanConfig(plan),
            })
        .toList();
  }

  // Backward compatibility method
  static bool canUpgradeTo(SubscriptionPlan? currentPlan, SubscriptionPlan targetPlan) {
    if (currentPlan == null) return true;
    if (currentPlan == targetPlan) return false;

    final currentPrice = getPlanPrice(currentPlan);
    final targetPrice = getPlanPrice(targetPlan);

    return targetPrice > currentPrice;
  }



  static bool canDowngradeTo(SubscriptionPlan currentPlan, SubscriptionPlan targetPlan) {
    final currentIndex = SubscriptionPlan.values.indexOf(currentPlan);
    final targetIndex = SubscriptionPlan.values.indexOf(targetPlan);
    return targetIndex < currentIndex;
  }

  static List<SubscriptionPlan> getUpgradeOptions(SubscriptionPlan currentPlan) {
    return SubscriptionPlan.values
        .where((plan) => canUpgradeTo(currentPlan, plan))
        .toList();
  }

  static const double warningThreshold = 0.8;
  static const double criticalThreshold = 0.95;

  static bool isNearLimit(int used, int limit) {
    if (limit == 0) return false;
    return (used / limit) >= warningThreshold;
  }

  static bool isCriticalLimit(int used, int limit) {
    if (limit == 0) return false;
    return (used / limit) >= criticalThreshold;
  }

  static const String noCreditsMessage = 'You have run out of credits. Please upgrade your plan to continue.';
  static const String expiredSubscriptionMessage = 'Your subscription has expired. Please renew to continue using the app.';
  static const String upgradeRequiredMessage = 'Upgrade your plan to access this feature.';
  
  static const String subscriptionUpgradeSuccess = 'Successfully upgraded your subscription!';
  static const String creditsRefreshed = 'Your credits have been refreshed for the new billing cycle.';

  static const String lemonsqueezyStoreId = 'your_store_id';
  static const String lemonsqueezyBaseUrl = 'https://api.lemonsqueezy.com/v1';

  static const String lemonsqueezyWebhookUrl = 'https://your-domain.com/webhook';
  
  static const int maxConversationHistory = 100;
  static const int maxMessageHistory = 1000;
  static const Duration sessionTimeout = Duration(hours: 24);
  
  static const bool enableStripe = false;
  static const bool enablePayPal = false;
  static const bool enableAnalytics = true;
  static const bool enableErrorReporting = true;
}
