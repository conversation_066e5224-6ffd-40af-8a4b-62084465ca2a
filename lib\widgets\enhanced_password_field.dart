import 'package:flutter/material.dart';
import '../utils/theme.dart';
import 'password_requirements_widget.dart';

class EnhancedPasswordField extends StatefulWidget {
  final String? label;
  final String? hint;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final bool showRequirements;
  final IconData? prefixIcon;
  final TextInputAction textInputAction;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;

  const EnhancedPasswordField({
    super.key,
    this.label,
    this.hint,
    this.controller,
    this.validator,
    this.showRequirements = true,
    this.prefixIcon,
    this.textInputAction = TextInputAction.done,
    this.onChanged,
    this.onSubmitted,
  });

  @override
  State<EnhancedPasswordField> createState() => _EnhancedPasswordFieldState();
}

class _EnhancedPasswordFieldState extends State<EnhancedPasswordField> {
  bool _isObscured = true;
  bool _hasFocus = false;
  String _currentPassword = '';

  @override
  void initState() {
    super.initState();
    _currentPassword = widget.controller?.text ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
        ],
        Focus(
          onFocusChange: (hasFocus) {
            setState(() {
              _hasFocus = hasFocus;
            });
          },
          child: TextFormField(
            controller: widget.controller,
            validator: widget.validator,
            obscureText: _isObscured,
            textInputAction: widget.textInputAction,
            onChanged: (value) {
              setState(() {
                _currentPassword = value;
              });
              widget.onChanged?.call(value);
            },
            onFieldSubmitted: widget.onSubmitted,
            style: Theme.of(context).textTheme.bodyLarge,
            decoration: InputDecoration(
              hintText: widget.hint,
              prefixIcon: widget.prefixIcon != null
                  ? Icon(
                      widget.prefixIcon,
                      color: _hasFocus ? AppTheme.primaryColor : AppTheme.textSecondary,
                    )
                  : null,
              suffixIcon: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (_currentPassword.isNotEmpty && widget.showRequirements) ...[
                    _buildStrengthIndicator(),
                    const SizedBox(width: 8),
                  ],
                  IconButton(
                    icon: Icon(
                      _isObscured ? Icons.visibility_off : Icons.visibility,
                      color: _hasFocus ? AppTheme.primaryColor : AppTheme.textSecondary,
                    ),
                    onPressed: () {
                      setState(() {
                        _isObscured = !_isObscured;
                      });
                    },
                  ),
                ],
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.errorColor, width: 2),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.errorColor, width: 2),
              ),
              filled: true,
              fillColor: _hasFocus ? Colors.blue.shade50 : Colors.grey.shade50,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
          ),
        ),
        if (widget.showRequirements && (_hasFocus || _currentPassword.isNotEmpty))
          PasswordRequirementsWidget(
            password: _currentPassword,
            showRequirements: true,
          ),
      ],
    );
  }

  Widget _buildStrengthIndicator() {
    final requirements = [
      _currentPassword.length >= 8,
      _currentPassword.contains(RegExp(r'[A-Z]')),
      _currentPassword.contains(RegExp(r'[a-z]')),
      _currentPassword.contains(RegExp(r'[0-9]')),
      _currentPassword.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]')),
    ];
    
    final validCount = requirements.where((req) => req).length;
    final strength = validCount / requirements.length;
    
    Color strengthColor;
    if (strength <= 0.2) {
      strengthColor = Colors.red;
    } else if (strength <= 0.4) {
      strengthColor = Colors.orange;
    } else if (strength <= 0.6) {
      strengthColor = Colors.yellow.shade700;
    } else if (strength <= 0.8) {
      strengthColor = Colors.blue;
    } else {
      strengthColor = Colors.green;
    }

    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        color: strengthColor.withValues(alpha: 0.1),
        shape: BoxShape.circle,
        border: Border.all(color: strengthColor, width: 2),
      ),
      child: Center(
        child: Text(
          '$validCount',
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.bold,
            color: strengthColor,
          ),
        ),
      ),
    );
  }
}
