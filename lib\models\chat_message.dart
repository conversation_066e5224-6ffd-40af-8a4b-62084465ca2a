import 'package:equatable/equatable.dart';

enum MessageType { user, ai }

class ChatMessage extends Equatable {
  final String id;
  final String userId;
  final String content;
  final MessageType type;
  final DateTime timestamp;
  final bool isLoading;
  final Map<String, dynamic>? metadata;

  const ChatMessage({
    required this.id,
    required this.userId,
    required this.content,
    required this.type,
    required this.timestamp,
    this.isLoading = false,
    this.metadata,
  });

  factory ChatMessage.fromMap(Map<String, dynamic> map) {
    return ChatMessage(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      content: map['content'] ?? '',
      type: MessageType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => MessageType.user,
      ),
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp'] ?? 0),
      isLoading: map['isLoading'] ?? false,
      metadata: map['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'content': content,
      'type': type.name,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'isLoading': isLoading,
      'metadata': metadata,
    };
  }

  ChatMessage copyWith({
    String? id,
    String? userId,
    String? content,
    MessageType? type,
    DateTime? timestamp,
    bool? isLoading,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      content: content ?? this.content,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      isLoading: isLoading ?? this.isLoading,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [id, userId, content, type, timestamp, isLoading, metadata];
}