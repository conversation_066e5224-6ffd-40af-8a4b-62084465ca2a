import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/subscription_model.dart';
import '../utils/constants.dart';

/// Service for managing dynamic plan configurations with real-time updates
class DynamicPlanService {
  static const String _documentId = 'plan_configuration';

  static final DynamicPlanService _instance = DynamicPlanService._internal();
  factory DynamicPlanService() => _instance;
  DynamicPlanService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // Real-time plan configurations
  Map<SubscriptionPlan, Map<String, dynamic>> _currentConfigs = {};
  
  // Stream controllers for real-time updates
  final StreamController<Map<SubscriptionPlan, Map<String, dynamic>>> _planConfigController = 
      StreamController<Map<SubscriptionPlan, Map<String, dynamic>>>.broadcast();
  
  // Listeners for plan configuration changes
  StreamSubscription<DocumentSnapshot>? _configSubscription;
  
  bool _isInitialized = false;
  bool _isLoading = false;

  /// Initialize the service and start listening for real-time updates
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _isLoading = true;
      debugPrint('DynamicPlanService: Initializing...');
      
      // Load initial configuration
      await _loadInitialConfiguration();
      
      // Start real-time listener
      _startRealtimeListener();
      
      _isInitialized = true;
      debugPrint('DynamicPlanService: Initialized successfully');
    } catch (e) {
      debugPrint('DynamicPlanService: Initialization failed: $e');
      // Use fallback configuration
      _loadFallbackConfiguration();
    } finally {
      _isLoading = false;
    }
  }

  /// Load initial plan configuration from Firestore
  Future<void> _loadInitialConfiguration() async {
    try {
      final doc = await _firestore
          .collection(AppConstants.appConfigCollection)
          .doc(_documentId)
          .get();

      if (doc.exists && doc.data() != null) {
        _updateConfigurationsFromFirestore(doc.data()!);
        debugPrint('DynamicPlanService: Loaded configuration from Firestore');
      } else {
        debugPrint('DynamicPlanService: No configuration found, creating default configuration');
        await _createDefaultConfiguration();
        _loadFallbackConfiguration();
      }
    } catch (e) {
      debugPrint('DynamicPlanService: Error loading initial configuration: $e');
      _loadFallbackConfiguration();
    }
  }

  /// Create default configuration document in Firestore
  Future<void> _createDefaultConfiguration() async {
    try {
      final defaultConfig = {
        'free': {
          'id': 'free',
          'name': 'Free Plan',
          'price': 0.0,
          'chatCredits': 25,
          'voiceCredits': 5,
          'durationDays': 7,
          'description': 'Perfect for trying out the app',
          'features': [
            '25 chat messages',
            '5 voice messages',
            '7-day access',
            'Breathing Exercise',
          ],
          'popular': false,
          'color': 0xFF9E9E9E,
          'available': true,
        },
        'basic': {
          'id': 'basic',
          'name': 'Basic Plan',
          'price': 15.0,
          'chatCredits': 13000,
          'voiceCredits': 80,
          'durationDays': 30,
          'description': 'Great for regular users',
          'features': [
            '13,000 chat messages',
            '80 voice messages',
            'Chat history',
            '30-day access',
            'Breathing Exercise',
          ],
          'popular': true,
          'color': 0xFF2196F3,
          'available': true,
        },
        'premium': {
          'id': 'premium',
          'name': 'Premium Plan',
          'price': 30.0,
          'chatCredits': 20000,
          'voiceCredits': 300,
          'durationDays': 30,
          'description': 'Best value for power users',
          'features': [
            '20,000 chat messages',
            '300 voice messages',
            'Chat History',
            '30-days access',
            'Breathing Exercise',
          ],
          'popular': false,
          'color': 0xFFFF9800,
          'available': true,
        },
      };

      await _firestore
          .collection(AppConstants.appConfigCollection)
          .doc(_documentId)
          .set(defaultConfig);

      debugPrint('DynamicPlanService: Created default configuration document');
    } catch (e) {
      debugPrint('DynamicPlanService: Failed to create default configuration: $e');
    }
  }

  /// Start real-time listener for plan configuration changes
  void _startRealtimeListener() {
    _configSubscription = _firestore
        .collection(AppConstants.appConfigCollection)
        .doc(_documentId)
        .snapshots()
        .listen(
          (snapshot) {
            if (snapshot.exists && snapshot.data() != null) {
              debugPrint('DynamicPlanService: Received real-time configuration update');
              _updateConfigurationsFromFirestore(snapshot.data()!);
              
              // Notify all listeners of the update
              _planConfigController.add(_currentConfigs);
            }
          },
          onError: (error) {
            debugPrint('DynamicPlanService: Real-time listener error: $error');
          },
        );
  }

  /// Update configurations from Firestore data
  void _updateConfigurationsFromFirestore(Map<String, dynamic> data) {
    final newConfigs = <SubscriptionPlan, Map<String, dynamic>>{};

    for (final plan in SubscriptionPlan.values) {
      final planData = data[plan.name] as Map<String, dynamic>?;
      if (planData != null) {
        // Ensure proper type casting for numeric values
        final normalizedData = <String, dynamic>{};
        for (final entry in planData.entries) {
          final key = entry.key;
          final value = entry.value;

          // Handle type conversions for numeric fields
          if (key == 'price') {
            normalizedData[key] = (value is int) ? value.toDouble() : (value as double? ?? 0.0);
          } else if (key == 'chatCredits' || key == 'voiceCredits' || key == 'durationDays') {
            normalizedData[key] = (value is double) ? value.toInt() : (value as int? ?? 0);
          } else if (key == 'color') {
            normalizedData[key] = (value is String) ? int.parse(value) : (value as int? ?? 0xFF2196F3);
          } else {
            normalizedData[key] = value;
          }
        }
        newConfigs[plan] = normalizedData;
      }
    }

    if (newConfigs.isNotEmpty) {
      _currentConfigs = newConfigs;
      debugPrint('DynamicPlanService: Updated ${newConfigs.length} plan configurations');
    }
  }

  /// Load fallback configuration when Firestore is unavailable
  void _loadFallbackConfiguration() {
    _currentConfigs = {
      SubscriptionPlan.free: {
        'id': 'free',
        'name': 'Free Plan',
        'price': 0.0,
        'chatCredits': 25,
        'voiceCredits': 5,
        'durationDays': 7,
        'description': 'Perfect for trying out the app',
        'features': ['25 chat messages', '5 voice messages', '7-day access', 'Breathing Exercise'],
        'popular': false,
        'color': 0xFF9E9E9E,
        'available': true,
      },
      SubscriptionPlan.basic: {
        'id': 'basic',
        'name': 'Basic Plan',
        'price': 15.0,
        'chatCredits': 13000,
        'voiceCredits': 80,
        'durationDays': 30,
        'description': 'Great for regular users',
        'features': ['13,000 chat messages', '80 voice messages', 'Chat history', '30-day access', 'Breathing Exercise'],
        'popular': true,
        'color': 0xFF2196F3,
        'available': true,
      },
      SubscriptionPlan.premium: {
        'id': 'premium',
        'name': 'Premium Plan',
        'price': 30.0,
        'chatCredits': 20000,
        'voiceCredits': 300,
        'durationDays': 30,
        'description': 'Best value for power users',
        'features': ['20,000 chat messages', '300 voice messages', 'Chat History', '30-days access', 'Breathing Exercise'],
        'popular': false,
        'color': 0xFFFF9800,
        'available': true,
      },
    };
    
    debugPrint('DynamicPlanService: Loaded fallback configuration');
  }

  /// Get current plan configuration
  Map<String, dynamic> getPlanConfig(SubscriptionPlan plan) {
    if (!_isInitialized && !_isLoading) {
      // Auto-initialize if not done yet
      initialize();
    }
    
    return _currentConfigs[plan] ?? _getFallbackPlanConfig(plan);
  }

  /// Get fallback configuration for a specific plan
  Map<String, dynamic> _getFallbackPlanConfig(SubscriptionPlan plan) {
    switch (plan) {
      case SubscriptionPlan.free:
        return {
          'id': 'free',
          'name': 'Free Plan',
          'price': 0.0,
          'chatCredits': 25,
          'voiceCredits': 5,
          'durationDays': 7,
          'description': 'Perfect for trying out the app',
          'features': ['25 chat messages', '5 voice messages', '7-day access'],
          'popular': false,
          'color': 0xFF9E9E9E,
          'available': true,
        };
      case SubscriptionPlan.basic:
        return {
          'id': 'basic',
          'name': 'Basic Plan',
          'price': 15.0,
          'chatCredits': 13000,
          'voiceCredits': 80,
          'durationDays': 30,
          'description': 'Great for regular users',
          'features': ['13,000 chat messages', '80 voice messages', 'Chat history', '30-day access'],
          'popular': true,
          'color': 0xFF2196F3,
          'available': true,
        };
      case SubscriptionPlan.premium:
        return {
          'id': 'premium',
          'name': 'Premium Plan',
          'price': 30.0,
          'chatCredits': 20000,
          'voiceCredits': 300,
          'durationDays': 30,
          'description': 'Best value for power users',
          'features': ['20,000 chat messages', '300 voice messages', 'Chat History', '30-days access'],
          'popular': false,
          'color': 0xFFFF9800,
          'available': true,
        };
    }
  }

  /// Get all current plan configurations
  Map<SubscriptionPlan, Map<String, dynamic>> getAllPlanConfigs() {
    if (_currentConfigs.isEmpty) {
      _loadFallbackConfiguration();
    }
    return Map.unmodifiable(_currentConfigs);
  }

  /// Stream of plan configuration updates
  Stream<Map<SubscriptionPlan, Map<String, dynamic>>> get planConfigStream => 
      _planConfigController.stream;

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Dispose resources
  void dispose() {
    _configSubscription?.cancel();
    _planConfigController.close();
    _isInitialized = false;
  }

  /// Force refresh configuration from Firestore
  Future<void> refresh() async {
    try {
      await _loadInitialConfiguration();
      _planConfigController.add(_currentConfigs);
    } catch (e) {
      debugPrint('DynamicPlanService: Refresh failed: $e');
    }
  }

  /// Update existing user subscriptions when plan configurations change
  Future<void> updateExistingUserSubscriptions(Map<SubscriptionPlan, Map<String, dynamic>> oldConfigs) async {
    try {
      debugPrint('DynamicPlanService: Updating existing user subscriptions...');

      // Get all active subscriptions
      final subscriptionsQuery = await _firestore
          .collection('user_subscriptions')
          .where('isActive', isEqualTo: true)
          .get();

      final batch = _firestore.batch();
      int updateCount = 0;

      for (final doc in subscriptionsQuery.docs) {
        final data = doc.data();
        final planName = data['plan'] as String?;

        if (planName != null) {
          final plan = SubscriptionPlan.values.firstWhere(
            (p) => p.name == planName,
            orElse: () => SubscriptionPlan.free,
          );

          final newConfig = _currentConfigs[plan];
          final oldConfig = oldConfigs[plan];

          if (newConfig != null && oldConfig != null) {
            // Check if credits or duration changed
            final oldChatCredits = oldConfig['chatCredits'] ?? 0;
            final newChatCredits = newConfig['chatCredits'] ?? 0;
            final oldVoiceCredits = oldConfig['voiceCredits'] ?? 0;
            final newVoiceCredits = newConfig['voiceCredits'] ?? 0;
            final oldDuration = oldConfig['durationDays'] ?? 0;
            final newDuration = newConfig['durationDays'] ?? 0;

            bool needsUpdate = false;
            final updates = <String, dynamic>{};

            // Update credit limits if they changed
            if (oldChatCredits != newChatCredits) {
              updates['chatCreditsLimit'] = newChatCredits;
              needsUpdate = true;
            }

            if (oldVoiceCredits != newVoiceCredits) {
              updates['voiceCreditsLimit'] = newVoiceCredits;
              needsUpdate = true;
            }

            // Update duration if it changed (extend subscription if beneficial)
            if (oldDuration != newDuration && newDuration > oldDuration) {
              final currentEndDate = (data['endDate'] as Timestamp?)?.toDate();
              if (currentEndDate != null) {
                final additionalDays = newDuration - oldDuration;
                final newEndDate = currentEndDate.add(Duration(days: additionalDays));
                updates['endDate'] = Timestamp.fromDate(newEndDate);
                needsUpdate = true;
              }
            }

            if (needsUpdate) {
              updates['lastPlanUpdate'] = FieldValue.serverTimestamp();
              updates['planUpdateReason'] = 'admin_configuration_change';

              batch.update(doc.reference, updates);
              updateCount++;
            }
          }
        }
      }

      if (updateCount > 0) {
        await batch.commit();
        debugPrint('DynamicPlanService: Updated $updateCount user subscriptions');
      } else {
        debugPrint('DynamicPlanService: No user subscriptions needed updates');
      }

    } catch (e) {
      debugPrint('DynamicPlanService: Failed to update user subscriptions: $e');
    }
  }

  /// Get configuration changes between old and new configs
  Map<String, dynamic> getConfigurationChanges(
    Map<SubscriptionPlan, Map<String, dynamic>> oldConfigs,
    Map<SubscriptionPlan, Map<String, dynamic>> newConfigs,
  ) {
    final changes = <String, dynamic>{};

    for (final plan in SubscriptionPlan.values) {
      final oldConfig = oldConfigs[plan];
      final newConfig = newConfigs[plan];

      if (oldConfig != null && newConfig != null) {
        final planChanges = <String, dynamic>{};

        // Check each configuration field
        for (final key in ['name', 'price', 'chatCredits', 'voiceCredits', 'durationDays', 'description', 'available']) {
          final oldValue = oldConfig[key];
          final newValue = newConfig[key];

          if (oldValue != newValue) {
            planChanges[key] = {
              'old': oldValue,
              'new': newValue,
            };
          }
        }

        if (planChanges.isNotEmpty) {
          changes[plan.name] = planChanges;
        }
      }
    }

    return changes;
  }
}
