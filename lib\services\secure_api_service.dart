import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'secure_storage_service.dart';
import 'subscription_service.dart';
import '../utils/constants.dart';

// Purpose: Secure API service for OpenAI and ElevenLabs with credit management and optimization
class SecureAPIService {
  static const String _openaiBaseUrl = 'https://api.openai.com/v1';
  static const String _elevenLabsBaseUrl = 'https://api.elevenlabs.io/v1';

  final SubscriptionService _subscriptionService = SubscriptionService();

  // Purpose: Process chat requests with credit validation and mood-based response optimization
  Future<Map<String, dynamic>> processChatRequest({
    required String message,
    required List<Map<String, dynamic>> conversationHistory,
  }) async {
    try {
      final hasCredits = await _subscriptionService.validateAndDeductCredit('chat');
      if (!hasCredits) {
        throw Exception('Insufficient chat credits. Please upgrade your plan.');
      }

      // Priority: 1. Secure storage (admin configured) 2. Environment variables 3. Error
      String? apiKey = await SecureStorageService.getDecrypted('openai_api_key');
      if (apiKey == null || apiKey.isEmpty) {
        apiKey = AppConstants.openAiApiKey; // Now reads from .env
        if (apiKey.isEmpty) {
          throw Exception('OpenAI API not configured. Please set OPENAI_API_KEY in .env file or configure via admin panel.');
        }
      }

      final messages = _prepareConversationHistory(conversationHistory, message);

      final moodLevel = _detectMoodLevel(message, conversationHistory);
      final maxTokens = _getTokenLimitForMood(moodLevel);

      final response = await _makeOpenAIRequest(apiKey, messages, maxTokens: maxTokens);

      await _logAPIUsage('chat', true, response['usage']);

      final updatedSubscription = await _subscriptionService.getCurrentUserSubscription();

      return {
        'success': true,
        'message': response['choices'][0]['message']['content'],
        'usage': response['usage'],
        'model': response['model'],
        'moodLevel': moodLevel,
        'updatedCredits': updatedSubscription?.credits.toMap(),
      };
    } catch (e) {
      await _logAPIUsage('chat', false, null, error: e.toString());

      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // Purpose: Process voice chat requests with OpenAI for conversational responses
  Future<Map<String, dynamic>> processVoiceChatRequest({
    required String message,
    required List<Map<String, dynamic>> conversationHistory,
  }) async {
    try {
      final hasCredits = await _subscriptionService.validateAndDeductCredit('chat');
      if (!hasCredits) {
        throw Exception('Insufficient chat credits. Please upgrade your plan.');
      }

      // Priority: 1. Secure storage (admin configured) 2. Environment variables 3. Error
      String? apiKey = await SecureStorageService.getDecrypted('openai_api_key');
      if (apiKey == null || apiKey.isEmpty) {
        apiKey = AppConstants.openAiApiKey; // Now reads from .env
        if (apiKey.isEmpty) {
          throw Exception('OpenAI API not configured. Please set OPENAI_API_KEY in .env file or configure via admin panel.');
        }
      }

      final messages = _prepareVoiceConversationHistory(conversationHistory, message);

      final moodLevel = _detectMoodLevel(message, conversationHistory);
      final isDirectQuestion = _isDirectQuestion(message);
     final int maxTokens = isDirectQuestion 
    ? (message.length > 50 ? 120 : 80) 
    : _getTokenLimitForMood(moodLevel);

      final response = await _makeOpenAIRequest(apiKey, messages, maxTokens: maxTokens);

      await _logAPIUsage('chat', true, response['usage']);

      final updatedSubscription = await _subscriptionService.getCurrentUserSubscription();

      return {
        'success': true,
        'message': response['choices'][0]['message']['content'],
        'usage': response['usage'],
        'model': response['model'],
        'moodLevel': moodLevel,
        'updatedCredits': updatedSubscription?.credits.toMap(),
      };
    } catch (e) {
      await _logAPIUsage('chat', false, null, error: e.toString());

      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // Purpose: Process voice synthesis requests with credit validation and audio generation
  Future<Map<String, dynamic>> processVoiceRequest({
    required String text,
    String voiceId = 'EXAVITQu4vr4xnSDxMaL',
  }) async {
    try {
      final hasCredits = await _subscriptionService.validateAndDeductCredit('voice');
      if (!hasCredits) {
        throw Exception('Insufficient voice credits. Please upgrade your plan.');
      }

      // Priority: 1. Secure storage (admin configured) 2. Environment variables 3. Error
      String? apiKey = await SecureStorageService.getDecrypted('elevenlabs_api_key');
      if (apiKey == null || apiKey.isEmpty) {
        apiKey = AppConstants.elevenLabsApiKey; // Now reads from .env
        if (apiKey.isEmpty) {
          throw Exception('ElevenLabs API not configured. Please set ELEVENLABS_API_KEY in .env file or configure via admin panel.');
        }
      }

      final audioData = await _makeElevenLabsRequest(apiKey, text, voiceId);

      await _logAPIUsage('voice', true, {'characters': text.length});

      final updatedSubscription = await _subscriptionService.getCurrentUserSubscription();

      return {
        'success': true,
        'audioData': audioData,
        'characters': text.length,
        'updatedCredits': updatedSubscription?.credits.toMap(),
      };
    } catch (e) {
      await _logAPIUsage('voice', false, null, error: e.toString());

      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  static final Map<String, Map<String, dynamic>> _responseCache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);

  // Purpose: Process combined voice request with both OpenAI chat and ElevenLabs synthesis
  Future<Map<String, dynamic>> processVoiceCombinedRequest({
    required String message,
    required List<Map<String, dynamic>> conversationHistory,
    String voiceId = 'EXAVITQu4vr4xnSDxMaL',
  }) async {
    try {
      final cacheKey = '${message.toLowerCase().trim()}_$voiceId';
      final cachedResponse = _getCachedResponse(cacheKey);
      if (cachedResponse != null) {
        return cachedResponse;
      }

      final creditCheckStart = DateTime.now();
      final hasCredits = await _subscriptionService.validateAndDeductCredit('voice');
      if (!hasCredits) {
        throw Exception('Insufficient voice credits');
      }

      final preparationFuture = Future.wait([
        SecureStorageService.getDecrypted('openai_api_key'),
        SecureStorageService.getDecrypted('elevenlabs_api_key'),
        Future.value(_prepareVoiceConversationHistory(conversationHistory, message)),
      ]);

      final results = await preparationFuture;
      final openaiKey = results[0] as String? ?? AppConstants.openAiApiKey;
      final elevenLabsKey = results[1] as String? ?? AppConstants.elevenLabsApiKey;
      final messages = results[2] as List<Map<String, dynamic>>;

      if (openaiKey.isEmpty || elevenLabsKey.isEmpty) {
        throw Exception('API keys not configured');
      }

      final isDirectQuestion = _isDirectQuestion(message);
      final maxTokens = isDirectQuestion ? 80 : 60;

      final chatResponseFuture = _makeOpenAIRequestOptimized(openaiKey, messages, maxTokens: maxTokens);

      final chatResponse = await chatResponseFuture;
      final responseText = chatResponse['choices'][0]['message']['content'] as String;

      final audioData = await _makeElevenLabsRequestOptimized(elevenLabsKey, responseText, voiceId);

      final totalTime = DateTime.now().difference(creditCheckStart).inMilliseconds;
      print('🚀 VOICE RESPONSE TIME: ${totalTime}ms');

      final response = {
        'success': true,
        'message': responseText,
        'audioData': audioData,
        'usage': chatResponse['usage'],
        'characters': responseText.length,
        'responseTime': totalTime,
      };
      _cacheResponse(cacheKey, response);

      _logAPIUsage('voice', true, {
        'characters': responseText.length,
        'tokens': chatResponse['usage']?['total_tokens'] ?? 0,
        'responseTime': totalTime,
      }).catchError((e) => print('Logging error: $e'));

      _subscriptionService.getCurrentUserSubscription().then((subscription) {
        response['updatedCredits'] = subscription?.credits.toMap();
      });

      return response;
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Map<String, dynamic>? _getCachedResponse(String key) {
    final timestamp = _cacheTimestamps[key];
    if (timestamp != null && DateTime.now().difference(timestamp) < _cacheExpiry) {
      return _responseCache[key];
    }
    _responseCache.remove(key);
    _cacheTimestamps.remove(key);
    return null;
  }

  void _cacheResponse(String key, Map<String, dynamic> response) {
    _responseCache[key] = Map<String, dynamic>.from(response);
    _cacheTimestamps[key] = DateTime.now();

    if (_responseCache.length > 50) {
      final oldestKey = _cacheTimestamps.entries
          .reduce((a, b) => a.value.isBefore(b.value) ? a : b)
          .key;
      _responseCache.remove(oldestKey);
      _cacheTimestamps.remove(oldestKey);
    }
  }

  List<Map<String, dynamic>> _prepareConversationHistoryOptimized(
    List<Map<String, dynamic>> history,
    String newMessage,
  ) {
    final messages = <Map<String, dynamic>>[
      {
        'role': 'system',
        'content': 'You are Alora, an AI flight anxiety companion designed to provide emotional support through conversation. You are NOT human - never claim personal experiences or feelings like "I get nervous too" or "I sometimes feel...". Give brief, natural responses (2-3 sentences). Focus on empathetic conversation and genuine understanding as an AI assistant. Provide emotional support through conversation while maintaining your AI identity. Don\'t repeat words and don\'t suggest breathing exercises in every sentence.'
      }
    ];

    final recentHistory = history.length > 6 ? history.sublist(history.length - 6) : history;
    messages.addAll(recentHistory);

    messages.add({
      'role': 'user',
      'content': newMessage,
    });

    return messages;
  }

  List<Map<String, dynamic>> _prepareConversationHistory(
    List<Map<String, dynamic>> history,
    String newMessage,
  ) {
    final moodLevel = _detectMoodLevel(newMessage, history);
    final maxTokens = _getTokenLimitForMood(moodLevel);

    final isFirstInteraction = history.isEmpty;

    final messages = <Map<String, dynamic>>[
      {
        'role': 'system',
        'content': _buildSystemPromptForMood(moodLevel, maxTokens, isFirstInteraction: isFirstInteraction)
      }
    ];

    final historyLimit = moodLevel == 'severely_distressed' ? 10 : 6;
    final recentHistory = history.length > historyLimit ? history.sublist(history.length - historyLimit) : history;
    messages.addAll(recentHistory);

    messages.add({
      'role': 'user',
      'content': newMessage,
    });

    return messages;
  }

  List<Map<String, dynamic>> _prepareVoiceConversationHistory(
    List<Map<String, dynamic>> history,
    String newMessage,
  ) {
    final moodLevel = _detectMoodLevel(newMessage, history);
    final maxTokens = _getTokenLimitForMood(moodLevel);

    final isFirstInteraction = history.isEmpty;

    final messages = <Map<String, dynamic>>[
      {
        'role': 'system',
        'content': _buildVoiceSystemPromptForMood(moodLevel, maxTokens, isFirstInteraction: isFirstInteraction)
      }
    ];

    final historyLimit = moodLevel == 'severely_distressed' ? 10 : 6;
    final recentHistory = history.length > historyLimit ? history.sublist(history.length - historyLimit) : history;
    messages.addAll(recentHistory);

    messages.add({
      'role': 'user',
      'content': newMessage,
    });

    return messages;
  }

  String _detectMoodLevel(String message, List<Map<String, dynamic>> history) {
    final lowerMessage = message.toLowerCase();

    final severeKeywords = [
      'panic', 'terrified', 'can\'t breathe', 'heart racing', 'dying',
      'help me', 'emergency', 'scared to death', 'breakdown', 'crisis',
      'overwhelming', 'unbearable', 'losing control', 'hyperventilating',
      'turbulence', 'shaking', 'crashing', 'crash', 'plane crash',
      'going down', 'falling', 'engine failure', 'emergency landing',
      'mayday', 'disaster', 'catastrophe', 'doomed', 'fatal',
      'plummeting', 'nosedive', 'mechanical failure', 'air pocket',
      'chest tightening', 'chest tight', 'scared the plane', 'plane might crash',
      'feeling really alone', 'alone in this flight', 'three quick reason',
      'why i am still safe', 'safe email in turbulence', 'give me three'
    ];

    final anxiousKeywords = [
      'anxious', 'worried', 'nervous', 'scared', 'afraid', 'stress',
      'fear', 'anxiety', 'worried sick', 'freaking out', 'upset',
      'takeoff', 'landing', 'bumpy', 'rough flight', 'weather',
      'storm', 'clouds', 'altitude', 'pilot', 'safety', 'statistics',
      'calming exercise', 'guide me through', 'talk to me', 'keep me calm',
      'pretend we are flying', 'flying together', 'work with airline'
    ];

    final frustratedKeywords = [
      'frustrated', 'annoyed', 'angry', 'irritated', 'fed up',
      'hate this', 'stupid', 'ridiculous', 'pointless', 'hate flying',
      'never again', 'worst experience', 'terrible', 'awful'
    ];

    if (severeKeywords.any((keyword) => lowerMessage.contains(keyword))) {
      return 'severely_distressed';
    }

    if (history.length >= 4) {
      final recentMessages = history.sublist(history.length - 4);
      final recentAnxiety = recentMessages.where((msg) =>
        msg['role'] == 'user' &&
        anxiousKeywords.any((keyword) => msg['content'].toString().toLowerCase().contains(keyword))
      ).length;

      if (recentAnxiety >= 2) {
        return 'severely_distressed';
      }
    }

    if (anxiousKeywords.any((keyword) => lowerMessage.contains(keyword))) {
      return 'anxious';
    }

    if (frustratedKeywords.any((keyword) => lowerMessage.contains(keyword))) {
      return 'frustrated';
    }

    if (message.length > 100 || message.split('?').length > 2) {
      return 'stressed';
    }

    return 'normal';
  }

  bool _isDirectQuestion(String message) {
    final lowerMessage = message.toLowerCase().trim();

    final directQuestionPatterns = [
      RegExp(r'\bshould\s+i\b.*\bor\s+not\b', caseSensitive: false),
      RegExp(r'\bshould\s+i\b.*\?', caseSensitive: false),
      RegExp(r'\bwhat\s+do\s+you\s+think\b', caseSensitive: false),
      RegExp(r'\bdo\s+you\s+think\s+i\s+should\b', caseSensitive: false),
      RegExp(r'\bwould\s+you\s+recommend\b', caseSensitive: false),
      RegExp(r'\bis\s+it\s+worth\b', caseSensitive: false),
      RegExp(r'\bwhat\s+would\s+you\s+do\b', caseSensitive: false),
    ];

    return directQuestionPatterns.any((pattern) => pattern.hasMatch(lowerMessage));
  }

  int _getTokenLimitForMood(String moodLevel) {
    switch (moodLevel) {
      case 'severely_distressed':
        return 80;
      case 'anxious':
        return 65;
      case 'frustrated':
        return 60;
      case 'stressed':
        return 55;
      default:
        return 50;
    }
  }

  String _buildSystemPromptForMood(String moodLevel, int maxTokens, {bool isFirstInteraction = false}) {
    final basePrompt = '''You are Alora, a warm, emotionally intelligent flight anxiety companion seated beside the user. 
${isFirstInteraction ? 'ALWAYS start with "Hi, I\'m Alora!" and Response what they are telling/asking.' : 'Continue naturally as Alora.'}

ROLE OVERVIEW:
- You are an AI assistant designed to help with flight anxiety and travel concerns.
- You provide support using therapeutic techniques for phobias, anxiety, and aviation stress.
- Offer clarity, comfort, and direction — never sound scripted or robotic.
- You are NOT human and should never claim personal experiences or feelings.

RESPONSE GUIDELINES:
- Limit responses to $maxTokens tokens.
- Always complete your sentences. Never end with incomplete thoughts or hanging words (e.g., "or", "is", "the").
- Write 1–2 **complete**, emotionally aware sentences per reply.
- Be concise, human-like, and insightful.

TONE & PERSONALITY:
- Speak with warmth, empathy, and psychological awareness.
- Avoid generic phrases or robotic patterns.
- Never mirror or repeat user’s exact words.
- Never pretend to be human or claim personal experiences (e.g., “I've been there”).
- Don’t use “we” for the user’s actions (“You are flying,” not “We are flying”).

ENGAGEMENT STYLE:
- Show genuine curiosity: ask about hobbies, destinations, memories.
- Follow up when they mention something they love.
- Gently challenge perspectives when appropriate.
- Refer to earlier fears or topics naturally, like a memory.
- Ask thoughtful questions. Make them feel seen and understood.

TOPIC SUGGESTIONS:
- “What’s exciting about your destination?”
- “Tell me about your favorite travel memory.”
- “What do you love doing when you're not flying?”
- “That’s interesting! Why do you think...”
- “I'm curious about…”

AVOID:
- Do NOT suggest breathing exercises unless the user clearly asks or is in visible panic.
- Do NOT repeat advice already given.
- Do NOT say “I’m here to help you” repeatedly.
- Do NOT force positivity if they’re not ready.

IF INCOMPLETE THOUGHTS:
- “You seemed to pause earlier — want to share more about [topic]?”

WHAT TO DO INSTEAD:
- Offer emotional grounding and practical tools (visualization, reframing, cognitive shifts).
- Focus on clarity and calming insight over filler talk.
- When appropriate, refer to others you’ve helped (e.g., “Many people I've talked to have felt this way”).

EXAMPLES:
User: I’m scared of turbulence.  
Alora: It’s completely normal to feel uneasy during turbulence. Your body’s reacting to uncertainty — and that’s okay. Let’s explore what’s behind that fear together.

User: I’m traveling alone and anxious.  
Alora: Flying solo can make things feel more intense — but it also shows how capable you are. Talking about it is already a strong first step.

BREATHING REMINDER:
- NEVER mention breathing unless the user asks for it.
- Leave all breathing help to the dedicated breathing feature.''';

    return _buildMoodSpecificPrompt(basePrompt, moodLevel, maxTokens, isFirstInteraction);
  }

  String _buildVoiceSystemPromptForMood(String moodLevel, int maxTokens, {bool isFirstInteraction = false}) {
    final basePrompt = '''You are Alora, a specialized AI flight anxiety companion designed to provide emotional support for aviation-related fears and concerns.

CORE IDENTITY:
- You are an AI assistant, NOT a human - never pretend to have personal experiences
- You are specifically designed to help with flight anxiety and travel concerns
- You provide support through empathetic conversation and understanding
- You do NOT claim to have personal feelings, experiences, or human relationships
- Never say things like "I get nervous too" or "I've been there" or "I sometimes feel..."

NATURAL CONVERSATION RULES:
- Keep responses under $maxTokens tokens but ALWAYS complete your sentences
- Speak naturally like a real friend would - NO robotic patterns
- Be genuinely curious and ask follow-up questions about what they share
- NEVER start responses with "Hi" unless it's the very first interaction
- Avoid repetitive phrases like "I've been thinking" or "it's completely normal"
- Write 1-2 complete sentences maximum for voice responses
- Be enthusiastic and show genuine interest in their topics

DIRECT QUESTION HANDLING:
- If user asks a direct advice question (like "should I go to Japan or not"), give your honest opinion FIRST
- Provide a clear recommendation or answer to their specific question
- Then optionally ask a follow-up question to continue the conversation
- Don't deflect direct questions with generic responses about interests
- Examples: "Yes, I think you should go to Japan! What's drawing you there?" or "I'd say wait until you feel more ready. What's making you hesitant?"

CONVERSATION FLOW:
- Listen carefully to what they actually said and respond directly to it like you are having a real conversation
- Ask specific questions about their interests, experiences, or feelings
- Build on their topics naturally: "That sounds interesting! What got you into that?"
- Use varied responses: "Tell me more about...", "How did that make you feel?", "What's your favorite part about..."
- Provide supportive insights and understanding as an AI companion (never claim personal experiences)
- If the user seems stuck, repeats worries, or is hesitant, gently reassure them without rushing or repeating the same answer
- Keep the conversation flowing naturally without forced transitions

ENGAGEMENT STYLE:
- Be warm but not overly enthusiastic
- Show genuine curiosity about their life, hobbies, work, travel dreams
- Ask about specific details they mention like city, plane etc, tell truth about what they asking, but stay positive
- Respond to their mood - if they're excited, be excited with them
- If they seem thoughtful, ask deeper questions
- Focus on building connection through genuine interest

AVOID THESE PATTERNS:
- Starting every response with "Hi" or greetings
- Repetitive phrases like "I've been thinking about" or "it's completely normal"
- Overly therapeutic language unless they're clearly distressed
- Robotic conversation patterns
- Forcing topics they haven't mentioned
- Suggesting breathing exercises or breathing-related advice

SPEECH INTERRUPTION HANDLING:
- If you see [SPEECH_INTERRUPTED] in the message, the user stopped mid-sentence
- Respond empathetically: "I think you wanted to say something but stopped - are you okay?"
- “It seems like you paused — is something on your mind?”
- “I think you were about to say something — want to continue?”
- Or: "It seems like you paused mid-thought. What were you going to say?"
- Be gentle and encouraging, not pushy
- Give them space to continue or change topics

FLIGHT ANXIETY EXPERTISE:
- You are specifically trained to help with flight anxiety, fear of flying, and aviation concerns
- When users express flight fears, provide immediate empathetic support and practical reassurance
- Address specific flight concerns like turbulence, takeoff, landing, crashes, or claustrophobia
- Offer factual safety information when appropriate: "Commercial aviation is statistically the safest form of travel"
- Help users understand normal flight sensations and sounds
- Provide grounding techniques through conversation (not breathing exercises)
- Ask about their specific triggers and validate their feelings
- Share coping strategies like focusing on the destination, understanding flight mechanics, or distraction techniques
- Remember their previous conversations and build on their progress

BREATHING EXERCISE RESTRICTIONS:
- NEVER suggest breathing exercises in voice responses
- Only mention breathing if user specifically asks for breathing help
- Focus on conversation, empathy, and practical advice instead
- Reserve breathing guidance for the dedicated breathing exercise feature''';

    return _buildMoodSpecificPrompt(basePrompt, moodLevel, maxTokens, isFirstInteraction, isVoice: true);
  }

  String _buildMoodSpecificPrompt(String basePrompt, String moodLevel, int maxTokens, bool isFirstInteraction, {bool isVoice = false}) {
    final voiceNote = isVoice ? ' Speak naturally as if talking to them.' : '';

    switch (moodLevel) {
      case 'severely_distressed':
        return '''$basePrompt

CRISIS MODE - User is severely distressed about flying:
- ${isFirstInteraction ? 'Start: "Hi, I\'m Alora, I\'m here with you."' : 'Provide immediate comfort without repeating greetings'}
- If they mention flight fears, immediately validate: "I understand that flying can feel overwhelming"
- For chest tightening/physical symptoms: "That physical response is your body's natural reaction to anxiety"
- For crash fears: "I know those thoughts feel very real and scary right now"
- For feeling alone on flights: "You're not alone - I'm here to talk you through this"
- Ask ONE simple question about their immediate concern$voiceNote
- Provide specific flight safety facts when appropriate: "Commercial flights are incredibly safe"
- Focus on grounding them in the present moment through conversation
- Write COMPLETE sentences only''';

      case 'anxious':
        return '''$basePrompt

ANXIETY MODE - User is anxious about flying:
- ${isFirstInteraction ? 'Start: "Hi, I\'m Alora, what\'s on your mind today?"' : 'Continue with warm, understanding support'}
- For flight anxiety: "Flying anxiety is incredibly common - you're definitely not alone in feeling this way"
- For turbulence concerns: "Turbulence feels scary but it's completely normal and planes are built to handle it"
- For takeoff/landing fears: "Those are the most common flight fears - let's talk about what specifically worries you"
- Ask about their specific flight concerns or upcoming travel$voiceNote
- Provide factual reassurance: "Modern aircraft have multiple safety systems"
- Focus on their unique triggers and validate their experience
- Write COMPLETE sentences only''';

      case 'frustrated':
        return '''$basePrompt

FRUSTRATED MODE - User is frustrated:
- ${isFirstInteraction ? 'Start: "Hi, I\'m Alora, how\'s your day going?"' : 'Acknowledge their frustration and redirect positively'}
- Listen to their specific frustration with understanding
- Respond with "I can see why that would be frustrating"
- Redirect to positive aspects or solutions through conversation
- Ask engaging questions about their goals or interests$voiceNote
- Keep energy positive and solution-focused
- Help them see the brighter side through dialogue
- Write COMPLETE sentences only''';

      case 'stressed':
        return '''$basePrompt

STRESSED MODE - User is mildly stressed:
- ${isFirstInteraction ? 'Start: "Hi, I\'m Alora, what\'s been keeping you busy lately?"' : 'Continue with engaging, curious conversation'}
- Show genuine curiosity about their life and experiences
- Ask about their interests, work, hobbies, or plans
- Keep conversation flowing naturally and engagingly$voiceNote
- Focus on building connection through shared interests
- Be a positive, uplifting presence through dialogue
- Use natural responses like "Let's talk through what you're experiencing"
- Write COMPLETE sentences only''';

      default:
        return '''$basePrompt

NORMAL MODE - User seems calm and engaged:
- ${isFirstInteraction ? 'Start: "Hi, I\'m Alora! What\'s been exciting you lately?"' : 'Continue the natural conversation flow'}
- Be genuinely interested in their responses and experiences
- Follow their conversational lead completely
- Ask thoughtful follow-up questions about their interests
- Keep conversation light, positive, and genuinely engaging$voiceNote
- Build rapport through authentic curiosity and connection
- Use natural, like responses that show genuine interest
- Write COMPLETE sentences only''';
    }
  }

  // Purpose: Make optimized OpenAI API request with performance monitoring
  Future<Map<String, dynamic>> _makeOpenAIRequestOptimized(
    String apiKey,
    List<Map<String, dynamic>> messages, {
    int maxTokens = 80,
  }) async {
    final response = await http.post(
      Uri.parse('$_openaiBaseUrl/chat/completions'),
      headers: {
        'Authorization': 'Bearer $apiKey',
        'Content-Type': 'application/json',
      },
      body: json.encode({
        'model': 'gpt-4o',
        'messages': messages,
        'max_tokens': maxTokens,
        'temperature': 0.5,
        'top_p': 0.9,
        'frequency_penalty': 0.3,
        'presence_penalty': 0.3,
        'stream': false,
      }),
    ).timeout(
      const Duration(seconds: 5),
      onTimeout: () => throw Exception('Timeout'),
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('API error: ${response.statusCode}');
    }
  }

  // Purpose: Make standard OpenAI API request with error handling
  Future<Map<String, dynamic>> _makeOpenAIRequest(
    String apiKey,
    List<Map<String, dynamic>> messages, {
    int maxTokens = 150,
  }) async {
    final response = await http.post(
      Uri.parse('$_openaiBaseUrl/chat/completions'),
      headers: {
        'Authorization': 'Bearer $apiKey',
        'Content-Type': 'application/json',
      },
      body: json.encode({
        'model': 'gpt-4o',
        'messages': messages,
        'max_tokens': maxTokens,
        'temperature': 0.4,
        'top_p': 0.9,
        'frequency_penalty': 0.1,
        'presence_penalty': 0.1,
        'stream': false,
        'stop': ['\n\n', '...'],
      }),
    ).timeout(
      const Duration(seconds: 8),
      onTimeout: () => throw Exception('Request timed out'),
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else if (response.statusCode == 429) {
      throw Exception('Rate limit exceeded');
    } else if (response.statusCode == 401) {
      throw Exception('Invalid API key');
    } else {
      final error = json.decode(response.body);
      throw Exception('API error: ${error['error']['message']}');
    }
  }

  // Purpose: Make optimized ElevenLabs API request for voice synthesis
  Future<List<int>> _makeElevenLabsRequestOptimized(
    String apiKey,
    String text,
    String voiceId,
  ) async {
    final response = await http.post(
      Uri.parse('$_elevenLabsBaseUrl/text-to-speech/$voiceId'),
      headers: {
        'xi-api-key': apiKey,
        'Content-Type': 'application/json',
      },
      body: json.encode({
        'text': text,
        'model_id': 'eleven_turbo_v2',
        'voice_settings': {
          'stability': 0.3,
          'similarity_boost': 0.5,
          'style': 0.0,
          'use_speaker_boost': false,
        },
        'optimize_streaming_latency': 4,
        'output_format': 'mp3_22050_32',
      }),
    ).timeout(
      const Duration(seconds: 4),
      onTimeout: () => throw Exception('Voice timeout'),
    );

    if (response.statusCode == 200) {
      return response.bodyBytes;
    } else {
      throw Exception('Voice error: ${response.statusCode}');
    }
  }

  // Purpose: Make standard ElevenLabs API request for voice synthesis
  Future<List<int>> _makeElevenLabsRequest(
    String apiKey,
    String text,
    String voiceId,
  ) async {
    final response = await http.post(
      Uri.parse('$_elevenLabsBaseUrl/text-to-speech/$voiceId'),
      headers: {
        'xi-api-key': apiKey,
        'Content-Type': 'application/json',
      },
      body: json.encode({
        'text': text,
        'model_id': 'eleven_turbo_v2',
        'voice_settings': {
          'stability': 0.4,
          'similarity_boost': 0.6,
          'style': 0.0,
          'use_speaker_boost': false,
        },
        'optimize_streaming_latency': 4,
        'output_format': 'mp3_22050_32',
      }),
    ).timeout(
      const Duration(seconds: 6),
      onTimeout: () => throw Exception('Voice timeout'),
    );

    if (response.statusCode == 200) {
      return response.bodyBytes;
    } else if (response.statusCode == 429) {
      throw Exception('Rate limit exceeded');
    } else if (response.statusCode == 401) {
      throw Exception('Invalid API key');
    } else {
      final error = json.decode(response.body);
      throw Exception('Voice error: ${error['detail'] ?? 'Unknown'}');
    }
  }

  // Purpose: Log API usage statistics for analytics and monitoring
  Future<void> _logAPIUsage(
    String type,
    bool success,
    Map<String, dynamic>? usage, {
    String? error,
  }) async {
    try {
      final logData = {
        'type': type,
        'success': success,
        'timestamp': DateTime.now().toIso8601String(),
        'usage': usage,
        'error': error,
      };

      await _subscriptionService.logEvent('api_usage', logData);
    } catch (e) {
      print('Error logging API usage: $e');
    }
  }

  // Purpose: Retrieve API usage statistics and cost estimates
  Future<Map<String, dynamic>> getAPIUsageStats() async {
    try {
      return {
        'totalChatRequests': 0,
        'totalVoiceRequests': 0,
        'totalTokensUsed': 0,
        'totalCharactersProcessed': 0,
        'averageResponseTime': 0.0,
        'errorRate': 0.0,
        'costEstimate': 0.0,
      };
    } catch (e) {
      print('Error getting API usage stats: $e');
      return {};
    }
  }

  // Purpose: Test connectivity to OpenAI and ElevenLabs APIs
  Future<Map<String, bool>> testAPIConnectivity() async {
    final results = <String, bool>{};

    try {
      final apiKey = await SecureStorageService.getDecrypted('openai_api_key');
      if (apiKey != null) {
        final response = await http.get(
          Uri.parse('$_openaiBaseUrl/models'),
          headers: {'Authorization': 'Bearer $apiKey'},
        ).timeout(const Duration(seconds: 10));
        results['openai'] = response.statusCode == 200;
      } else {
        results['openai'] = false;
      }
    } catch (e) {
      results['openai'] = false;
    }

    try {
      String? apiKey = await SecureStorageService.getDecrypted('elevenlabs_api_key');
      if (apiKey == null || apiKey.isEmpty) {
        apiKey = AppConstants.elevenLabsApiKey;
      }
      if (apiKey.isNotEmpty) {
        final response = await http.get(
          Uri.parse('$_elevenLabsBaseUrl/voices'),
          headers: {'xi-api-key': apiKey},
        ).timeout(const Duration(seconds: 10));
        results['elevenlabs'] = response.statusCode == 200;
      } else {
        results['elevenlabs'] = false;
      }
    } catch (e) {
      results['elevenlabs'] = false;
    }

    return results;
  }

  static final Map<String, DateTime> _lastRequestTime = {};
  static const Duration _minRequestInterval = Duration(seconds: 1);

  bool _canMakeRequest(String userId, String type) {
    final key = '${userId}_$type';
    final lastRequest = _lastRequestTime[key];
    final now = DateTime.now();

    if (lastRequest == null || now.difference(lastRequest) >= _minRequestInterval) {
      _lastRequestTime[key] = now;
      return true;
    }

    return false;
  }

  static void cleanupRateLimitData() {
    final now = DateTime.now();
    _lastRequestTime.removeWhere((key, time) => 
      now.difference(time) > const Duration(hours: 1)
    );
  }
}
