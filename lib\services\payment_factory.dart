import '../interfaces/payment_provider_interface.dart';
import '../models/payment_models.dart';
import 'providers/lemonsqueezy_provider.dart';
import 'providers/stripe_provider.dart' as stripe_impl;
import 'providers/paypal_provider.dart' as paypal_impl;

/// Factory class for creating payment provider instances.
///
/// This factory implements the Factory pattern to create appropriate
/// payment provider instances based on the PaymentProvider enum.
/// The actual implementations are in lib/services/providers/
class PaymentProviderFactory {
  /// Creates a payment provider instance based on the provider type
  static PaymentProviderInterface create(PaymentProvider provider) {
    switch (provider) {
      case PaymentProvider.lemonsqueezy:
        return LemonSqueezyProvider();
      case PaymentProvider.stripe:
        return stripe_impl.StripeProvider();
      case PaymentProvider.paypal:
        return paypal_impl.PayPalProvider();
    }
  }
}
