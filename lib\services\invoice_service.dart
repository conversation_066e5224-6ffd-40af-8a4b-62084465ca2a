import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/subscription_model.dart';
import '../models/user_model.dart';
import '../models/invoice_model.dart';

class InvoiceService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  static Future<InvoiceModel> generateInvoice({
    required UserModel user,
    required SubscriptionPlan plan,
    required String checkoutUrl,
  }) async {
    final now = DateTime.now();
    final invoiceNumber = _generateInvoiceNumber();
    
    final invoice = InvoiceModel(
      id: '',
      invoiceNumber: invoiceNumber,
      userId: user.id,
      userEmail: user.email,
      userName: user.name,
      plan: plan,
      amount: _getPlanPrice(plan),
      currency: 'USD',
      status: InvoiceStatus.pending,
      checkoutUrl: checkoutUrl,
      createdAt: now,
      dueDate: now.add(const Duration(days: 7)),
      billingPeriodStart: now,
      billingPeriodEnd: now.add(const Duration(days: 30)),
      items: [
        InvoiceItem(
          description: _getPlanDisplayName(plan),
          quantity: 1,
          unitPrice: _getPlanPrice(plan),
          totalPrice: _getPlanPrice(plan),
        ),
      ],
    );

    final docRef = await _firestore.collection('invoices').add(invoice.toMap());
    
    return invoice.copyWith(id: docRef.id);
  }

  static Future<InvoiceModel?> getInvoice(String invoiceId) async {
    try {
      final doc = await _firestore.collection('invoices').doc(invoiceId).get();
      
      if (!doc.exists) return null;
      
      final data = doc.data()!;
      return InvoiceModel.fromMap(data, doc.id);
    } catch (e) {
      print('Error getting invoice: $e');
      return null;
    }
  }

  static Future<List<InvoiceModel>> getUserInvoices(String userId) async {
    try {
      final query = await _firestore
          .collection('invoices')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return query.docs
          .map((doc) => InvoiceModel.fromMap(doc.data(), doc.id))
          .toList();
    } catch (e) {
      print('Error getting user invoices: $e');
      return [];
    }
  }

  static Future<void> updateInvoiceStatus({
    required String invoiceId,
    required InvoiceStatus status,
    String? paymentId,
    DateTime? paidAt,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'status': status.name,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      };

      if (paymentId != null) {
        updateData['paymentId'] = paymentId;
      }

      if (paidAt != null) {
        updateData['paidAt'] = Timestamp.fromDate(paidAt);
      }

      await _firestore.collection('invoices').doc(invoiceId).update(updateData);
    } catch (e) {
      print('Error updating invoice status: $e');
      rethrow;
    }
  }

  static Future<void> markInvoiceAsPaid({
    required String invoiceId,
    required String paymentId,
  }) async {
    await updateInvoiceStatus(
      invoiceId: invoiceId,
      status: InvoiceStatus.paid,
      paymentId: paymentId,
      paidAt: DateTime.now(),
    );
  }

  static Future<void> markInvoiceAsFailed(String invoiceId) async {
    await updateInvoiceStatus(
      invoiceId: invoiceId,
      status: InvoiceStatus.failed,
    );
  }

  static Future<void> markInvoiceAsCancelled(String invoiceId) async {
    await updateInvoiceStatus(
      invoiceId: invoiceId,
      status: InvoiceStatus.cancelled,
    );
  }

  static String _generateInvoiceNumber() {
    final now = DateTime.now();
    final year = now.year.toString();
    final month = now.month.toString().padLeft(2, '0');
    final day = now.day.toString().padLeft(2, '0');
    final timestamp = now.millisecondsSinceEpoch.toString().substring(8);
    
    return 'INV-$year$month$day-$timestamp';
  }

  static double _getPlanPrice(SubscriptionPlan plan) {
    switch (plan) {
      case SubscriptionPlan.free:
        return 0.0;
      case SubscriptionPlan.basic:
        return 15.0;
      case SubscriptionPlan.premium:
        return 30.0;
    }
  }

  static String _getPlanDisplayName(SubscriptionPlan plan) {
    switch (plan) {
      case SubscriptionPlan.free:
        return 'Free Plan';
      case SubscriptionPlan.basic:
        return 'Basic Plan - 30 Days';
      case SubscriptionPlan.premium:
        return 'Premium Plan - 30 Days';
    }
  }

  static double calculateTax(double amount, {String? country}) {
    return 0.0;
  }

  static Future<String?> generateInvoicePDF(InvoiceModel invoice) async {
    print('PDF generation not implemented yet for invoice ${invoice.invoiceNumber}');
    return null;
  }

  static Future<void> sendInvoiceEmail(InvoiceModel invoice) async {
    print('Email sending not implemented yet for invoice ${invoice.invoiceNumber}');
  }

  static Future<Map<String, dynamic>> getInvoiceStatistics() async {
    try {
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      
      final monthlyQuery = await _firestore
          .collection('invoices')
          .where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfMonth))
          .get();

      final monthlyInvoices = monthlyQuery.docs
          .map((doc) => InvoiceModel.fromMap(doc.data(), doc.id))
          .toList();

      final totalInvoices = monthlyInvoices.length;
      final paidInvoices = monthlyInvoices.where((i) => i.status == InvoiceStatus.paid).length;
      final pendingInvoices = monthlyInvoices.where((i) => i.status == InvoiceStatus.pending).length;
      final totalRevenue = monthlyInvoices
          .where((i) => i.status == InvoiceStatus.paid)
          .fold(0.0, (sum, invoice) => sum + invoice.amount);

      return {
        'totalInvoices': totalInvoices,
        'paidInvoices': paidInvoices,
        'pendingInvoices': pendingInvoices,
        'totalRevenue': totalRevenue,
        'conversionRate': totalInvoices > 0 ? (paidInvoices / totalInvoices) * 100 : 0.0,
      };
    } catch (e) {
      print('Error getting invoice statistics: $e');
      return {
        'totalInvoices': 0,
        'paidInvoices': 0,
        'pendingInvoices': 0,
        'totalRevenue': 0.0,
        'conversionRate': 0.0,
      };
    }
  }
}
