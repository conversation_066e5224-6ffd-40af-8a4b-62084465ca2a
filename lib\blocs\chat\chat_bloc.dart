import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:flight_fear_wellness_app/models/chat_message.dart';
import 'package:flight_fear_wellness_app/repositories/chat_repository.dart';
import 'package:flight_fear_wellness_app/services/crisis_intervention_service.dart';
import 'chat_event.dart';
import 'chat_state.dart';

class ChatBloc extends Bloc<ChatEvent, ChatState> {
  final ChatRepository chatRepository;
  StreamSubscription? _messagesSubscription;

  ChatBloc({required this.chatRepository}) : super(const ChatState()) {
    on<SendMessageEvent>(_onSendMessage);
    on<LoadChatHistoryEvent>(_onLoadChatHistory);
    on<ClearChatHistoryEvent>(_onClearChatHistory);
    on<GenerateAIResponseEvent>(_onGenerateAIResponse);
    on<ChatMessagesUpdated>(_onChatMessagesUpdated);
    on<TriggerCrisisInterventionEvent>(_onTriggerCrisisIntervention);
    on<ReturnFromBreathingExerciseEvent>(_onReturnFromBreathingExercise);
    on<OfferDebateModeEvent>(_onOfferDebateMode);
    on<CloseCrisisInterventionEvent>(_onCloseCrisisIntervention);
  }

  // Purpose: Handle sending user message and saving to chat history
  Future<void> _onSendMessage(
    SendMessageEvent event,
    Emitter<ChatState> emit,
  ) async {
    final userMessage = ChatMessage(
      id: '${DateTime.now().millisecondsSinceEpoch}_user',
      userId: event.userId,
      content: event.message,
      type: MessageType.user,
      timestamp: DateTime.now(),
    );

    emit(state.copyWith(
      messages: [...state.messages, userMessage],
      isGeneratingResponse: true,
      error: null,
    ));

    await chatRepository.saveMessage(userMessage);

    add(GenerateAIResponseEvent(
      userId: event.userId,
      message: event.message,
    ));
  }

  // Purpose: Generate AI response using secure API service and update chat
  Future<void> _onGenerateAIResponse(
    GenerateAIResponseEvent event,
    Emitter<ChatState> emit,
  ) async {
    try {
      final moodResponse = await chatRepository.generateAIResponse(
        event.userId,
        event.message,
        conversationHistory: state.messages,
      );

      emit(state.copyWith(
        isGeneratingResponse: false,
        crisisOptions: moodResponse.interventionOptions,
        currentMoodLevel: moodResponse.moodLevel,
        shouldOfferDebate: moodResponse.shouldOfferDebate,
      ));
    } catch (e) {
      emit(state.copyWith(
        error: 'Failed to get AI response: $e',
        isGeneratingResponse: false,
        crisisOptions: [],
        currentMoodLevel: null,
        shouldOfferDebate: false,
      ));
    }
  }

  // Purpose: Load user's chat history from Firebase and set up real-time updates
  Future<void> _onLoadChatHistory(
    LoadChatHistoryEvent event,
    Emitter<ChatState> emit,
  ) async {
    emit(state.copyWith(status: ChatStatus.loading));

    _messagesSubscription?.cancel();
    _messagesSubscription = chatRepository
        .getChatMessages(event.userId)
        .listen((messages) {
      add(ChatMessagesUpdated(messages));
    });
  }

  void _onClearChatHistory(
    ClearChatHistoryEvent event,
    Emitter<ChatState> emit,
  ) async {
    await chatRepository.clearChatHistory(event.userId);
    emit(state.copyWith(messages: []));
  }

  void _onChatMessagesUpdated(ChatMessagesUpdated event, Emitter<ChatState> emit) {
    emit(state.copyWith(
      messages: event.messages,
      status: ChatStatus.success,
    ));
  }

  void _onTriggerCrisisIntervention(
    TriggerCrisisInterventionEvent event,
    Emitter<ChatState> emit,
  ) {
    if (event.moodLevel == 'close') {
      emit(state.copyWith(
        crisisOptions: [],
      ));
    }
  }

  void _onReturnFromBreathingExercise(
    ReturnFromBreathingExerciseEvent event,
    Emitter<ChatState> emit,
  ) {
    final followUpMessage = ChatMessage(
      id: '${DateTime.now().millisecondsSinceEpoch}_followup',
      userId: event.userId,
      content: CrisisInterventionService.getPostBreathingMessage(event.exerciseType),
      type: MessageType.ai,
      timestamp: DateTime.now(),
      metadata: {
        'isFollowUp': true,
        'exerciseType': event.exerciseType,
      },
    );

    chatRepository.saveMessage(followUpMessage);
  }

  void _onOfferDebateMode(
    OfferDebateModeEvent event,
    Emitter<ChatState> emit,
  ) {
    emit(state.copyWith(
      debateTopic: event.topic,
      shouldOfferDebate: false,
    ));
  }

  void _onCloseCrisisIntervention(
    CloseCrisisInterventionEvent event,
    Emitter<ChatState> emit,
  ) {
    emit(state.copyWith(
      crisisOptions: [],
    ));
  }

  // Purpose: Clean up resources and cancel subscriptions when bloc is disposed
  @override
  Future<void> close() {
    _messagesSubscription?.cancel();
    return super.close();
  }
}

