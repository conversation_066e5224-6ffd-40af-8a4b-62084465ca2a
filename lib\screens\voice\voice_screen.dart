import 'package:flight_fear_wellness_app/blocs/auth/auth_state.dart';
import 'package:flight_fear_wellness_app/blocs/voice/voice_event.dart';
import 'package:flight_fear_wellness_app/blocs/voice/voice_state.dart';
import 'package:flight_fear_wellness_app/models/user_model.dart';
import 'package:flight_fear_wellness_app/utils/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flight_fear_wellness_app/blocs/auth/auth_bloc.dart';
import 'package:flight_fear_wellness_app/blocs/voice/voice_bloc.dart';
import 'package:flight_fear_wellness_app/widgets/custom_button.dart';
import 'package:flight_fear_wellness_app/widgets/crisis_intervention_widget.dart';

class VoiceScreen extends StatefulWidget {
  const VoiceScreen({super.key});

  @override
  State<VoiceScreen> createState() => _VoiceScreenState();
}

class _VoiceScreenState extends State<VoiceScreen> with SingleTickerProviderStateMixin {
  late UserModel _currentUser;
  bool _isPreloaded = false;
  late AnimationController _waveController;
  late Animation<double> _waveAnimation;

  @override
  void initState() {
    super.initState();
    final authState = context.read<AuthBloc>().state;
    if (authState.status == AuthStatus.authenticated && authState.user != null) {
      _currentUser = authState.user!;
    }

    _waveController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat(reverse: true);
    
    _waveAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _waveController, curve: Curves.easeInOut),
    );

    _preloadVoiceFeature();
  }

  @override
  void dispose() {
    _waveController.dispose();
    super.dispose();
  }

  void _preloadVoiceFeature() async {
    try {
      final voiceBloc = context.read<VoiceBloc>();
      await voiceBloc.speechService.initialize();

      final audioPlayer = voiceBloc.voiceRepository.audioPlayer;
      await audioPlayer.setVolume(1.0);
      await audioPlayer.setPlaybackRate(1.0);

      setState(() {
        _isPreloaded = true;
      });
    } catch (e) {
      setState(() {
        _isPreloaded = true;
      });
    }
  }

  Widget _buildVoiceVisualization(VoiceState state) {
    return AnimatedBuilder(
      animation: _waveAnimation,
      builder: (context, child) {
        return Container(
          width: 180,
          height: 180,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                AppTheme.primaryColor.withOpacity(0.2),
                AppTheme.primaryColor.withOpacity(0.1),
              ],
              stops: const [0.5, 1.0],
            ),
          ),
          child: Center(
            child: Transform.scale(
              scale: state.isRecording ? _waveAnimation.value : 1.0,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: state.isRecording 
                      ? AppTheme.primaryColor 
                      : Colors.grey.shade200,
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryColor.withOpacity(0.4),
                      blurRadius: state.isRecording ? 20 : 0,
                      spreadRadius: state.isRecording ? 5 : 0,
                    ),
                  ],
                ),
                child: Icon(
                  state.isRecording ? Icons.mic : Icons.mic_none,
                  size: 48,
                  color: state.isRecording ? Colors.white : Colors.grey.shade600,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildUserMessage(VoiceState state) {
    if (state.currentInput == null) return const SizedBox.shrink();
    
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.08),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.person, size: 18, color: AppTheme.primaryColor),
              const SizedBox(width: 8),
              Text(
                'You said:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            state.currentInput!,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAssistantMessage(VoiceState state) {
    if (state.currentResponse == null || state.currentResponse!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.assistant, size: 18, color: Colors.grey.shade700),
              const SizedBox(width: 8),
              Text(
                state.isAnimatingText ? 'Alora is speaking...' : 'Alora said:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: state.isAnimatingText ? Colors.blue.shade700 : Colors.green.shade700,
                ),
              ),
              if (state.isAnimatingText) ...[
                const SizedBox(width: 8),
                Icon(
                  Icons.record_voice_over,
                  size: 16,
                  color: Colors.blue.shade600,
                ),
              ] else ...[
                const SizedBox(width: 8),
                Icon(
                  Icons.check_circle,
                  size: 16,
                  color: Colors.green.shade600,
                ),
              ],
            ],
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: Text(
              state.displayedText.isNotEmpty ? state.displayedText : state.currentResponse!,
              textAlign: TextAlign.left,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.normal,
                color: Colors.black87,
                height: 1.4,
              ),
            ),
          ),
          if (state.isAnimatingText) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                for (int i = 0; i < 3; i++)
                  AnimatedContainer(
                    duration: Duration(milliseconds: 300 + (i * 100)),
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    width: 4,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.blue.shade400,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButtons(VoiceState state) {
    final bool hasHadConversation = state.conversationHistory.isNotEmpty || state.currentResponse != null;
    final String mainButtonText = state.isRecording
        ? 'Listening...'
        : hasHadConversation
            ? 'Reply'
            : 'Start Conversation';

    return Column(
      children: [
        if (state.status == VoiceStatus.processing)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Alora Is Getting Your Response...',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
          )
        else if (state.isAnimatingText)
          Center(
            child: CustomButton(
              text: 'Stop Speaking',
              onPressed: () => context.read<VoiceBloc>().add(StopTextAnimationEvent()),
              icon: Icons.stop,
              type: ButtonType.outline,
              width: 200,
            ),
          )
        else if (state.isRecording)
          Center(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Listening... Speak naturally',
                    style: TextStyle(
                      color: Colors.red.shade700,
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          )
        else
          Column(
            children: [
              Center(
                child: CustomButton(
                  text: mainButtonText,
                  onPressed: () => context.read<VoiceBloc>().add(
                        StartRecordingEvent(_currentUser.id),
                      ),
                  icon: Icons.mic,
                  type: ButtonType.primary,
                  width: 220,
                ),
              ),

              if (state.audioFilePath != null && !state.isRecording && !state.isAnimatingText) ...[
                const SizedBox(height: 12),
                Center(
                  child: CustomButton(
                    text: 'Play Response Again',
                    onPressed: () => context.read<VoiceBloc>().add(
                          StartTextAnimationEvent(
                            text: state.currentResponse!,
                            audioFilePath: state.audioFilePath!,
                          ),
                        ),
                    icon: Icons.play_arrow,
                    type: ButtonType.outline,
                    width: 220,
                  ),
                ),
              ],
            ],
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Voice Assistant', style: TextStyle(fontWeight: FontWeight.w600)),
        centerTitle: true,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Reset conversation',
            onPressed: () => context.read<VoiceBloc>().add(ResetVoiceSessionEvent()),
          ),
        ],
      ),
      body: BlocConsumer<VoiceBloc, VoiceState>(
        listener: (context, state) {
          if (state.status == VoiceStatus.error) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.error!),
                backgroundColor: AppTheme.errorColor,
              ),
            );
          }
        },
        builder: (context, state) {
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.white,
                  Colors.grey.shade50,
                ],
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      child: Column(
                        children: [
                          _buildUserMessage(state),
                          _buildAssistantMessage(state),

                          if (state.crisisOptions.isNotEmpty)
                            CrisisInterventionWidget(
                              options: state.crisisOptions,
                              userId: _currentUser.id,
                              moodLevel: state.currentMoodLevel ?? 'normal',
                              onClose: () {
                                context.read<VoiceBloc>().add(
                                  CloseVoiceCrisisInterventionEvent(
                                    userId: _currentUser.id,
                                  ),
                                );
                              },
                            ),
                        ],
                      ),
                    ),
                  ),

                  _buildVoiceVisualization(state),
                  const SizedBox(height: 20),
                  
                  
                  _buildActionButtons(state),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}