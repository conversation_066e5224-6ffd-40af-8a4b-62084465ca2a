{"name": "lemonsqueezy-webhook-bridge", "version": "1.0.0", "description": "LemonSqueezy webhook bridge for Firebase Spark plan", "main": "api/webhook.js", "scripts": {"start": "node webhook_bridge.js", "dev": "node webhook_bridge.js", "test": "node -e \"console.log('Webhook bridge test passed')\""}, "dependencies": {}, "engines": {"node": "18.x"}, "keywords": ["lemonsqueezy", "webhook", "firebase", "serverless"], "author": "Flight Fear Wellness", "license": "MIT"}