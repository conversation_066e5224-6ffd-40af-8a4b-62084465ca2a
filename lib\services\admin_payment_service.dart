import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/payment_models.dart';
import '../models/subscription_model.dart';
import '../models/user_subscription_model.dart';
import '../models/credit_model.dart';
import 'payment_manager.dart';

class AdminPaymentService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  static Future<Map<String, dynamic>> getPaymentStats() async {
    try {
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final startOfWeek = now.subtract(Duration(days: now.weekday - 1));

      final monthlyPayments = await _firestore
          .collection('payment_logs')
          .where('timestamp', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfMonth))
          .where('status', isEqualTo: 'succeeded')
          .get();

      final weeklyPayments = await _firestore
          .collection('payment_logs')
          .where('timestamp', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfWeek))
          .where('status', isEqualTo: 'succeeded')
          .get();

      double monthlyRevenue = 0;
      double weeklyRevenue = 0;

      for (final doc in monthlyPayments.docs) {
        final amount = doc.data()['amount'] as double? ?? 0;
        monthlyRevenue += amount;
      }

      for (final doc in weeklyPayments.docs) {
        final amount = doc.data()['amount'] as double? ?? 0;
        weeklyRevenue += amount;
      }

      final users = await _firestore.collection('users').get();
      final subscriptionCounts = <String, int>{
        'free': 0,
        'basic': 0,
        'premium': 0,
      };

      for (final doc in users.docs) {
        final data = doc.data();
        final plan = data['subscription']?['plan'] ?? 'free';
        subscriptionCounts[plan] = (subscriptionCounts[plan] ?? 0) + 1;
      }

      return {
        'revenue': {
          'monthly': monthlyRevenue,
          'weekly': weeklyRevenue,
        },
        'subscriptions': subscriptionCounts,
        'payments': {
          'monthly_count': monthlyPayments.docs.length,
          'weekly_count': weeklyPayments.docs.length,
        },
        'total_users': users.docs.length,
      };
    } catch (e) {
      debugPrint('Failed to get payment stats: $e');
      return {};
    }
  }

  static Future<List<Map<String, dynamic>>> getRecentPayments({
    int limit = 50,
  }) async {
    try {
      final query = await _firestore
          .collection('payment_logs')
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();

      return query.docs.map((doc) {
        final data = doc.data();
        return {
          'id': doc.id,
          'provider': data['provider'],
          'customerEmail': data['customerEmail'],
          'amount': data['amount'],
          'status': data['status'],
          'timestamp': data['timestamp'],
          'data': data['data'],
        };
      }).toList();
    } catch (e) {
      debugPrint('Failed to get recent payments: $e');
      return [];
    }
  }

  static Future<List<Map<String, dynamic>>> getWebhookLogs({
    int limit = 100,
    String? provider,
    String? status,
  }) async {
    try {
      Query query = _firestore
          .collection('webhook_logs')
          .orderBy('timestamp', descending: true);

      if (provider != null) {
        query = query.where('provider', isEqualTo: provider);
      }

      if (status != null) {
        query = query.where('status', isEqualTo: status);
      }

      final result = await query.limit(limit).get();

      return result.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          'provider': data['provider'],
          'status': data['status'],
          'error': data['error'],
          'timestamp': data['timestamp'],
          'payload': data['payload'],
        };
      }).toList();
    } catch (e) {
      debugPrint('Failed to get webhook logs: $e');
      return [];
    }
  }

  static Future<bool> grantSubscription({
    required String userEmail,
    required SubscriptionPlan plan,
    int? durationDays,
    String? reason,
  }) async {
    try {
      final userQuery = await _firestore
          .collection('users')
          .where('email', isEqualTo: userEmail)
          .limit(1)
          .get();

      if (userQuery.docs.isEmpty) {
        debugPrint('User not found: $userEmail');
        return false;
      }

      final userId = userQuery.docs.first.id;
      final userData = userQuery.docs.first.data();

      final currentSubscription = UserSubscriptionModel.fromMap(userData, userId);
      final now = DateTime.now();
      final endDate = durationDays != null 
          ? now.add(Duration(days: durationDays))
          : now.add(const Duration(days: 30));

      final updatedSubscription = currentSubscription.copyWith(
        subscription: SubscriptionModel(
          plan: plan,
          status: SubscriptionStatus.active,
          startDate: now,
          endDate: endDate,
          autoRenew: false,
          trialUsed: currentSubscription.subscription.trialUsed,
        ),
        credits: CreditModel.fromPlan(plan),
      );

      await _firestore
          .collection('users')
          .doc(userId)
          .update(updatedSubscription.toMap());

      await _firestore.collection('admin_actions').add({
        'action': 'grant_subscription',
        'userEmail': userEmail,
        'plan': plan.name,
        'durationDays': durationDays,
        'reason': reason,
        'timestamp': FieldValue.serverTimestamp(),
      });

      debugPrint('Granted ${plan.name} subscription to $userEmail');
      return true;
    } catch (e) {
      debugPrint('Failed to grant subscription: $e');
      return false;
    }
  }

  static Future<bool> revokeSubscription({
    required String userEmail,
    String? reason,
  }) async {
    try {
      final userQuery = await _firestore
          .collection('users')
          .where('email', isEqualTo: userEmail)
          .limit(1)
          .get();

      if (userQuery.docs.isEmpty) {
        debugPrint('User not found: $userEmail');
        return false;
      }

      final userId = userQuery.docs.first.id;
      final userData = userQuery.docs.first.data();

      final currentSubscription = UserSubscriptionModel.fromMap(userData, userId);
      final updatedSubscription = currentSubscription.copyWith(
        subscription: SubscriptionModel.free(),
        credits: CreditModel.free(),
      );

      await _firestore
          .collection('users')
          .doc(userId)
          .update(updatedSubscription.toMap());

      await _firestore.collection('admin_actions').add({
        'action': 'revoke_subscription',
        'userEmail': userEmail,
        'reason': reason,
        'timestamp': FieldValue.serverTimestamp(),
      });

      debugPrint('Revoked subscription from $userEmail');
      return true;
    } catch (e) {
      debugPrint('Failed to revoke subscription: $e');
      return false;
    }
  }

  static Future<bool> addCredits({
    required String userEmail,
    int? chatCredits,
    int? voiceCredits,
    String? reason,
  }) async {
    try {
      final userQuery = await _firestore
          .collection('users')
          .where('email', isEqualTo: userEmail)
          .limit(1)
          .get();

      if (userQuery.docs.isEmpty) {
        debugPrint('User not found: $userEmail');
        return false;
      }

      final userId = userQuery.docs.first.id;
      final userData = userQuery.docs.first.data();

      final currentSubscription = UserSubscriptionModel.fromMap(userData, userId);
      final currentCredits = currentSubscription.credits;
      
      final updatedCredits = currentCredits.copyWith(
        chatLimit: chatCredits != null 
            ? currentCredits.chatLimit + chatCredits 
            : currentCredits.chatLimit,
        voiceLimit: voiceCredits != null 
            ? currentCredits.voiceLimit + voiceCredits 
            : currentCredits.voiceLimit,
      );

      final updatedSubscription = currentSubscription.copyWith(
        credits: updatedCredits,
      );

      await _firestore
          .collection('users')
          .doc(userId)
          .update(updatedSubscription.toMap());

      await _firestore.collection('admin_actions').add({
        'action': 'add_credits',
        'userEmail': userEmail,
        'chatCredits': chatCredits,
        'voiceCredits': voiceCredits,
        'reason': reason,
        'timestamp': FieldValue.serverTimestamp(),
      });

      debugPrint('Added credits to $userEmail: chat=$chatCredits, voice=$voiceCredits');
      return true;
    } catch (e) {
      debugPrint('Failed to add credits: $e');
      return false;
    }
  }

  static Future<Map<String, dynamic>?> getUserSubscriptionDetails(String userEmail) async {
    try {
      final userQuery = await _firestore
          .collection('users')
          .where('email', isEqualTo: userEmail)
          .limit(1)
          .get();

      if (userQuery.docs.isEmpty) {
        return null;
      }

      final userId = userQuery.docs.first.id;
      final userData = userQuery.docs.first.data();
      final subscription = UserSubscriptionModel.fromMap(userData, userId);

      return {
        'userId': userId,
        'email': subscription.user.email,
        'name': subscription.user.name,
        'plan': subscription.subscription.plan.name,
        'status': subscription.subscription.status.name,
        'startDate': subscription.subscription.startDate.toIso8601String(),
        'endDate': subscription.subscription.endDate.toIso8601String(),
        'daysRemaining': subscription.daysRemaining,
        'autoRenew': subscription.subscription.autoRenew,
        'credits': {
          'chat': {
            'limit': subscription.credits.chatLimit,
            'used': subscription.credits.chatUsed,
            'remaining': subscription.credits.chatRemaining,
          },
          'voice': {
            'limit': subscription.credits.voiceLimit,
            'used': subscription.credits.voiceUsed,
            'remaining': subscription.credits.voiceRemaining,
          },
        },
        'usage': {
          'totalChatPrompts': subscription.usage.totalChatPrompts,
          'totalVoicePrompts': subscription.usage.totalVoicePrompts,
          'lastChatAt': subscription.usage.lastChatAt?.toIso8601String(),
          'lastVoiceAt': subscription.usage.lastVoiceAt?.toIso8601String(),
        },
        'payment': {
          'lemonsqueezy': {
            'customerId': subscription.payment.lemonsqueezyCustomerId,
            'subscriptionId': subscription.payment.lemonsqueezySubscriptionId,
          },
          'stripe': {
            'customerId': subscription.payment.stripeCustomerId,
            'subscriptionId': subscription.payment.stripeSubscriptionId,
          },
          'paypal': {
            'customerId': subscription.payment.paypalCustomerId,
            'subscriptionId': subscription.payment.paypalSubscriptionId,
          },
        },
      };
    } catch (e) {
      debugPrint('Failed to get user subscription details: $e');
      return null;
    }
  }

  static Future<List<Map<String, dynamic>>> getAdminActionLogs({
    int limit = 50,
  }) async {
    try {
      final query = await _firestore
          .collection('admin_actions')
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();

      return query.docs.map((doc) {
        final data = doc.data();
        return {
          'id': doc.id,
          'action': data['action'],
          'userEmail': data['userEmail'],
          'details': data,
          'timestamp': data['timestamp'],
        };
      }).toList();
    } catch (e) {
      debugPrint('Failed to get admin action logs: $e');
      return [];
    }
  }

  static Future<bool> configurePaymentProvider({
    required PaymentProvider provider,
    required PaymentConfig config,
  }) async {
    try {
      await PaymentManager.instance.addProvider(config);
      
      await _firestore.collection('admin_actions').add({
        'action': 'configure_payment_provider',
        'provider': provider.name,
        'isTestMode': config.isTestMode,
        'timestamp': FieldValue.serverTimestamp(),
      });

      debugPrint('Configured payment provider: ${provider.name}');
      return true;
    } catch (e) {
      debugPrint('Failed to configure payment provider: $e');
      return false;
    }
  }

  static Map<String, dynamic> getPaymentProviderStatus() {
    final providerInfo = PaymentManager.instance.providerInfo;
    return providerInfo.map((key, value) => MapEntry(key.name, value));
  }
}
