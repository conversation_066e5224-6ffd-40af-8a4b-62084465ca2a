import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_subscription_model.dart';
import '../models/subscription_model.dart';
import '../widgets/subscription/credit_warning_dialog.dart';
import 'package:flutter/material.dart';

class UsageAnalyticsService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  static Future<void> trackActivity({
    required String userId,
    required String activityType,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      await _firestore.collection('usage_analytics').add({
        'userId': userId,
        'activityType': activityType,
        'timestamp': Timestamp.fromDate(DateTime.now()),
        'metadata': metadata ?? {},
      });
    } catch (e) {
      print('Error tracking activity: $e');
    }
  }

  static Future<UpgradeSuggestion?> analyzeUsagePatterns(
    UserSubscriptionModel userSubscription,
  ) async {
    try {
      final userId = userSubscription.user.id;
      final currentPlan = userSubscription.subscription.plan;
      
      if (currentPlan == SubscriptionPlan.premium) return null;

      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      
      final usageQuery = await _firestore
          .collection('usage_analytics')
          .where('userId', isEqualTo: userId)
          .where('timestamp', isGreaterThan: Timestamp.fromDate(thirtyDaysAgo))
          .get();

      final usageData = usageQuery.docs.map((doc) => doc.data()).toList();
      
      final analysis = _analyzePatterns(usageData, userSubscription);
      
      if (analysis.shouldSuggestUpgrade) {
        return UpgradeSuggestion(
          currentPlan: currentPlan,
          suggestedPlan: analysis.suggestedPlan,
          reason: analysis.reason,
          confidence: analysis.confidence,
          potentialSavings: analysis.potentialSavings,
          usageStats: analysis.usageStats,
        );
      }

      return null;
    } catch (e) {
      print('Error analyzing usage patterns: $e');
      return null;
    }
  }

  static UsageAnalysis _analyzePatterns(
    List<Map<String, dynamic>> usageData,
    UserSubscriptionModel userSubscription,
  ) {
    final chatActivities = usageData.where((d) => d['activityType'] == 'chat').length;
    final voiceActivities = usageData.where((d) => d['activityType'] == 'voice').length;
    final totalActivities = chatActivities + voiceActivities;
    
    final currentPlan = userSubscription.subscription.plan;
    
    final daysActive = _calculateActiveDays(usageData);
    final dailyChatUsage = daysActive > 0 ? (chatActivities / daysActive).toDouble() : 0.0;
    final dailyVoiceUsage = daysActive > 0 ? (voiceActivities / daysActive).toDouble() : 0.0;
    
    final usageIntensity = _calculateUsageIntensity(usageData);
    
    final isPowerUser = _isPowerUser(
      dailyChatUsage: dailyChatUsage,
      dailyVoiceUsage: dailyVoiceUsage,
      currentPlan: currentPlan,
      usageIntensity: usageIntensity,
    );

    final potentialSavings = _calculatePotentialSavings(
      currentPlan: currentPlan,
      dailyChatUsage: dailyChatUsage,
      dailyVoiceUsage: dailyVoiceUsage,
    );

    SubscriptionPlan? suggestedPlan;
    String reason = '';
    double confidence = 0.0;

    if (currentPlan == SubscriptionPlan.free && isPowerUser) {
      if (dailyChatUsage > 200 || dailyVoiceUsage > 5) {
        suggestedPlan = SubscriptionPlan.premium;
        reason = 'Your high usage patterns suggest Premium plan would provide better value';
        confidence = 0.9;
      } else {
        suggestedPlan = SubscriptionPlan.basic;
        reason = 'Upgrade to Basic for more credits and uninterrupted access';
        confidence = 0.8;
      }
    } else if (currentPlan == SubscriptionPlan.basic && isPowerUser) {
      if (dailyChatUsage > 400 || dailyVoiceUsage > 8) {
        suggestedPlan = SubscriptionPlan.premium;
        reason = 'Your usage exceeds Basic limits. Premium offers better value for power users';
        confidence = 0.85;
      }
    }

    return UsageAnalysis(
      shouldSuggestUpgrade: suggestedPlan != null,
      suggestedPlan: suggestedPlan ?? currentPlan,
      reason: reason,
      confidence: confidence,
      potentialSavings: potentialSavings,
      usageStats: {
        'totalActivities': totalActivities,
        'chatActivities': chatActivities,
        'voiceActivities': voiceActivities,
        'daysActive': daysActive,
        'dailyChatUsage': dailyChatUsage,
        'dailyVoiceUsage': dailyVoiceUsage,
        'usageIntensity': usageIntensity,
        'isPowerUser': isPowerUser,
      },
    );
  }

  static int _calculateActiveDays(List<Map<String, dynamic>> usageData) {
    final uniqueDays = <String>{};
    
    for (final data in usageData) {
      final timestamp = (data['timestamp'] as Timestamp).toDate();
      final dayKey = '${timestamp.year}-${timestamp.month}-${timestamp.day}';
      uniqueDays.add(dayKey);
    }
    
    return uniqueDays.length;
  }

  static double _calculateUsageIntensity(List<Map<String, dynamic>> usageData) {
    final activeDays = _calculateActiveDays(usageData);
    return activeDays > 0 ? usageData.length / activeDays : 0.0;
  }

  static bool _isPowerUser({
    required double dailyChatUsage,
    required double dailyVoiceUsage,
    required SubscriptionPlan currentPlan,
    required double usageIntensity,
  }) {
    switch (currentPlan) {
      case SubscriptionPlan.free:
        return dailyChatUsage > 12.5 || dailyVoiceUsage > 2.5 || usageIntensity > 15;
      
      case SubscriptionPlan.basic:
        return dailyChatUsage > 260 || dailyVoiceUsage > 1.6 || usageIntensity > 20;
      
      case SubscriptionPlan.premium:
        return false;
    }
  }

  static double _calculatePotentialSavings({
    required SubscriptionPlan currentPlan,
    required double dailyChatUsage,
    required double dailyVoiceUsage,
  }) {
    
    if (currentPlan == SubscriptionPlan.free) {
      final chatOverage = (dailyChatUsage * 30 - 25).clamp(0, double.infinity);
      final voiceOverage = (dailyVoiceUsage * 30 - 5).clamp(0, double.infinity);
      
      return (chatOverage * 0.01) + (voiceOverage * 0.50);
    }
    
    return 0.0;
  }

  static Future<void> showUpgradeSuggestion({
    required BuildContext context,
    required UpgradeSuggestion suggestion,
    required VoidCallback onUpgrade,
  }) async {
    if (context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => CreditWarningDialog(
          title: 'Upgrade Recommended',
          message: '${suggestion.reason}\n\nBased on your usage patterns, you could save up to \$${suggestion.potentialSavings.toStringAsFixed(2)} per month.',
          onUpgrade: onUpgrade,
        ),
      );
    }
  }

  static Future<double> getUserEngagementScore(String userId) async {
    try {
      final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
      
      final recentUsage = await _firestore
          .collection('usage_analytics')
          .where('userId', isEqualTo: userId)
          .where('timestamp', isGreaterThan: Timestamp.fromDate(sevenDaysAgo))
          .get();

      final activeDays = _calculateActiveDays(
        recentUsage.docs.map((doc) => doc.data()).toList()
      );
      
      return (activeDays / 7.0 * 100).clamp(0, 100);
    } catch (e) {
      print('Error calculating engagement score: $e');
      return 0.0;
    }
  }
}

class UsageAnalysis {
  final bool shouldSuggestUpgrade;
  final SubscriptionPlan suggestedPlan;
  final String reason;
  final double confidence;
  final double potentialSavings;
  final Map<String, dynamic> usageStats;

  const UsageAnalysis({
    required this.shouldSuggestUpgrade,
    required this.suggestedPlan,
    required this.reason,
    required this.confidence,
    required this.potentialSavings,
    required this.usageStats,
  });
}

class UpgradeSuggestion {
  final SubscriptionPlan currentPlan;
  final SubscriptionPlan suggestedPlan;
  final String reason;
  final double confidence;
  final double potentialSavings;
  final Map<String, dynamic> usageStats;

  const UpgradeSuggestion({
    required this.currentPlan,
    required this.suggestedPlan,
    required this.reason,
    required this.confidence,
    required this.potentialSavings,
    required this.usageStats,
  });

  String get planUpgradeText {
    return '${currentPlan.name.toUpperCase()} → ${suggestedPlan.name.toUpperCase()}';
  }

  bool get isHighConfidence => confidence >= 0.8;
}
