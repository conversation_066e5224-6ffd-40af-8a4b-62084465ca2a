import 'package:flutter/material.dart';
import '../../utils/theme.dart';

class UpgradeRequiredDialog extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback onUpgrade;
  final bool canExit;

  const UpgradeRequiredDialog({
    super.key,
    required this.title,
    required this.message,
    required this.onUpgrade,
    this.canExit = true,
  });

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => canExit,
      child: AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.lock_outline,
                color: AppTheme.primaryColor,
                size: 32,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              message,
              style: const TextStyle(
                fontSize: 16,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.orange.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.orange,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Upgrade to continue using chat and voice features',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          if (canExit)
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Exit',
                style: TextStyle(
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onUpgrade();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
            ),
            child: const Text(
              'Upgrade Now',
              style: TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  static void showForFreePlan({
    required BuildContext context,
    required VoidCallback onUpgrade,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => UpgradeRequiredDialog(
        title: 'Free Plan Limit Reached',
        message: 'You\'ve reached your free plan limits. Upgrade to continue enjoying unlimited access to our AI therapy features.',
        onUpgrade: onUpgrade,
        canExit: true,
      ),
    );
  }

  static void showForOutOfCredits({
    required BuildContext context,
    required String creditType,
    required VoidCallback onUpgrade,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => UpgradeRequiredDialog(
        title: 'Out of $creditType Credits',
        message: 'You\'ve used all your $creditType credits for this billing period. Upgrade your plan to continue.',
        onUpgrade: onUpgrade,
        canExit: true,
      ),
    );
  }

  static void showForExpiredSubscription({
    required BuildContext context,
    required VoidCallback onUpgrade,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => UpgradeRequiredDialog(
        title: 'Subscription Expired',
        message: 'Your subscription has expired. Renew or upgrade to continue using our premium features.',
        onUpgrade: onUpgrade,
        canExit: false,
      ),
    );
  }
}
