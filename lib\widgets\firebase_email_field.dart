import 'package:flutter/material.dart';
import 'dart:async';
import '../utils/theme.dart';
import '../services/email_verification_service.dart';

class FirebaseEmailField extends StatefulWidget {
  final String? label;
  final String? hint;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final IconData? prefixIcon;
  final TextInputAction textInputAction;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final Function(String?)? onSaved;
  final bool isForLogin;
  final List<String>? autofillHints;

  const FirebaseEmailField({
    super.key,
    this.label,
    this.hint,
    this.controller,
    this.validator,
    this.prefixIcon,
    this.textInputAction = TextInputAction.next,
    this.onChanged,
    this.onSubmitted,
    this.onSaved,
    this.isForLogin = true,
    this.autofillHints,
  });

  @override
  State<FirebaseEmailField> createState() => _FirebaseEmailFieldState();
}

class _FirebaseEmailFieldState extends State<FirebaseEmailField> {
  final EmailVerificationService _emailService = EmailVerificationService();
  bool _hasFocus = false;
  String _currentEmail = '';
  bool _isValidFormat = false;
  EmailValidationResult? _validationResult;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _currentEmail = widget.controller?.text ?? '';
    _validateEmailFormat(_currentEmail);
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _validateEmailFormat(String email) {
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    setState(() {
      _isValidFormat = emailRegex.hasMatch(email) && email.isNotEmpty;
    });
  }

  void _checkEmailWithFirebase(String email) {
    if (!_isValidFormat || email.isEmpty) {
      setState(() {
        _validationResult = null;
      });
      return;
    }

    setState(() {
      _validationResult = null;
    });

    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 800), () async {
      try {
        final result = widget.isForLogin
            ? await _emailService.validateEmailForLogin(email)
            : await _emailService.validateEmailForSignup(email);

        if (mounted) {
          setState(() {
            if (result.success) {
              _validationResult = result.data;
            } else {
              _validationResult = EmailValidationResult(
                isValid: false,
                exists: false,
                message: result.error ?? 'Validation failed',
                canProceed: false,
              );
            }
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _validationResult = EmailValidationResult(
              isValid: false,
              exists: false,
              message: 'Network error',
              canProceed: false,
            );
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
        ],
        Focus(
          onFocusChange: (hasFocus) {
            setState(() {
              _hasFocus = hasFocus;
            });
            if (!hasFocus && _currentEmail.isNotEmpty) {
              _checkEmailWithFirebase(_currentEmail);
            }
          },
          child: TextFormField(
            controller: widget.controller,
            validator: widget.validator,
            keyboardType: TextInputType.emailAddress,
            textInputAction: widget.textInputAction,
            autocorrect: false,
            autofillHints: widget.autofillHints,
            onChanged: (value) {
              setState(() {
                _currentEmail = value;
                _validationResult = null;
              });
              _validateEmailFormat(value);
              
              if (value.isNotEmpty && _isValidFormat) {
                _checkEmailWithFirebase(value);
              }
              
              widget.onChanged?.call(value);
            },
            onFieldSubmitted: widget.onSubmitted,
            onSaved: widget.onSaved,
            style: Theme.of(context).textTheme.bodyLarge,
            decoration: InputDecoration(
              hintText: widget.hint,
              prefixIcon: widget.prefixIcon != null
                  ? Icon(
                      widget.prefixIcon,
                      color: _hasFocus ? AppTheme.primaryColor : AppTheme.textSecondary,
                    )
                  : null,
              suffixIcon: _buildSuffixIcon(),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.errorColor, width: 2),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.errorColor, width: 2),
              ),
              filled: true,
              fillColor: _hasFocus ? Colors.blue.shade50 : Colors.grey.shade50,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
          ),
        ),
        if (_currentEmail.isNotEmpty)
          _buildValidationFeedback(),
      ],
    );
  }

  Widget _buildSuffixIcon() {

    if (_validationResult != null && _validationResult!.message.isNotEmpty) {
      final isSuccess = widget.isForLogin
          ? _validationResult!.exists
          : !_validationResult!.exists;

      return Icon(
        isSuccess ? Icons.check_circle : Icons.error,
        color: isSuccess ? Colors.green : Colors.red,
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildValidationFeedback() {

    if (_validationResult != null && _validationResult!.message.isNotEmpty) {
      Color color;
      IconData icon;

      if (widget.isForLogin) {
        color = _validationResult!.exists ? Colors.green : Colors.red;
        icon = _validationResult!.exists ? Icons.check_circle_outline : Icons.error_outline;
      } else {
        color = !_validationResult!.exists ? Colors.green : Colors.red;
        icon = !_validationResult!.exists ? Icons.check_circle_outline : Icons.error_outline;
      }

      return Container(
        margin: const EdgeInsets.only(top: 8),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 8),
            if (_validationResult!.message.isNotEmpty)
              Expanded(
              child: Text(
                _validationResult!.message,
                style: TextStyle(
                  fontSize: 12,
                  color: color,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }
}
