import 'dart:async';
import 'package:flutter_speech/flutter_speech.dart';
import 'package:permission_handler/permission_handler.dart';

class SpeechRecognitionService {
  static final SpeechRecognitionService _instance = SpeechRecognitionService._internal();
  factory SpeechRecognitionService() => _instance;
  SpeechRecognitionService._internal();

  SpeechRecognition? _speech;
  bool _isInitialized = false;
  bool _isListening = false;
  String? _lastResult;
  String? _lastPartialResult;
  Timer? _recognitionTimer;
  Timer? _pauseDetectionTimer;
  String _currentLocale = 'en-US';
  DateTime? _lastSpeechTime;

  final StreamController<String> _resultController = StreamController<String>.broadcast();
  final StreamController<bool> _listeningController = StreamController<bool>.broadcast();
  final StreamController<String> _errorController = StreamController<String>.broadcast();

  Stream<String> get onResult => _resultController.stream;
  Stream<bool> get onListeningChange => _listeningController.stream;
  Stream<String> get onError => _errorController.stream;

  bool get isListening => _isListening;
  bool get isInitialized => _isInitialized;

  Future<bool> initialize() async {
    try {
      if (_isInitialized) return true;

      final permissionStatus = await Permission.microphone.request();
      if (permissionStatus != PermissionStatus.granted) {
        _errorController.add('Microphone permission denied');
        return false;
      }

      _speech = SpeechRecognition();

      _speech!.setAvailabilityHandler((bool result) {
        if (!_isInitialized) {
          _isInitialized = result;
          if (!result) {
            _errorController.add('Speech recognition not available on this device');
          }
        }
      });

      _speech!.setRecognitionStartedHandler(() {
        _isListening = true;
        _listeningController.add(true);
      });

      _speech!.setRecognitionResultHandler((String text) {
        if (text.isNotEmpty) {
          _lastPartialResult = text;
          _lastSpeechTime = DateTime.now();
          _resultController.add(text);

          // Cancel any existing pause detection timer
          _pauseDetectionTimer?.cancel();
          // Increase pause detection time to allow for complete speech processing
          _pauseDetectionTimer = Timer(const Duration(seconds: 4), () {
            if (_isListening && _lastPartialResult != null && _lastPartialResult!.isNotEmpty) {
              print('Finalizing recognition due to pause detection: $_lastPartialResult');
              _finalizeRecognition(_lastPartialResult!);
            }
          });
        }
      });

      _speech!.setRecognitionCompleteHandler((String text) {
        print('Recognition complete handler called with: "$text"');
        print('Current partial result: "$_lastPartialResult"');

        if (_isListening) {
          // Cancel pause detection timer since we have a completion event
          _pauseDetectionTimer?.cancel();

          // Increase delay to ensure we get the most complete result
          Timer(const Duration(milliseconds: 300), () {
            if (_isListening) {
              final finalText = _chooseBestResult(text, _lastPartialResult);
              print('Final chosen text: "$finalText"');

              if (finalText.isNotEmpty && finalText.trim().length > 1) {
                _finalizeRecognition(finalText);
              } else {
                _isListening = false;
                _listeningController.add(false);
                _errorController.add('No speech detected. Please speak clearly and try again.');
              }
            }
          });
        }
      });



      final locales = ['en-US', 'en_US', 'en', 'en-GB'];
      bool activated = false;

      for (String locale in locales) {
        try {
          activated = await _speech!.activate(locale);
          if (activated) {
            _currentLocale = locale;
            break;
          }
        } catch (e) {
          continue;
        }
      }

      _isInitialized = activated;

      if (!activated) {
        _errorController.add('Speech recognition activation failed with all locales');
      }

      return activated;
    } catch (e) {
      _errorController.add('Failed to initialize speech recognition: $e');
      return false;
    }
  }

  void _finalizeRecognition(String finalText) {
    print('Finalizing recognition with: "$finalText"');

    // Ensure we use the most complete result available
    final bestResult = _chooseBestResult(finalText, _lastPartialResult);

    _isListening = false;
    _listeningController.add(false);
    _lastResult = bestResult;
    _resultController.add("SPEECH_COMPLETED:$bestResult");
    _pauseDetectionTimer?.cancel();

    print('Finalized with best result: "$bestResult"');
  }

  String _chooseBestResult(String finalResult, String? partialResult) {
    print('Choosing best result - Final: "$finalResult", Partial: "$partialResult"');

    if (partialResult == null || partialResult.isEmpty) {
      print('Using final result (no partial): "$finalResult"');
      return finalResult;
    }

    if (finalResult.isEmpty) {
      print('Using partial result (no final): "$partialResult"');
      return partialResult;
    }

    final partialWords = partialResult.trim().split(' ').where((w) => w.isNotEmpty).toList();
    final finalWords = finalResult.trim().split(' ').where((w) => w.isNotEmpty).toList();

    print('Partial words count: ${partialWords.length}, Final words count: ${finalWords.length}');

    // Prioritize the result with more words (more complete)
    if (finalWords.length > partialWords.length) {
      print('Using final result (more words): "$finalResult"');
      return finalResult;
    }

    if (partialWords.length > finalWords.length) {
      print('Using partial result (more words): "$partialResult"');
      return partialResult;
    }

    // If same word count, prioritize by character length (more detailed)
    if (finalResult.length > partialResult.length) {
      print('Using final result (longer): "$finalResult"');
      return finalResult;
    }

    if (partialResult.length > finalResult.length) {
      print('Using partial result (longer): "$partialResult"');
      return partialResult;
    }

    // If both are equal, check if one contains the other (partial might be more complete)
    if (partialResult.contains(finalResult)) {
      print('Using partial result (contains final): "$partialResult"');
      return partialResult;
    }

    if (finalResult.contains(partialResult)) {
      print('Using final result (contains partial): "$finalResult"');
      return finalResult;
    }

    // Default to final result if all else is equal
    print('Using final result (default): "$finalResult"');
    return finalResult;
  }

  Future<bool> startListening({
    Duration? listenFor,
    Duration? pauseFor,
    bool partialResults = true,
  }) async {
    try {
      if (!_isInitialized) {
        final initialized = await initialize();
        if (!initialized) {
          print('Failed to initialize speech recognition');
          return false;
        }
      }

      if (_isListening) {
        await stopListening();
        await Future.delayed(const Duration(milliseconds: 100));
      }

      _lastResult = null;
      _lastPartialResult = null;
      _lastSpeechTime = null;
      _pauseDetectionTimer?.cancel();

      print('Starting speech recognition...');
      final result = await _speech!.listen();

      if (result) {
        print('Speech recognition started successfully');
        _recognitionTimer?.cancel();
        _recognitionTimer = Timer(listenFor ?? const Duration(seconds: 45), () {
          print('Auto-stopping speech recognition after timeout');
          stopListening();
        });
      } else {
        print('Failed to start speech recognition');
      }

      return result;
    } catch (e) {
      print('Error starting speech recognition: $e');
      _errorController.add('Failed to start listening: $e');
      return false;
    }
  }

  Future<String?> stopListening() async {
    try {
      if (_isListening) {
        print('Stopping speech recognition...');

        // Give a small delay to allow any final recognition results to come through
        await Future.delayed(const Duration(milliseconds: 150));

        await _speech!.stop();
        _recognitionTimer?.cancel();
        _pauseDetectionTimer?.cancel();
        _isListening = false;
        _listeningController.add(false);

        // Choose the best result between _lastResult and _lastPartialResult
        final bestResult = _chooseBestResult(_lastResult ?? '', _lastPartialResult);
        print('Stop listening - returning best result: "$bestResult"');

        return bestResult.isNotEmpty ? bestResult : _lastResult;
      }
      return _lastResult;
    } catch (e) {
      print('Error stopping speech recognition: $e');
      _errorController.add('Failed to stop listening: $e');
      return _lastResult;
    }
  }

  Future<bool> isAvailable() async {
    try {
      if (!_isInitialized) {
        await initialize();
      }
      return _isInitialized;
    } catch (e) {
      return false;
    }
  }

  void resetAvailability() {
    if (_speech != null) {
      _isInitialized = true;
    }
  }

  Future<List<String>> getLocales() async {
    try {
      return ['en_US', 'en_GB', 'es_ES', 'fr_FR', 'de_DE', 'it_IT', 'pt_BR', 'ru_RU', 'ja_JP', 'ko_KR', 'zh_CN'];
    } catch (e) {
      return ['en_US'];
    }
  }

  void dispose() {
    try {
      _speech?.stop();
    } catch (e) {
    }
    _recognitionTimer?.cancel();
    _pauseDetectionTimer?.cancel();
    _resultController.close();
    _listeningController.close();
    _errorController.close();
  }
}