"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.paypalWebhook = exports.stripeWebhook = exports.deleteUserFromAuth = exports.voiceAPI = exports.chatAPI = exports.lemonsqueezyWebhook = exports.SUBSCRIPTION_PLANS = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
// Import webhook handlers
const stripe_webhook_1 = require("./webhooks/stripe-webhook");
Object.defineProperty(exports, "stripeWebhook", { enumerable: true, get: function () { return stripe_webhook_1.stripeWebhook; } });
const paypal_webhook_1 = require("./webhooks/paypal-webhook");
Object.defineProperty(exports, "paypalWebhook", { enumerable: true, get: function () { return paypal_webhook_1.paypalWebhook; } });
// Initialize Firebase Admin
admin.initializeApp();
// Initialize Express app
const app = (0, express_1.default)();
app.use((0, cors_1.default)({ origin: true }));
app.use(express_1.default.json());
// Subscription plan configurations
exports.SUBSCRIPTION_PLANS = {
    free: {
        id: 'free',
        name: 'Free Plan',
        price: 0,
        credits: { chat: 25, voice: 5 },
        duration: 7, // days
    },
    basic: {
        id: 'basic',
        name: 'Basic Plan',
        price: 15,
        credits: { chat: 13000, voice: 80 },
        duration: 30, // days
    },
    premium: {
        id: 'premium',
        name: 'Premium Plan',
        price: 30,
        credits: { chat: 20000, voice: 300 }, // Transparent limits
        duration: 30, // days
    },
};
// Helper function to verify Firebase Auth token (currently unused but kept for future use)
// const verifyAuth = async (req: any, res: any, next: any) => {
//   try {
//     const token = req.headers.authorization?.split('Bearer ')[1];
//     if (!token) {
//       return res.status(401).json({ error: 'No authorization token provided' });
//     }
//     const decodedToken = await admin.auth().verifyIdToken(token);
//     req.user = decodedToken;
//     next();
//   } catch (error) {
//     console.error('Auth verification error:', error);
//     return res.status(401).json({ error: 'Invalid authorization token' });
//   }
// };
// Helper function to get user subscription data
const getUserSubscription = async (userId) => {
    const userDoc = await admin.firestore().collection('users').doc(userId).get();
    if (!userDoc.exists) {
        throw new Error('User not found');
    }
    return userDoc.data();
};
// Helper function to update user credits
const updateUserCredits = async (userId, chatUsed, voiceUsed) => {
    const userRef = admin.firestore().collection('users').doc(userId);
    await userRef.update({
        'credits.chat.used': admin.firestore.FieldValue.increment(chatUsed),
        'credits.voice.used': admin.firestore.FieldValue.increment(voiceUsed),
        'usage.totalChatPrompts': admin.firestore.FieldValue.increment(chatUsed),
        'usage.totalVoicePrompts': admin.firestore.FieldValue.increment(voiceUsed),
        'usage.lastChatAt': chatUsed > 0 ? admin.firestore.FieldValue.serverTimestamp() : null,
        'usage.lastVoiceAt': voiceUsed > 0 ? admin.firestore.FieldValue.serverTimestamp() : null,
    });
};
// Webhook routes - these will be handled by separate webhook functions
app.post('/stripe-webhook', (req, res) => {
    res.status(200).send('Use the stripeWebhook Cloud Function instead');
});
app.post('/paypal-webhook', (req, res) => {
    res.status(200).send('Use the paypalWebhook Cloud Function instead');
});
// LemonSqueezy Webhook Handler
app.post('/lemonsqueezy-webhook', async (req, res) => {
    try {
        console.log('LemonSqueezy webhook received:', req.body);
        // Verify webhook signature (implement when LemonSqueezy is configured)
        // const signature = req.headers['x-signature'];
        // if (!verifyLemonSqueezySignature(req.body, signature)) {
        //   return res.status(401).json({ error: 'Invalid signature' });
        // }
        const { event_name, data } = req.body;
        switch (event_name) {
            case 'subscription_created':
                await handleSubscriptionCreated(data);
                break;
            case 'subscription_updated':
                await handleSubscriptionUpdated(data);
                break;
            case 'subscription_cancelled':
                await handleSubscriptionCancelled(data);
                break;
            case 'subscription_resumed':
                await handleSubscriptionResumed(data);
                break;
            default:
                console.log('Unhandled webhook event:', event_name);
        }
        res.status(200).json({ received: true });
    }
    catch (error) {
        console.error('Webhook error:', error);
        res.status(500).json({ error: 'Webhook processing failed' });
    }
});
// Handle subscription created
const handleSubscriptionCreated = async (data) => {
    const { customer_id, variant_id } = data.attributes;
    // Map variant_id to subscription plan
    const plan = mapVariantToPlan(variant_id);
    if (!plan) {
        throw new Error('Unknown subscription variant');
    }
    // Find user by customer_id (you'll need to store this mapping)
    const userId = await findUserByCustomerId(customer_id);
    if (!userId) {
        throw new Error('User not found for customer_id');
    }
    // Update user subscription
    const planConfig = exports.SUBSCRIPTION_PLANS[plan];
    const now = new Date();
    const endDate = new Date(now.getTime() + planConfig.duration * 24 * 60 * 60 * 1000);
    await admin.firestore().collection('users').doc(userId).update({
        'subscription.plan': plan,
        'subscription.status': 'active',
        'subscription.startDate': admin.firestore.Timestamp.fromDate(now),
        'subscription.endDate': admin.firestore.Timestamp.fromDate(endDate),
        'subscription.subscriptionId': data.id,
        'subscription.paymentPlatform': 'lemonsqueezy',
        'credits.chat.limit': planConfig.credits.chat,
        'credits.chat.used': 0,
        'credits.voice.limit': planConfig.credits.voice,
        'credits.voice.used': 0,
        'credits.resetDate': admin.firestore.Timestamp.fromDate(endDate),
        'payment.lemonsqueezy.customerId': customer_id,
        'payment.lemonsqueezy.subscriptionId': data.id,
    });
    console.log(`Subscription created for user ${userId}: ${plan}`);
};
// Handle subscription updated
const handleSubscriptionUpdated = async (data) => {
    // Implementation for subscription updates
    console.log('Subscription updated:', data);
};
// Handle subscription cancelled
const handleSubscriptionCancelled = async (data) => {
    const userId = await findUserBySubscriptionId(data.id);
    if (!userId)
        return;
    await admin.firestore().collection('users').doc(userId).update({
        'subscription.status': 'cancelled',
        'subscription.autoRenew': false,
    });
    console.log(`Subscription cancelled for user ${userId}`);
};
// Handle subscription resumed
const handleSubscriptionResumed = async (data) => {
    const userId = await findUserBySubscriptionId(data.id);
    if (!userId)
        return;
    await admin.firestore().collection('users').doc(userId).update({
        'subscription.status': 'active',
        'subscription.autoRenew': true,
    });
    console.log(`Subscription resumed for user ${userId}`);
};
// Helper functions (to be implemented)
const mapVariantToPlan = (variantId) => {
    // Map LemonSqueezy variant IDs to plan names
    const variantMap = {
    // 'variant_123': 'basic',
    // 'variant_456': 'premium',
    };
    return variantMap[variantId] || null;
};
const findUserByCustomerId = async (customerId) => {
    const query = await admin.firestore()
        .collection('users')
        .where('payment.lemonsqueezy.customerId', '==', customerId)
        .limit(1)
        .get();
    return query.empty ? null : query.docs[0].id;
};
const findUserBySubscriptionId = async (subscriptionId) => {
    const query = await admin.firestore()
        .collection('users')
        .where('payment.lemonsqueezy.subscriptionId', '==', subscriptionId)
        .limit(1)
        .get();
    return query.empty ? null : query.docs[0].id;
};
// Export the webhook handler
exports.lemonsqueezyWebhook = functions.https.onRequest(app);
// Chat API with credit deduction
exports.chatAPI = functions.https.onCall(async (data, context) => {
    var _a, _b, _c, _d, _e;
    // Verify authentication
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const userId = context.auth.uid;
    const { message, conversationHistory } = data;
    try {
        // Get user subscription
        const userData = await getUserSubscription(userId);
        const chatRemaining = ((_b = (_a = userData === null || userData === void 0 ? void 0 : userData.credits) === null || _a === void 0 ? void 0 : _a.chat) === null || _b === void 0 ? void 0 : _b.remaining) || 0;
        // Check if user has credits
        if (chatRemaining <= 0) {
            throw new functions.https.HttpsError('resource-exhausted', 'No chat credits remaining');
        }
        // Check if subscription is active
        const subscriptionEndDate = (_d = (_c = userData === null || userData === void 0 ? void 0 : userData.subscription) === null || _c === void 0 ? void 0 : _c.endDate) === null || _d === void 0 ? void 0 : _d.toDate();
        if (!subscriptionEndDate || new Date() > subscriptionEndDate) {
            throw new functions.https.HttpsError('permission-denied', 'Subscription expired');
        }
        // Process chat request with OpenAI (implement OpenAI integration)
        const response = await processOpenAIRequest(message, conversationHistory);
        // Deduct credit
        await updateUserCredits(userId, 1, 0);
        // Log usage
        await admin.firestore().collection('usage_logs').add({
            userId,
            type: 'chat',
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            creditsUsed: 1,
            plan: ((_e = userData === null || userData === void 0 ? void 0 : userData.subscription) === null || _e === void 0 ? void 0 : _e.plan) || 'free',
            success: true,
        });
        return { response, creditsRemaining: chatRemaining - 1 };
    }
    catch (error) {
        console.error('Chat API error:', error);
        // Log error
        await admin.firestore().collection('usage_logs').add({
            userId,
            type: 'chat',
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            creditsUsed: 0,
            plan: 'unknown',
            success: false,
            error: error instanceof Error ? error.message : String(error),
        });
        throw error;
    }
});
// Voice API with credit deduction
exports.voiceAPI = functions.https.onCall(async (data, context) => {
    var _a, _b;
    // Similar implementation to chatAPI but for voice processing
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const userId = context.auth.uid;
    const { text } = data;
    try {
        const userData = await getUserSubscription(userId);
        const voiceRemaining = ((_b = (_a = userData === null || userData === void 0 ? void 0 : userData.credits) === null || _a === void 0 ? void 0 : _a.voice) === null || _b === void 0 ? void 0 : _b.remaining) || 0;
        if (voiceRemaining <= 0) {
            throw new functions.https.HttpsError('resource-exhausted', 'No voice credits remaining');
        }
        // Process voice request with ElevenLabs (implement ElevenLabs integration)
        const audioUrl = await processElevenLabsRequest(text);
        // Deduct credit
        await updateUserCredits(userId, 0, 1);
        return { audioUrl, creditsRemaining: voiceRemaining - 1 };
    }
    catch (error) {
        console.error('Voice API error:', error);
        throw error;
    }
});
// Placeholder functions (to be implemented)
const processOpenAIRequest = async (message, history) => {
    // Implement OpenAI API call
    return 'Mock response from OpenAI';
};
const processElevenLabsRequest = async (text) => {
    // Implement ElevenLabs API call
    return 'https://mock-audio-url.com/audio.mp3';
};
// Admin function to delete user from Firebase Auth
exports.deleteUserFromAuth = functions.https.onCall(async (data, context) => {
    var _a, _b, _c, _d;
    // Verify authentication
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const { userId, adminEmail } = data;
    if (!userId) {
        throw new functions.https.HttpsError('invalid-argument', 'userId is required');
    }
    try {
        // Verify admin access by checking if the calling user is an admin
        const callerUid = context.auth.uid;
        const callerDoc = await admin.firestore().collection('users').doc(callerUid).get();
        if (!callerDoc.exists) {
            throw new functions.https.HttpsError('permission-denied', 'Caller user document not found');
        }
        const callerData = callerDoc.data();
        const isAdmin = (callerData === null || callerData === void 0 ? void 0 : callerData.isAdmin) === true || (callerData === null || callerData === void 0 ? void 0 : callerData.role) === 'admin';
        if (!isAdmin) {
            throw new functions.https.HttpsError('permission-denied', 'Only admins can delete users');
        }
        // Get user data before deletion for logging
        let userEmail = 'unknown';
        try {
            const userRecord = await admin.auth().getUser(userId);
            userEmail = userRecord.email || 'unknown';
        }
        catch (error) {
            console.log(`User ${userId} not found in Firebase Auth, may have been already deleted`);
        }
        // Delete user from Firebase Authentication
        try {
            await admin.auth().deleteUser(userId);
            console.log(`Successfully deleted user ${userId} (${userEmail}) from Firebase Auth`);
        }
        catch (error) {
            console.log(`Error deleting user ${userId} from Firebase Auth:`, error);
            // Don't throw error if user doesn't exist in Auth
            if (error.code !== 'auth/user-not-found') {
                throw error;
            }
        }
        // Log the admin action
        await admin.firestore().collection('admin_logs').add({
            action: 'delete_user_from_auth',
            description: `Deleted user ${userId} (${userEmail}) from Firebase Authentication`,
            userId: userId,
            userEmail: userEmail,
            deletedBy: adminEmail || ((_b = (_a = context.auth) === null || _a === void 0 ? void 0 : _a.token) === null || _b === void 0 ? void 0 : _b.email) || 'unknown',
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
        });
        return {
            success: true,
            message: `User ${userId} (${userEmail}) deleted from Firebase Auth successfully`
        };
    }
    catch (error) {
        console.error('Error in deleteUserFromAuth:', error);
        // Log the failed attempt
        await admin.firestore().collection('admin_logs').add({
            action: 'delete_user_from_auth_failed',
            description: `Failed to delete user ${userId} from Firebase Authentication: ${error instanceof Error ? error.message : String(error)}`,
            userId: userId,
            deletedBy: adminEmail || ((_d = (_c = context.auth) === null || _c === void 0 ? void 0 : _c.token) === null || _d === void 0 ? void 0 : _d.email) || 'unknown',
            error: error instanceof Error ? error.message : String(error),
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
        });
        throw new functions.https.HttpsError('internal', `Failed to delete user: ${error instanceof Error ? error.message : String(error)}`);
    }
});
//# sourceMappingURL=index.js.map