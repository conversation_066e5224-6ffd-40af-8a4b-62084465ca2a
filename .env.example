# =============================================================================
# FLUTTER FLIGHT FEAR WELLNESS APP - ENVIRONMENT VARIABLES TEMPLATE
# =============================================================================
# 
# PURPOSE: Template file showing required environment variables for the app.
# USAGE: Copy this file to .env and fill in your actual API keys and credentials.
# SECURITY: This template file can be safely committed to version control.
#
# SETUP INSTRUCTIONS:
# 1. Copy this file: cp .env.example .env
# 2. Replace all placeholder values with your actual credentials
# 3. Never commit the .env file to version control
# =============================================================================

# =============================================================================
# AI & VOICE API CREDENTIALS
# =============================================================================

# OpenAI API Key for chat and voice processing
# Get from: https://platform.openai.com/api-keys
# Format: sk-proj-... (project key) or sk-... (legacy key)
OPENAI_API_KEY=your_openai_api_key_here

# ElevenLabs API Key for voice synthesis
# Get from: https://elevenlabs.io/app/settings/api-keys
# Format: sk_...
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# =============================================================================
# PAYMENT PROVIDER CREDENTIALS
# =============================================================================

# LemonSqueezy Configuration
# Get from: https://app.lemonsqueezy.com/settings/api
LEMONSQUEEZY_API_KEY=your_lemonsqueezy_api_key_here
LEMONSQUEEZY_STORE_ID=your_lemonsqueezy_store_id_here
LEMONSQUEEZY_WEBHOOK_SECRET=your_lemonsqueezy_webhook_secret_here
LEMONSQUEEZY_BASIC_PLAN_ID=your_basic_plan_variant_id_here
LEMONSQUEEZY_PREMIUM_PLAN_ID=your_premium_plan_variant_id_here

# Stripe Configuration (Optional)
# Get from: https://dashboard.stripe.com/apikeys
STRIPE_SECRET_KEY=your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here
STRIPE_BASIC_PRICE_ID=your_stripe_basic_price_id_here
STRIPE_PREMIUM_PRICE_ID=your_stripe_premium_price_id_here

# PayPal Configuration (Optional)
# Get from: https://developer.paypal.com/developer/applications/
PAYPAL_CLIENT_ID=your_paypal_client_id_here
PAYPAL_CLIENT_SECRET=your_paypal_client_secret_here
PAYPAL_WEBHOOK_ID=your_paypal_webhook_id_here
PAYPAL_BASIC_PLAN_ID=your_paypal_basic_plan_id_here
PAYPAL_PREMIUM_PLAN_ID=your_paypal_premium_plan_id_here

# =============================================================================
# WEBHOOK CONFIGURATION
# =============================================================================

# General webhook secret for payment processing
# Used by webhook_receiver.dart for signature verification
WEBHOOK_SECRET=your_webhook_secret_here

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================

# Set to 'true' for test mode, 'false' for production
PAYMENT_TEST_MODE=true

# App environment: development, staging, production
APP_ENVIRONMENT=development
