import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/services.dart';
import 'package:flutter_tts/flutter_tts.dart';

import 'breathing_event.dart';
import 'breathing_state.dart';

class BreathingBloc extends Bloc<BreathingEvent, BreathingState> {
  Timer? _timer;
  int _currentCycle = 0;
  BreathingPattern _currentPattern = BreathingPattern.fourSevenEight;
  BreathingPatternConfig get _patternConfig => BreathingPatternConfig.patterns[_currentPattern]!;

  final FlutterTts _tts = FlutterTts();
  bool _ttsInitialized = false;

  BreathingBloc() : super(BreathingInitial()) {
    on<StartBreathingExercise>(_onStart);
    on<PauseBreathingExercise>(_onPause);
    on<ResumeBreathingExercise>(_onResume);
    on<ResetBreathingExercise>(_onReset);
    on<BreathingCycleTick>(_onTick);
    on<PreparationCountdownTick>(_onPreparationTick);
    on<SelectBreathingPattern>(_onSelectPattern);
    _initializeTts();
  }

  Future<void> _initializeTts() async {
    if (_ttsInitialized) return;

    print('TTS: Starting initialization...');

    try {
      await _tts.setLanguage('en-US');


      await _tts.setSpeechRate(0.35);
      await _tts.setVolume(0.85);
      await _tts.setPitch(0.85);

      try {
        await _tts.setSharedInstance(true);
      } catch (e) {
      }

      _ttsInitialized = true;
      print('TTS initialized successfully');
    } catch (e) {
      print('TTS initialization failed: $e');
      _ttsInitialized = false;
    }
  }

  Future<void> _onStart(
    StartBreathingExercise event,
    Emitter<BreathingState> emit,
  ) async {
    _currentPattern = event.pattern;
    _currentCycle = 0;

    emit(BreathingPreparing(
      countdownSeconds: 5,
      pattern: _currentPattern,
      message: "Use headphones for the best experience. Your breathing exercise will begin in...",
    ));

    _startPreparationTimer();
  }

  Future<void> _onSelectPattern(
    SelectBreathingPattern event,
    Emitter<BreathingState> emit,
  ) async {
    _currentPattern = event.pattern;
    emit(BreathingInitial());
  }

  Future<void> _speakGuidance(String message) async {
    print('TTS: Attempting to speak: "$message"');
    print('TTS: Initialized: $_ttsInitialized');

    if (message.isEmpty) {
      print('TTS: Silent period - no speech');
      return;
    }

    if (!_ttsInitialized) {
      print('TTS: Not initialized, attempting to initialize...');
      await _initializeTts();
      if (!_ttsInitialized) {
        print('TTS: Failed to initialize');
        return;
      }
    }

    try {
      print('TTS: Speaking message...');
      await Future.delayed(const Duration(milliseconds: 300));
      await _tts.speak(message);
      print('TTS: Message spoken successfully');
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      print('TTS: Error speaking: $e');
    }
  }

  void _startPreparationTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state is BreathingPreparing) {
        add(PreparationCountdownTick());
      } else {
        timer.cancel();
      }
    });
  }

  Future<void> _onPreparationTick(
    PreparationCountdownTick event,
    Emitter<BreathingState> emit,
  ) async {
    if (state is! BreathingPreparing) return;

    final currentState = state as BreathingPreparing;
    final nextSeconds = currentState.countdownSeconds - 1;

    if (nextSeconds <= 0) {
      _timer?.cancel();

      await _speakGuidance(_patternConfig.guidanceMessages[0]);

      emit(BreathingInProgress(
        cycle: _currentCycle,
        totalCycles: _patternConfig.totalCycles,
        phase: BreathingPhase.inhale,
        remainingSeconds: _patternConfig.inhaleSeconds,
        pattern: _currentPattern,
        currentGuidanceMessage: _patternConfig.guidanceMessages[1],
      ));

      await _speakGuidance(_patternConfig.guidanceMessages[1]);
      _startTimer(_patternConfig.inhaleSeconds);
    } else {
      emit(BreathingPreparing(
        countdownSeconds: nextSeconds,
        pattern: currentState.pattern,
        message: "Ready yourself...",
      ));

    }
  }

  Future<void> _onPause(
    PauseBreathingExercise event,
    Emitter<BreathingState> emit,
  ) async {
    _timer?.cancel();
    if (state is BreathingInProgress) {
      final currentState = state as BreathingInProgress;
      emit(currentState.copyWith(isPaused: true));
    }
  }

  Future<void> _onResume(
    ResumeBreathingExercise event,
    Emitter<BreathingState> emit,
  ) async {
    if (state is BreathingInProgress) {
      final currentState = state as BreathingInProgress;
      emit(currentState.copyWith(isPaused: false));
      _startTimer(currentState.remainingSeconds);
    }
  }

  Future<void> _onReset(
    ResetBreathingExercise event,
    Emitter<BreathingState> emit,
  ) async {
    _timer?.cancel();
    emit(BreathingInitial());
  }

  Future<void> _onTick(
    BreathingCycleTick event,
    Emitter<BreathingState> emit,
  ) async {
    if (state is! BreathingInProgress) return;

    final currentState = state as BreathingInProgress;

    if (currentState.isPaused) return;

    int nextSeconds = currentState.remainingSeconds - 1;
    BreathingPhase nextPhase = currentState.phase;
    String? nextGuidanceMessage;

    if (nextSeconds <= 0) {
      _timer?.cancel();

      switch (currentState.phase) {
        case BreathingPhase.inhale:
          nextPhase = BreathingPhase.hold;
          nextSeconds = _patternConfig.holdAfterInhaleSeconds;
          nextGuidanceMessage = _patternConfig.guidanceMessages[2];
          await _speakGuidance(nextGuidanceMessage);
          HapticFeedback.mediumImpact();
          break;

        case BreathingPhase.hold:
          nextPhase = BreathingPhase.exhale;
          nextSeconds = _patternConfig.exhaleSeconds;
          nextGuidanceMessage = _patternConfig.guidanceMessages[3];
          await _speakGuidance(nextGuidanceMessage);
          HapticFeedback.mediumImpact();
          break;

        case BreathingPhase.exhale:
          if (_patternConfig.holdAfterExhaleSeconds > 0) {
            nextPhase = BreathingPhase.holdAfterExhale;
            nextSeconds = _patternConfig.holdAfterExhaleSeconds;
            nextGuidanceMessage = _patternConfig.guidanceMessages[4];
          } else {
            if (_currentCycle >= _patternConfig.totalCycles - 1) {
              _timer?.cancel();
              await _speakGuidance(_patternConfig.guidanceMessages.last);
              emit(BreathingComplete());
              return;
            }
            _currentCycle++;
            nextPhase = BreathingPhase.inhale;
            nextSeconds = _patternConfig.inhaleSeconds;
            nextGuidanceMessage = _getProgressMessage();
            await _speakGuidance(nextGuidanceMessage);
          }
          HapticFeedback.mediumImpact();
          break;

        case BreathingPhase.holdAfterExhale:
          if (_currentCycle >= _patternConfig.totalCycles - 1) {
            _timer?.cancel();
            await _speakGuidance(_patternConfig.guidanceMessages.last);
            emit(BreathingComplete());
            return;
          }
          _currentCycle++;
          nextPhase = BreathingPhase.inhale;
          nextSeconds = _patternConfig.inhaleSeconds;
          nextGuidanceMessage = _getProgressMessage();
          await _speakGuidance(nextGuidanceMessage);
          HapticFeedback.heavyImpact();
          break;
      }

      emit(currentState.copyWith(
        cycle: _currentCycle,
        phase: nextPhase,
        remainingSeconds: nextSeconds,
        isPaused: false,
        currentGuidanceMessage: nextGuidanceMessage,
      ));

      _startTimer(nextSeconds);
    } else {
      emit(currentState.copyWith(
        remainingSeconds: nextSeconds,
      ));
    }
  }

  String _getProgressMessage() {
    final progress = (_currentCycle + 1) / _patternConfig.totalCycles;
    if (progress <= 0.5) {
      return _patternConfig.guidanceMessages[4];
    } else if (progress <= 0.8) {
      return _patternConfig.guidanceMessages[5];
    } else {
      return _patternConfig.guidanceMessages[6];
    }
  }

  void _startTimer(int seconds) {
    _timer?.cancel();

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state is BreathingInProgress) {
        add(BreathingCycleTick());
      } else {
        timer.cancel();
      }
    });
  }

  @override
  Future<void> close() async {
    _timer?.cancel();
    await _tts.stop();
    return super.close();
  }
}