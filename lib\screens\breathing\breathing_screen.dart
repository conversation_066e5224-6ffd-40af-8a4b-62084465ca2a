import 'package:flight_fear_wellness_app/blocs/breathing/breathing_event.dart';
import 'package:flight_fear_wellness_app/blocs/breathing/breathing_state.dart';
import 'package:flight_fear_wellness_app/utils/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flight_fear_wellness_app/blocs/breathing/breathing_bloc.dart';
import 'package:flight_fear_wellness_app/widgets/custom_button.dart';

class BreathingScreen extends StatelessWidget {
  const BreathingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    final exerciseType = args?['exerciseType'] as String? ?? 'emergency';
    final returnToChat = args?['returnToChat'] as bool? ?? false;
    final userId = args?['userId'] as String?;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _getAppBarTitle(exerciseType),
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.transparent,
        leading: returnToChat ? IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(true),
        ) : null,
      ),
      extendBodyBehindAppBar: true,
      body: BlocProvider(
        create: (context) => BreathingBloc(),
        child: BreathingExerciseView(
          exerciseType: exerciseType,
          returnToChat: returnToChat,
          userId: userId,
        ),
      ),
    );
  }

  String _getAppBarTitle(String? exerciseType) {
    switch (exerciseType) {
      case 'emergency':
        return 'Emergency Calm Breathing';
      case 'calming':
        return 'Calming Breathing';
      default:
        return 'Breathing Exercises';
    }
  }
}

class BreathingExerciseView extends StatefulWidget {
  final String? exerciseType;
  final bool returnToChat;
  final String? userId;

  const BreathingExerciseView({
    super.key,
    this.exerciseType,
    this.returnToChat = false,
    this.userId,
  });

  @override
  State<BreathingExerciseView> createState() => _BreathingExerciseViewState();
}

class _BreathingExerciseViewState extends State<BreathingExerciseView> {
  BreathingPattern _selectedPattern = BreathingPattern.fourSevenEight;

  @override
  void initState() {
    super.initState();
    _selectedPattern = _getPatternForExerciseType(widget.exerciseType);

    if (widget.exerciseType == 'emergency') {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.read<BreathingBloc>().add(StartBreathingExercise(pattern: _selectedPattern));
      });
    }
  }

  BreathingPattern _getPatternForExerciseType(String? exerciseType) {
    switch (exerciseType) {
      case 'emergency':
        return BreathingPattern.boxBreathing;
      case 'calming':
        return BreathingPattern.fourSevenEight;
      default:
        return BreathingPattern.fourSevenEight;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BreathingBloc, BreathingState>(
      builder: (context, state) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppTheme.backgroundColor,
                AppTheme.backgroundColor.withValues(alpha: 0.9),
              ],
            ),
          ),
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 500),
            child: _buildStateContent(context, state),
          ),
        );
      },
    );
  }

  Widget _buildStateContent(BuildContext context, BreathingState state) {
    if (state is BreathingInitial) {
      return _buildPatternSelection(context);
    } else if (state is BreathingPreparing) {
      return _buildPreparationCountdown(context, state);
    } else if (state is BreathingInProgress) {
      return _buildExerciseInProgress(context, state);
    } else {
      return _buildExerciseComplete(context);
    }
  }

  Widget _buildPatternSelection(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 80),
          Image.asset(
            'assets/images/breathing_icon.png',
            height: 120,
            errorBuilder: (context, error, stackTrace) {
              return Icon(
                Icons.air,
                size: 120,
                color: AppTheme.primaryColor,
              );
            },
          ),
          const SizedBox(height: 30),
          const Text(
            'Calm Your Flight Anxiety',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.w700,
              color: AppTheme.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 10),
          const Text(
            'Select a breathing technique to find peace in the skies',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: AppTheme.textSecondary,
            ),
          ),
          const SizedBox(height: 40),

          ...BreathingPattern.values.map((pattern) {
            final config = BreathingPatternConfig.patterns[pattern]!;
            final isSelected = pattern == _selectedPattern;

            return Container(
              margin: const EdgeInsets.only(bottom: 20),
              child: Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                  side: BorderSide(
                    color: isSelected ? AppTheme.primaryColor : Colors.transparent,
                    width: 2,
                  ),
                ),
                child: InkWell(
                  borderRadius: BorderRadius.circular(16),
                  onTap: () {
                    setState(() {
                      _selectedPattern = pattern;
                    });
                    context.read<BreathingBloc>().add(SelectBreathingPattern(pattern));
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor.withValues(alpha: 0.1),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            _getPatternIcon(pattern),
                            color: isSelected ? AppTheme.primaryColor : AppTheme.textSecondary,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                config.name,
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: isSelected ? AppTheme.primaryColor : AppTheme.textPrimary,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                config.description,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: AppTheme.textSecondary,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Pattern: ${config.inhaleSeconds}-${config.holdAfterInhaleSeconds}-${config.exhaleSeconds}${config.holdAfterExhaleSeconds > 0 ? '-${config.holdAfterExhaleSeconds}' : ''} • ${config.totalCycles} cycles',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: AppTheme.textSecondary,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Icon(
                          isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                          color: isSelected ? AppTheme.primaryColor : AppTheme.textSecondary,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }),

          const SizedBox(height: 30),
          CustomButton(
            text: 'Start',
            onPressed: () {
              context.read<BreathingBloc>().add(StartBreathingExercise(pattern: _selectedPattern));
            },
            width: double.infinity,
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildPreparationCountdown(BuildContext context, BreathingPreparing state) {
    final config = BreathingPatternConfig.patterns[state.pattern]!;

    return Container(
      width: double.infinity,
      height: double.infinity,
      color: AppTheme.backgroundColor,
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
          Text(
            config.name,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 40),

          Container(
            padding: const EdgeInsets.all(20),
            margin: const EdgeInsets.symmetric(horizontal: 40),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppTheme.primaryColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.headphones,
                  size: 48,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Use Headphones',
                  style: TextStyle(
                    fontSize: 18,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          const SizedBox(height: 60),

          Container(
            width: 180,
            height: 180,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
              border: Border.all(
                color: AppTheme.primaryColor,
                width: 6,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.primaryColor.withValues(alpha: 0.2),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Center(
              child: Text(
                '${state.countdownSeconds}',
                style: const TextStyle(
                  fontSize: 64,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
          ),

          const SizedBox(height: 30),
          const Text(
            'Ready Yourself',
            style: TextStyle(
              fontSize: 20,
              color: AppTheme.primaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),

          const SizedBox(height: 60),

          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: CustomButton(
              text: 'Cancel',
              onPressed: () {
                context.read<BreathingBloc>().add(ResetBreathingExercise());
              },
              type: ButtonType.outline,
              width: 200,
            ),
          ),
        ],
          ),
        ),
      ),
    );
  }

  Widget _buildExerciseInProgress(BuildContext context, BreathingInProgress state) {
    final config = BreathingPatternConfig.patterns[state.pattern]!;
    final phaseColor = _getPhaseColor(state.phase);
    
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            phaseColor.withValues(alpha: 0.05),
            AppTheme.backgroundColor,
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            config.name,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: phaseColor,
            ),
          ),
          const SizedBox(height: 20),

          BreathingAnimation(phase: state.phase),
          const SizedBox(height: 30),
          
          Text(
            _getPhaseText(state.phase),
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.w700,
              color: phaseColor,
            ),
          ),
          const SizedBox(height: 20),
          
          Text(
            '${state.remainingSeconds}',
            style: TextStyle(
              fontSize: 48,
              fontWeight: FontWeight.w700,
              color: phaseColor,
            ),
          ),
          const SizedBox(height: 10),
          const Text(
            'seconds remaining',
            style: TextStyle(
              fontSize: 16,
              color: AppTheme.textSecondary,
            ),
          ),
          const SizedBox(height: 20),

          if (state.currentGuidanceMessage != null && state.phase != BreathingPhase.holdAfterExhale)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              margin: const EdgeInsets.symmetric(horizontal: 40),
              decoration: BoxDecoration(
                color: phaseColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                state.currentGuidanceMessage!,
                style: TextStyle(
                  fontSize: 16,
                  color: phaseColor,
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ),

          const SizedBox(height: 30),
          Text(
            'Cycle ${state.cycle + 1} of ${state.totalCycles}',
            style: const TextStyle(
              fontSize: 16,
              color: AppTheme.textSecondary,
            ),
          ),
          const SizedBox(height: 40),
          
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: CustomButton(
                    text: state.isPaused ? 'Resume' : 'Pause',
                    onPressed: () {
                      if (state.isPaused) {
                        context.read<BreathingBloc>().add(ResumeBreathingExercise());
                      } else {
                        context.read<BreathingBloc>().add(PauseBreathingExercise());
                      }
                    },
                    type: state.isPaused ? ButtonType.primary : ButtonType.outline,
                    icon: state.isPaused ? Icons.play_arrow : Icons.pause,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomButton(
                    text: 'Reset',
                    onPressed: () {
                      context.read<BreathingBloc>().add(ResetBreathingExercise());
                    },
                    type: ButtonType.outline,
                    icon: Icons.replay,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildExerciseComplete(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle,
              size: 100,
              color: AppTheme.successColor,
            ),
            const SizedBox(height: 30),
            const Text(
              'Exercise Complete!',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.w700,
                color: AppTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'You\'ve successfully calmed your mind and body',
              style: TextStyle(
                fontSize: 18,
                color: AppTheme.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 40),
            if (widget.returnToChat) ...[
              CustomButton(
                text: 'Return to Chat',
                onPressed: () {
                  Navigator.of(context).pop(true);
                },
                width: double.infinity,
              ),
              const SizedBox(height: 20),
              CustomButton(
                text: 'Practice Again',
                onPressed: () {
                  context.read<BreathingBloc>().add(StartBreathingExercise(pattern: _selectedPattern));
                },
                type: ButtonType.outline,
                width: double.infinity,
              ),
            ] else ...[
              CustomButton(
                text: 'Practice Again',
                onPressed: () {
                  context.read<BreathingBloc>().add(StartBreathingExercise());
                },
                width: double.infinity,
              ),
              const SizedBox(height: 20),
              CustomButton(
                text: 'Return to Exercises',
                onPressed: () {
                  context.read<BreathingBloc>().add(ResetBreathingExercise());
                },
                type: ButtonType.outline,
                width: double.infinity,
              ),
            ],
          ],
        ),
      ),
    );
  }

  IconData _getPatternIcon(BreathingPattern pattern) {
    switch (pattern) {
      case BreathingPattern.fourSevenEight:
        return Icons.nightlight_round;
      case BreathingPattern.boxBreathing:
        return Icons.square;
      case BreathingPattern.deepCalming:
        return Icons.water_drop;
    }
  }

  String _getPhaseText(BreathingPhase phase) {
    switch (phase) {
      case BreathingPhase.inhale:
        return 'Breathe In Deeply';
      case BreathingPhase.hold:
        return 'Hold Your Breath';
      case BreathingPhase.exhale:
        return 'Breathe Out Slowly';
      case BreathingPhase.holdAfterExhale:
        return 'Rest & Relax';
    }
  }

  Color _getPhaseColor(BreathingPhase phase) {
    switch (phase) {
      case BreathingPhase.inhale:
        return AppTheme.primaryColor;
      case BreathingPhase.hold:
        return AppTheme.secondaryColor;
      case BreathingPhase.exhale:
        return AppTheme.successColor;
      case BreathingPhase.holdAfterExhale:
        return AppTheme.textSecondary;
    }
  }
}

class BreathingAnimation extends StatefulWidget {
  final BreathingPhase phase;

  const BreathingAnimation({super.key, required this.phase});

  @override
  State<BreathingAnimation> createState() => _BreathingAnimationState();
}

class _BreathingAnimationState extends State<BreathingAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);
  }

  @override
  void didUpdateWidget(covariant BreathingAnimation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.phase != widget.phase) {
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Color circleColor = _getPhaseColor(widget.phase);
    double minScale = 0.8;
    double maxScale = 1.2;

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        double scale;
        switch (widget.phase) {
          case BreathingPhase.inhale:
            scale = minScale + (maxScale - minScale) * _controller.value;
            break;
          case BreathingPhase.exhale:
            scale = maxScale - (maxScale - minScale) * _controller.value;
            break;
          case BreathingPhase.hold:
          case BreathingPhase.holdAfterExhale:
            scale = minScale + (maxScale - minScale) * 0.5;
            break;
        }

        return Transform.scale(
          scale: scale,
          child: Container(
            width: 240,
            height: 240,
            decoration: BoxDecoration(
              color: circleColor.withValues(alpha: 0.05),
              shape: BoxShape.circle,
              border: Border.all(
                color: circleColor.withValues(alpha: 0.5),
                width: 4,
              ),
              boxShadow: [
                BoxShadow(
                  color: circleColor.withValues(alpha: 0.1),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Center(
              child: Container(
                width: 180,
                height: 180,
                decoration: BoxDecoration(
                  color: circleColor.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: circleColor.withValues(alpha: 0.3),
                    width: 3,
                  ),
                ),
                child: Center(
                  child: Icon(
                    _getPhaseIcon(),
                    size: 60,
                    color: circleColor,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  IconData _getPhaseIcon() {
    switch (widget.phase) {
      case BreathingPhase.inhale:
        return Icons.arrow_downward;
      case BreathingPhase.hold:
        return Icons.pause;
      case BreathingPhase.exhale:
        return Icons.arrow_upward;
      case BreathingPhase.holdAfterExhale:
        return Icons.self_improvement;
    }
  }

  Color _getPhaseColor(BreathingPhase phase) {
    switch (phase) {
      case BreathingPhase.inhale:
        return AppTheme.primaryColor;
      case BreathingPhase.hold:
        return AppTheme.secondaryColor;
      case BreathingPhase.exhale:
        return AppTheme.successColor;
      case BreathingPhase.holdAfterExhale:
        return AppTheme.textSecondary;
    }
  }
}