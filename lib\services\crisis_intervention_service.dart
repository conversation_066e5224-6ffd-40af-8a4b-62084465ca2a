import 'package:flutter/material.dart';

class CrisisInterventionService {
  
  static bool isCrisisLevel(String message, List<dynamic> conversationHistory) {
    final lowerMessage = message.toLowerCase();

    final crisisKeywords = [
      'panic attack', 'can\'t breathe', 'heart racing', 'dying',
      'help me', 'emergency', 'scared to death', 'breakdown',
      'overwhelming', 'unbearable', 'losing control', 'hyperventilating',
      'terrified', 'panic', 'crisis', 'turbulence', 'shaking',
      'crashing', 'crash', 'plane crash', 'going down', 'falling',
      'engine failure', 'emergency landing', 'mayday', 'disaster',
      'catastrophe', 'doomed', 'fatal', 'plummeting', 'nosedive',
      'mechanical failure', 'air pocket'
    ];
    
    if (crisisKeywords.any((keyword) => lowerMessage.contains(keyword))) {
      return true;
    }
    
    if (conversationHistory.length >= 3) {
      final recentMessages = conversationHistory
          .where((msg) => msg['role'] == 'user')
          .take(3)
          .toList();
      
      final anxietyCount = recentMessages.where((msg) {
        final content = msg['content'].toString().toLowerCase();
        return ['anxious', 'scared', 'afraid', 'panic', 'worry'].any(
          (keyword) => content.contains(keyword)
        );
      }).length;
      
      return anxietyCount >= 2;
    }
    
    return false;
  }
  
  static List<CrisisInterventionOption> getCrisisOptions(String moodLevel) {
    switch (moodLevel) {
      case 'severely_distressed':
        return [
          CrisisInterventionOption(
            type: CrisisInterventionType.cabinCrew,
            title: 'Talk to Cabin Crew',
            description: 'Connect with flight staff for immediate assistance',
            icon: Icons.support_agent,
            color: Colors.blue,
          ),
          CrisisInterventionOption(
            type: CrisisInterventionType.breathingExercise,
            title: 'Emergency Breathing Exercise',
            description: 'Start a guided breathing session to calm down',
            icon: Icons.air,
            color: Colors.green,
          ),
        ];
      case 'anxious':
        return [
          CrisisInterventionOption(
            type: CrisisInterventionType.breathingExercise,
            title: 'Calming Breathing Exercise',
            description: 'Try a gentle breathing exercise to relax',
            icon: Icons.air,
            color: Colors.green,
          ),
        ];
      default:
        return [];
    }
  }
  
  static String getBreathingExerciseType(String moodLevel) {
    switch (moodLevel) {
      case 'severely_distressed':
        return 'emergency';
      case 'anxious':
        return 'calming';
      default:
        return 'standard';
    }
  }
  
  static String getPostBreathingMessage(String exerciseType) {
    switch (exerciseType) {
      case 'emergency':
        return "How was that emergency breathing session? Are you feeling a bit more grounded now?";
      case 'calming':
        return "How was that breathing exercise? Did it help you feel more relaxed?";
      default:
        return "How was that breathing session? I hope it helped you feel more centered.";
    }
  }
  
  static bool shouldOfferDebate(String message, List<dynamic> conversationHistory) {
    if (conversationHistory.length < 4) return false;
    
    final recentUserMessages = conversationHistory
        .where((msg) => msg['role'] == 'user')
        .take(3)
        .map((msg) => msg['content'].toString().toLowerCase())
        .toList();
    
    final topicKeywords = [
      'think', 'believe', 'opinion', 'disagree', 'agree', 'perspective',
      'view', 'feel about', 'what about', 'but', 'however', 'actually'
    ];
    
    final engagementCount = recentUserMessages.where((msg) =>
      topicKeywords.any((keyword) => msg.contains(keyword))
    ).length;
    
    return engagementCount >= 2;
  }
}

enum CrisisInterventionType {
  cabinCrew,
  breathingExercise,
}

class CrisisInterventionOption {
  final CrisisInterventionType type;
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  
  const CrisisInterventionOption({
    required this.type,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}

class MoodBasedChatResponse {
  final String message;
  final String moodLevel;
  final List<CrisisInterventionOption> interventionOptions;
  final bool shouldOfferDebate;
  final Map<String, dynamic>? metadata;

  const MoodBasedChatResponse({
    required this.message,
    required this.moodLevel,
    this.interventionOptions = const [],
    this.shouldOfferDebate = false,
    this.metadata,
  });
}

class MoodBasedVoiceResponse {
  final String message;
  final String moodLevel;
  final List<CrisisInterventionOption> interventionOptions;
  final bool shouldOfferDebate;
  final String conversationId;
  final int tokensUsed;

  const MoodBasedVoiceResponse({
    required this.message,
    required this.moodLevel,
    this.interventionOptions = const [],
    this.shouldOfferDebate = false,
    required this.conversationId,
    required this.tokensUsed,
  });
}
