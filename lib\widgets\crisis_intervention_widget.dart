import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flight_fear_wellness_app/services/crisis_intervention_service.dart';
import 'package:flight_fear_wellness_app/blocs/chat/chat_bloc.dart';
import 'package:flight_fear_wellness_app/blocs/chat/chat_event.dart';
import 'package:flight_fear_wellness_app/utils/theme.dart';
import 'package:flight_fear_wellness_app/screens/breathing/breathing_screen.dart';

class CrisisInterventionWidget extends StatelessWidget {
  final List<CrisisInterventionOption> options;
  final String userId;
  final String moodLevel;
  final VoidCallback? onClose;

  const CrisisInterventionWidget({
    super.key,
    required this.options,
    required this.userId,
    required this.moodLevel,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    if (options.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.warning_amber_rounded,
                color: Colors.red.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Need immediate help?',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.red.shade700,
                    fontSize: 16,
                  ),
                ),
              ),
              IconButton(
                icon: Icon(
                  Icons.close,
                  color: Colors.red.shade600,
                  size: 20,
                ),
                onPressed: () {
                  if (onClose != null) {
                    onClose!();
                  } else {
                    context.read<ChatBloc>().add(
                      CloseCrisisInterventionEvent(
                        userId: userId,
                      ),
                    );
                  }
                },
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(
                  minWidth: 32,
                  minHeight: 32,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...options.map((option) => _buildOptionButton(context, option)),
        ],
      ),
    );
  }

  Widget _buildOptionButton(BuildContext context, CrisisInterventionOption option) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () => _handleOptionTap(context, option),
        icon: Icon(option.icon, size: 20),
        label: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              option.title,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
            Text(
              option.description,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: option.color.withOpacity(0.1),
          foregroundColor: option.color,
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(color: option.color.withOpacity(0.3)),
          ),
        ),
      ),
    );
  }

  void _handleOptionTap(BuildContext context, CrisisInterventionOption option) {
    context.read<ChatBloc>().add(
      TriggerCrisisInterventionEvent(
        type: option.type,
        userId: userId,
        moodLevel: moodLevel,
      ),
    );

    switch (option.type) {
      case CrisisInterventionType.cabinCrew:
        _showCabinCrewDialog(context);
        break;
      case CrisisInterventionType.breathingExercise:
        _navigateToBreathingExercise(context);
        break;
    }
  }

  void _showCabinCrewDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.support_agent, color: Colors.blue),
            SizedBox(width: 8),
            Text('Contact Cabin Crew'),
          ],
        ),
        content: const Text(
          'Press your call button or approach a flight attendant. '
          'They are trained to help passengers with anxiety and can provide immediate assistance.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  void _navigateToBreathingExercise(BuildContext context) {
    final exerciseType = CrisisInterventionService.getBreathingExerciseType(moodLevel);

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const BreathingScreen(),
      ),
    ).then((result) {
      if (result == true && context.mounted) {
        context.read<ChatBloc>().add(
          ReturnFromBreathingExerciseEvent(
            userId: userId,
            exerciseType: exerciseType,
          ),
        );
      }
    });
  }
}

class DebateOfferWidget extends StatelessWidget {
  final String topic;
  final String userId;

  const DebateOfferWidget({
    super.key,
    required this.topic,
    required this.userId,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.primaryColor.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.forum_rounded,
                color: AppTheme.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Interested in a friendly debate?',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'You seem engaged with this topic. Would you like to explore different perspectives?',
            style: TextStyle(
              color: Colors.grey.shade700,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _acceptDebate(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Yes, let\'s debate!'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton(
                  onPressed: () => _declineDebate(context),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppTheme.primaryColor,
                  ),
                  child: const Text('Maybe later'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _acceptDebate(BuildContext context) {
    context.read<ChatBloc>().add(
      OfferDebateModeEvent(
        userId: userId,
        topic: topic,
      ),
    );
    
    context.read<ChatBloc>().add(
      SendMessageEvent(
        message: 'Yes, I\'d like to debate this topic!',
        userId: userId,
      ),
    );
  }

  void _declineDebate(BuildContext context) {
    context.read<ChatBloc>().add(
      OfferDebateModeEvent(
        userId: userId,
        topic: '',
      ),
    );
  }
}
