import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_memory_model.dart';
import '../models/chat_message.dart';
import '../models/memory_insights_model.dart' as insights;
import '../services/user_memory_service.dart';
import '../models/api_response.dart';

/// Service for analyzing conversations and extracting memory insights
class MemoryAnalysisService {
  final UserMemoryService _memoryService = UserMemoryService();

  /// Analyze conversation messages and extract memory insights
  Future<ApiResponse<void>> analyzeConversation(
    String userId,
    List<ChatMessage> messages, {
    bool isVoiceSession = false,
  }) async {
    try {
      if (messages.isEmpty) return ApiResponse.success(null);

      final memoryResponse = await _memoryService.getUserMemory(userId);
      if (!memoryResponse.success) {
        return ApiResponse.error(memoryResponse.error!);
      }

      final currentMemory = memoryResponse.data!;
      final insights = await _extractInsights(messages);

      // Update memory with extracted insights
      await _updateMemoryWithInsights(userId, currentMemory, insights);

      // Track session engagement metrics
      await _updateSessionMetrics(userId, currentMemory, isVoiceSession);

      return ApiResponse.success(null);
    } catch (e) {
      debugPrint('Error analyzing conversation: $e');
      return ApiResponse.error('Failed to analyze conversation: ${e.toString()}');
    }
  }

  /// Analyze existing chat history to populate user memory
  Future<ApiResponse<void>> analyzeExistingChatHistory(String userId) async {
    try {
      debugPrint('Starting analysis of existing chat history for user: $userId');

      // Get all chat messages for the user
      final firestore = FirebaseFirestore.instance;
      final messagesSnapshot = await firestore
          .collection('chat_messages')
          .where('userId', isEqualTo: userId)
          .orderBy('timestamp', descending: false)
          .get();

      if (messagesSnapshot.docs.isEmpty) {
        debugPrint('No existing chat messages found for user: $userId');
        return ApiResponse.success(null);
      }

      final messages = messagesSnapshot.docs
          .map((doc) => ChatMessage.fromMap(doc.data()))
          .toList();

      debugPrint('Found ${messages.length} existing messages for analysis');

      // Group messages into conversation sessions (by day or conversation breaks)
      final conversationSessions = _groupMessagesIntoSessions(messages);

      debugPrint('Grouped messages into ${conversationSessions.length} conversation sessions');

      // Analyze each session
      for (int i = 0; i < conversationSessions.length; i++) {
        final session = conversationSessions[i];
        debugPrint('Analyzing session ${i + 1} with ${session.length} messages');

        // Determine if this was a voice session based on metadata
        final isVoiceSession = session.any((msg) =>
          msg.metadata?['isVoiceSession'] == true ||
          msg.metadata?['audioUrl'] != null
        );

        await analyzeConversation(userId, session, isVoiceSession: isVoiceSession);
      }

      debugPrint('Completed analysis of existing chat history for user: $userId');
      return ApiResponse.success(null);
    } catch (e) {
      debugPrint('Error analyzing existing chat history: $e');
      return ApiResponse.error('Failed to analyze existing chat history: ${e.toString()}');
    }
  }

  /// Group messages into conversation sessions
  List<List<ChatMessage>> _groupMessagesIntoSessions(List<ChatMessage> messages) {
    if (messages.isEmpty) return [];

    final sessions = <List<ChatMessage>>[];
    List<ChatMessage> currentSession = [];
    DateTime? lastMessageTime;

    for (final message in messages) {
      // Start a new session if:
      // 1. This is the first message
      // 2. More than 3 hours have passed since the last message
      // 3. Current session has more than 50 messages (to prevent overly large sessions)
      if (lastMessageTime == null ||
          message.timestamp.difference(lastMessageTime).inHours > 3 ||
          currentSession.length > 50) {

        if (currentSession.isNotEmpty) {
          sessions.add(List.from(currentSession));
        }
        currentSession = [message];
      } else {
        currentSession.add(message);
      }

      lastMessageTime = message.timestamp;
    }

    // Add the last session if it has messages
    if (currentSession.isNotEmpty) {
      sessions.add(currentSession);
    }

    return sessions;
  }

  /// Extract insights from conversation messages
  Future<ConversationInsights> _extractInsights(List<ChatMessage> messages) async {
    final insights = ConversationInsights();
    
    for (final message in messages) {
      if (message.type == MessageType.user) {
        await _analyzeUserMessage(message.content, insights);
      }
    }

    return insights;
  }

  /// Analyze individual user message for insights
  Future<void> _analyzeUserMessage(String content, ConversationInsights insights) async {
    final lowerContent = content.toLowerCase();

    // Extract phobias and fears
    _extractPhobias(lowerContent, insights);
    
    // Extract aviation-related information
    _extractAviationInfo(lowerContent, insights);
    
    // Extract triggers and anxiety patterns
    _extractTriggers(lowerContent, insights);
    
    // Extract coping mechanisms
    _extractCopingMechanisms(lowerContent, insights);
    
    // Extract personal information
    _extractPersonalInfo(lowerContent, insights);
    
    // Extract mood indicators
    _extractMoodIndicators(lowerContent, insights);
  }

  /// Extract phobias and fears from message
  void _extractPhobias(String content, ConversationInsights insights) {
    final phobiaKeywords = [
      'afraid of', 'scared of', 'fear of', 'phobia', 'terrified of',
      'anxious about', 'worried about', 'panic about', 'dread'
    ];

    final flightFears = [
      'takeoff', 'landing', 'turbulence', 'heights', 'crashing',
      'flying', 'airplane', 'aircraft', 'pilot error', 'mechanical failure'
    ];

    for (final keyword in phobiaKeywords) {
      if (content.contains(keyword)) {
        final index = content.indexOf(keyword);
        final afterKeyword = content.substring(index + keyword.length).trim();
        final words = afterKeyword.split(' ').take(5).join(' ');
        
        if (words.isNotEmpty) {
          insights.phobias.add(words);
        }
      }
    }

    for (final fear in flightFears) {
      if (content.contains(fear)) {
        insights.flightFears[fear] = (insights.flightFears[fear] ?? 0) + 1;
      }
    }
  }

  /// Extract aviation information from message
  void _extractAviationInfo(String content, ConversationInsights insights) {
    // Extract airlines
    final airlinePatterns = [
      r'american airlines?', r'delta', r'united', r'southwest', r'jetblue',
      r'alaska airlines?', r'spirit', r'frontier', r'lufthansa', r'british airways?'
    ];

    for (final pattern in airlinePatterns) {
      final regex = RegExp(pattern, caseSensitive: false);
      if (regex.hasMatch(content)) {
        insights.airlines.add(regex.firstMatch(content)!.group(0)!);
      }
    }

    // Extract airports (simple 3-letter codes)
    final airportPattern = RegExp(r'\b[A-Z]{3}\b');
    final matches = airportPattern.allMatches(content.toUpperCase());
    for (final match in matches) {
      insights.airports.add(match.group(0)!);
    }

    // Extract flight numbers
    final flightPattern = RegExp(r'\b[A-Z]{1,3}\s?\d{1,4}\b');
    final flightMatches = flightPattern.allMatches(content.toUpperCase());
    for (final match in flightMatches) {
      insights.flightNumbers.add(match.group(0)!);
    }
  }

  /// Extract triggers from message
  void _extractTriggers(String content, ConversationInsights insights) {
    final triggerKeywords = [
      'triggers me', 'makes me anxious', 'causes panic', 'sets off',
      'when i think about', 'reminds me of', 'brings back'
    ];

    for (final keyword in triggerKeywords) {
      if (content.contains(keyword)) {
        final index = content.indexOf(keyword);
        final beforeKeyword = content.substring(0, index).split(' ').takeLast(3).join(' ');
        final afterKeyword = content.substring(index + keyword.length).split(' ').take(5).join(' ');
        
        final trigger = '$beforeKeyword $keyword $afterKeyword'.trim();
        if (trigger.length > 10) {
          insights.triggers.add(trigger);
        }
      }
    }
  }

  /// Extract coping mechanisms from message
  void _extractCopingMechanisms(String content, ConversationInsights insights) {
    final copingKeywords = [
      'helps me', 'calms me', 'relaxes me', 'works for me',
      'breathing', 'meditation', 'music', 'distraction'
    ];

    for (final keyword in copingKeywords) {
      if (content.contains(keyword)) {
        final index = content.indexOf(keyword);
        final context = content.substring(
          (index - 20).clamp(0, content.length),
          (index + keyword.length + 20).clamp(0, content.length)
        );
        insights.copingMechanisms.add(context.trim());
      }
    }
  }

  /// Extract personal information from message
  void _extractPersonalInfo(String content, ConversationInsights insights) {
    // Extract age
    final agePattern = RegExp(r'i am (\d{1,2})|(\d{1,2}) years old');
    final ageMatch = agePattern.firstMatch(content);
    if (ageMatch != null) {
      final age = int.tryParse(ageMatch.group(1) ?? ageMatch.group(2) ?? '');
      if (age != null && age > 10 && age < 100) {
        insights.age = age;
      }
    }

    // Extract occupation
    final occupationKeywords = [
      'i work as', 'my job is', 'i am a', 'profession', 'career'
    ];

    for (final keyword in occupationKeywords) {
      if (content.contains(keyword)) {
        final index = content.indexOf(keyword);
        final afterKeyword = content.substring(index + keyword.length).trim();
        final occupation = afterKeyword.split(' ').take(3).join(' ');
        if (occupation.isNotEmpty) {
          insights.occupation = occupation;
        }
      }
    }
  }

  /// Extract mood indicators from message
  void _extractMoodIndicators(String content, ConversationInsights insights) {
    final moodKeywords = {
      'anxious': 'high_anxiety',
      'panic': 'panic',
      'calm': 'calm',
      'relaxed': 'relaxed',
      'stressed': 'stressed',
      'worried': 'worried',
      'confident': 'confident',
      'scared': 'scared',
      'terrified': 'terrified',
      'peaceful': 'peaceful'
    };

    for (final entry in moodKeywords.entries) {
      if (content.contains(entry.key)) {
        insights.moodIndicators[entry.value] = 
            (insights.moodIndicators[entry.value] ?? 0) + 1;
      }
    }
  }

  /// Update memory with extracted insights
  Future<void> _updateMemoryWithInsights(
    String userId,
    UserMemoryModel currentMemory,
    ConversationInsights insights,
  ) async {
    // Update personal information
    if (insights.phobias.isNotEmpty || insights.age != null || insights.occupation != null) {
      final updatedPersonalInfo = currentMemory.personalInfo.copyWith(
        phobias: _mergeStringLists(currentMemory.personalInfo.phobias, insights.phobias),
        age: insights.age ?? currentMemory.personalInfo.age,
        occupation: insights.occupation ?? currentMemory.personalInfo.occupation,
      );

      await _memoryService.updatePersonalInfo(userId, updatedPersonalInfo);
    }

    // Update aviation data
    if (insights.airlines.isNotEmpty || insights.airports.isNotEmpty || insights.flightFears.isNotEmpty) {
      final updatedAviationData = currentMemory.aviationData.copyWith(
        preferredAirlines: _mergeStringLists(currentMemory.aviationData.preferredAirlines, insights.airlines),
        flightFears: _mergeIntMaps(currentMemory.aviationData.flightFears, insights.flightFears),
      );

      await _memoryService.updateAviationData(userId, updatedAviationData);
    }

    // Update behavioral patterns
    if (insights.moodIndicators.isNotEmpty || insights.copingMechanisms.isNotEmpty) {
      final updatedPatterns = currentMemory.behavioralPatterns.copyWith(
        moodPatterns: _mergeIntMaps(currentMemory.behavioralPatterns.moodPatterns, insights.moodIndicators),
        preferredCopingMethods: _mergeStringLists(currentMemory.behavioralPatterns.preferredCopingMethods, insights.copingMechanisms),
      );

      await _memoryService.updateBehavioralPatterns(userId, updatedPatterns);
    }
  }

  /// Update session engagement metrics to track user activity
  Future<void> _updateSessionMetrics(
    String userId,
    UserMemoryModel currentMemory,
    bool isVoiceSession,
  ) async {
    try {
      final currentMetrics = Map<String, dynamic>.from(currentMemory.behavioralPatterns.engagementMetrics);

      // Increment session counters
      if (isVoiceSession) {
        currentMetrics['voiceSessions'] = (currentMetrics['voiceSessions'] as int? ?? 0) + 1;
        currentMetrics['lastVoiceSession'] = DateTime.now().millisecondsSinceEpoch;
      } else {
        currentMetrics['chatSessions'] = (currentMetrics['chatSessions'] as int? ?? 0) + 1;
        currentMetrics['lastChatSession'] = DateTime.now().millisecondsSinceEpoch;
      }

      // Update total sessions
      final voiceSessions = currentMetrics['voiceSessions'] as int? ?? 0;
      final chatSessions = currentMetrics['chatSessions'] as int? ?? 0;
      currentMetrics['totalSessions'] = voiceSessions + chatSessions;

      // Update last activity
      currentMetrics['lastActivity'] = DateTime.now().millisecondsSinceEpoch;

      // Update behavioral patterns with new metrics
      final updatedPatterns = currentMemory.behavioralPatterns.copyWith(
        engagementMetrics: currentMetrics,
      );

      await _memoryService.updateBehavioralPatterns(userId, updatedPatterns);

      debugPrint('Updated session metrics - Voice: $voiceSessions, Chat: $chatSessions, Total: ${voiceSessions + chatSessions}');
    } catch (e) {
      debugPrint('Error updating session metrics: $e');
    }
  }

  /// Merge two string lists, avoiding duplicates
  List<String> _mergeStringLists(List<String> existing, List<String> newItems) {
    final merged = List<String>.from(existing);
    for (final item in newItems) {
      if (!merged.contains(item)) {
        merged.add(item);
      }
    }
    return merged;
  }

  /// Merge two integer maps, adding values for existing keys
  Map<String, int> _mergeIntMaps(Map<String, int> existing, Map<String, int> newItems) {
    final merged = Map<String, int>.from(existing);
    for (final entry in newItems.entries) {
      merged[entry.key] = (merged[entry.key] ?? 0) + entry.value;
    }
    return merged;
  }

  /// Generate comprehensive insights for export
  Future<insights.MemoryInsights> generateInsightsForExport(UserMemoryModel memory) async {
    try {
      // Calculate session statistics from engagement metrics and conversation data
      final voiceSessions = memory.behavioralPatterns.engagementMetrics['voiceSessions'] as int? ?? 0;
      final chatSessions = memory.behavioralPatterns.engagementMetrics['chatSessions'] as int? ?? 0;
      final totalSessions = voiceSessions + chatSessions;

      // If engagement metrics are empty, fall back to conversation topics analysis or estimate from chat history
      if (totalSessions == 0) {
        int fallbackChat = 0;
        int fallbackVoice = 0;

        // Try to estimate from conversation topics
        if (memory.behavioralPatterns.conversationTopics.isNotEmpty) {
          final fallbackTotal = memory.behavioralPatterns.conversationTopics.length;
          fallbackVoice = memory.behavioralPatterns.conversationTopics
              .where((topic) => topic.toLowerCase().contains('voice'))
              .length;
          fallbackChat = fallbackTotal - fallbackVoice;
        } else {
          // If no conversation topics, estimate from session frequency or assume at least 1 chat session if user is exporting
          final totalSessionFreq = memory.behavioralPatterns.sessionFrequency.values.fold(0, (total, freq) => total + freq);
          if (totalSessionFreq > 0) {
            fallbackChat = totalSessionFreq; // Assume most sessions are chat
          } else {
            fallbackChat = 1; // Minimum assumption - user must have chatted to be exporting
          }
        }

        debugPrint('Using fallback session counts - Voice: $fallbackVoice, Chat: $fallbackChat');

        return generateInsightsForExport(memory.copyWith(
          behavioralPatterns: memory.behavioralPatterns.copyWith(
            engagementMetrics: {
              ...memory.behavioralPatterns.engagementMetrics,
              'voiceSessions': fallbackVoice,
              'chatSessions': fallbackChat,
              'totalSessions': fallbackVoice + fallbackChat,
            },
          ),
        ));
      }

      // Analyze anxiety patterns from flight history
      final anxietyLevels = memory.aviationData.flightHistory
          .map((exp) => exp.anxietyLevel)
          .where((level) => level > 0)
          .toList();
      final averageAnxiety = anxietyLevels.isEmpty
          ? 0.0
          : anxietyLevels.reduce((a, b) => a + b) / anxietyLevels.length;

      // Detect phobias from conversations and existing data
      final detectedPhobias = <String, int>{};
      detectedPhobias.addAll(memory.aviationData.flightFears);

      // Add phobias from conversation analysis
      for (final topic in memory.behavioralPatterns.conversationTopics) {
        _detectPhobiasFromText(topic, detectedPhobias);
      }

      // Detect triggers from anxiety trigger times
      final detectedTriggers = <String, int>{};
      memory.behavioralPatterns.anxietyTriggerTimes.forEach((trigger, times) {
        detectedTriggers[trigger] = times.length;
      });

      // Analyze successful strategies
      final successfulStrategies = <String, int>{};
      for (final experience in memory.aviationData.flightHistory) {
        for (final strategy in experience.whatWorked) {
          if (experience.anxietyLevel < 6) { // Consider successful if anxiety < 6
            successfulStrategies[strategy] = (successfulStrategies[strategy] ?? 0) + 1;
          }
        }
      }

      // Add successful coping strategies from aviation data
      for (final strategy in memory.aviationData.successfulCopingStrategies) {
        successfulStrategies[strategy] = (successfulStrategies[strategy] ?? 0) + 1;
      }

      // Analyze mood patterns from behavioral data
      final moodPatterns = memory.behavioralPatterns.moodPatterns;

      // Convert flight experiences to insights format
      final flightHistory = memory.aviationData.flightHistory
          .map((exp) => insights.FlightExperience(
                flightDate: exp.flightDate,
                route: '${exp.departureAirport} → ${exp.arrivalAirport}',
                anxietyLevel: exp.anxietyLevel.toDouble(),
                strategiesUsed: exp.whatWorked,
                outcome: exp.anxietyLevel < 5 ? 'Successful' : 'Challenging',
                notes: exp.notes ?? '',
              ))
          .toList();

      // Detect medical notes
      final medicalNotes = <String>[];
      medicalNotes.addAll(memory.personalInfo.medicalConditions);
      for (final topic in memory.behavioralPatterns.conversationTopics) {
        _detectMedicalNotesFromText(topic, medicalNotes);
      }

      // Calculate weekly anxiety trends from real flight history
      final weeklyTrends = <String, double>{};
      final now = DateTime.now();

      // Group flight experiences by week
      for (int i = 0; i < 4; i++) {
        final weekStart = now.subtract(Duration(days: 7 * (i + 1)));
        final weekEnd = now.subtract(Duration(days: 7 * i));
        final weekKey = '${weekStart.month}/${weekStart.day}';

        // Find flights in this week
        final weekFlights = memory.aviationData.flightHistory
            .where((exp) => exp.flightDate.isAfter(weekStart) && exp.flightDate.isBefore(weekEnd))
            .toList();

        if (weekFlights.isNotEmpty) {
          // Calculate average anxiety for this week
          final weekAnxiety = weekFlights
              .map((exp) => exp.anxietyLevel.toDouble())
              .reduce((a, b) => a + b) / weekFlights.length;
          weeklyTrends[weekKey] = weekAnxiety;
        } else {
          // Use overall average if no flights this week
          weeklyTrends[weekKey] = averageAnxiety;
        }
      }

      // Tool usage frequency
      final toolUsage = <String, int>{
        'Breathing Exercises': successfulStrategies['breathing'] ?? 0,
        'Voice Chat': voiceSessions,
        'Text Chat': chatSessions,
        'Calming Audio': successfulStrategies['audio'] ?? 0,
      };

      // Calculate coping success rate
      final totalCopingAttempts = memory.aviationData.flightHistory.length;
      final successfulCoping = memory.aviationData.flightHistory
          .where((exp) => exp.anxietyLevel < 6)
          .length;
      final copingSuccessRate = totalCopingAttempts > 0
          ? successfulCoping / totalCopingAttempts
          : 0.0;

      // Identify improvement areas and strengths
      final improvementAreas = <String>[];
      final strengths = <String>[];

      if (averageAnxiety > 7.0) {
        improvementAreas.add('High anxiety levels during flights');
      }
      if (copingSuccessRate < 0.5) {
        improvementAreas.add('Coping strategy effectiveness');
      }
      if (totalSessions < 5) {
        improvementAreas.add('Regular app engagement');
      }

      if (copingSuccessRate > 0.7) {
        strengths.add('Effective use of coping strategies');
      }
      if (totalSessions > 10) {
        strengths.add('Consistent app engagement');
      }
      if (averageAnxiety < 5.0) {
        strengths.add('Well-managed anxiety levels');
      }

      return insights.MemoryInsights(
        userId: memory.userId,
        generatedAt: DateTime.now(),
        averageAnxietyLevel: averageAnxiety,
        totalSessions: totalSessions,
        voiceSessions: voiceSessions,
        chatSessions: chatSessions,
        detectedPhobias: detectedPhobias,
        detectedTriggers: detectedTriggers,
        successfulStrategies: successfulStrategies,
        moodPatterns: moodPatterns,
        flightHistory: flightHistory,
        medicalNotes: medicalNotes,
        weeklyAnxietyTrends: weeklyTrends,
        toolUsageFrequency: toolUsage,
        copingSuccessRate: copingSuccessRate,
        improvementAreas: improvementAreas,
        strengths: strengths,
      );
    } catch (e) {
      debugPrint('Error generating insights: $e');
      rethrow;
    }
  }

  /// Detect phobias from conversation text
  void _detectPhobiasFromText(String text, Map<String, int> phobias) {
    final phobiaKeywords = {
      'turbulence': ['turbulence', 'bumpy', 'shaking', 'rough flight'],
      'takeoff': ['takeoff', 'taking off', 'departure', 'leaving ground'],
      'landing': ['landing', 'touchdown', 'approaching', 'descent'],
      'heights': ['height', 'altitude', 'high up', 'looking down'],
      'enclosed spaces': ['cramped', 'small space', 'confined', 'claustrophobic'],
      'crowds': ['crowded', 'too many people', 'packed', 'busy'],
      'crashes': ['crash', 'accident', 'disaster', 'emergency'],
    };

    final lowerText = text.toLowerCase();
    phobiaKeywords.forEach((phobia, keywords) {
      for (final keyword in keywords) {
        if (lowerText.contains(keyword)) {
          phobias[phobia] = (phobias[phobia] ?? 0) + 1;
          break;
        }
      }
    });
  }

  /// Detect mood patterns from conversation text
  void _detectMoodFromText(String text, Map<String, int> moods) {
    final moodKeywords = {
      'anxious': ['anxious', 'worried', 'nervous', 'scared', 'afraid'],
      'calm': ['calm', 'relaxed', 'peaceful', 'better', 'good'],
      'panic': ['panic', 'panicking', 'overwhelming', 'can\'t breathe'],
      'confident': ['confident', 'ready', 'prepared', 'strong'],
    };

    final lowerText = text.toLowerCase();
    moodKeywords.forEach((mood, keywords) {
      for (final keyword in keywords) {
        if (lowerText.contains(keyword)) {
          moods[mood] = (moods[mood] ?? 0) + 1;
          break;
        }
      }
    });
  }

  /// Detect medical notes from conversation text
  void _detectMedicalNotesFromText(String text, List<String> medicalNotes) {
    final medicalKeywords = [
      'asthma', 'anxiety disorder', 'panic disorder', 'claustrophobia',
      'medication', 'therapy', 'counseling', 'treatment'
    ];

    final lowerText = text.toLowerCase();
    for (final keyword in medicalKeywords) {
      if (lowerText.contains(keyword) && !medicalNotes.contains(keyword)) {
        medicalNotes.add(keyword);
      }
    }
  }
}

/// Container for insights extracted from conversation
class ConversationInsights {
  final List<String> phobias = [];
  final List<String> airlines = [];
  final List<String> airports = [];
  final List<String> flightNumbers = [];
  final Map<String, int> flightFears = {};
  final List<String> triggers = [];
  final List<String> copingMechanisms = [];
  final Map<String, int> moodIndicators = {};
  int? age;
  String? occupation;
}

extension on List<String> {
  List<String> takeLast(int count) {
    if (length <= count) return this;
    return sublist(length - count);
  }
}
