import 'dart:async';
import 'package:firebase_database/firebase_database.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/subscription_model.dart';
import '../models/payment_models.dart';
import 'notification_service.dart';
import 'payment_manager.dart';

/// Unified webhook processor for all payment providers (LemonSqueezy, Stripe, PayPal)
/// Handles real-time webhook events from Firebase Realtime Database
class WebhookProcessor {
  static final WebhookProcessor _instance = WebhookProcessor._internal();
  factory WebhookProcessor() => _instance;
  WebhookProcessor._internal();

  final FirebaseDatabase _database = FirebaseDatabase.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final NotificationService _notificationService = NotificationService();

  // Subscriptions for each provider
  StreamSubscription<DatabaseEvent>? _lemonSqueezySubscription;
  StreamSubscription<DatabaseEvent>? _stripeSubscription;
  StreamSubscription<DatabaseEvent>? _paypalSubscription;

  bool _isProcessing = false;

  Future<void> initialize() async {
    // Cancel existing subscriptions
    await dispose();

    // Initialize webhook listeners for all providers
    await _initializeLemonSqueezyWebhooks();
    await _initializeStripeWebhooks();
    await _initializePayPalWebhooks();

    debugPrint('WebhookProcessor: Initialized and listening for all provider events');
  }

  Future<void> _initializeLemonSqueezyWebhooks() async {
    try {
      final webhookRef = _database.ref('webhooks/lemonsqueezy');
      _lemonSqueezySubscription = webhookRef.onChildAdded.listen(
        (event) => _processWebhookEvent(event, PaymentProvider.lemonsqueezy),
      );
      debugPrint('WebhookProcessor: LemonSqueezy webhook listener initialized');
    } catch (e) {
      debugPrint('WebhookProcessor: Failed to initialize LemonSqueezy webhooks: $e');
    }
  }

  Future<void> _initializeStripeWebhooks() async {
    try {
      final webhookRef = _database.ref('webhooks/stripe');
      _stripeSubscription = webhookRef.onChildAdded.listen(
        (event) => _processWebhookEvent(event, PaymentProvider.stripe),
      );
      debugPrint('WebhookProcessor: Stripe webhook listener initialized');
    } catch (e) {
      debugPrint('WebhookProcessor: Failed to initialize Stripe webhooks: $e');
    }
  }

  Future<void> _initializePayPalWebhooks() async {
    try {
      final webhookRef = _database.ref('webhooks/paypal');
      _paypalSubscription = webhookRef.onChildAdded.listen(
        (event) => _processWebhookEvent(event, PaymentProvider.paypal),
      );
      debugPrint('WebhookProcessor: PayPal webhook listener initialized');
    } catch (e) {
      debugPrint('WebhookProcessor: Failed to initialize PayPal webhooks: $e');
    }
  }

  Future<void> _processWebhookEvent(DatabaseEvent event, PaymentProvider provider) async {
    if (_isProcessing) {
      debugPrint('WebhookProcessor: Already processing, queuing ${provider.name} event');
      // Add a small delay and retry
      await Future.delayed(const Duration(milliseconds: 100));
      if (!_isProcessing) {
        return _processWebhookEvent(event, provider);
      }
      return;
    }

    try {
      _isProcessing = true;
      final data = event.snapshot.value as Map<dynamic, dynamic>?;

      if (data == null) {
        debugPrint('WebhookProcessor: No data in ${provider.name} event');
        return;
      }

      final processed = data['processed'] as bool? ?? false;
      final processing = data['processing'] as bool? ?? false;

      if (processed) {
        debugPrint('WebhookProcessor: ${provider.name} event already processed');
        return;
      }

      if (processing) {
        debugPrint('WebhookProcessor: ${provider.name} event already being processed');
        return;
      }

      final eventType = data['eventType'] as String?;
      final webhookData = data['data'] as Map<dynamic, dynamic>?;

      if (eventType == null || webhookData == null) {
        debugPrint('WebhookProcessor: Invalid ${provider.name} event data');
        await event.snapshot.ref.update({
          'processed': true,
          'processing': false,
          'error': 'Invalid event data',
          'errorAt': ServerValue.timestamp,
        });
        return;
      }

      debugPrint('WebhookProcessor: Processing ${provider.name} event $eventType');

      // Mark as processing
      await event.snapshot.ref.update({
        'processing': true,
        'processingStartedAt': ServerValue.timestamp,
      });

      // Process the webhook with timeout
      await Future.any([
        _handleWebhookEvent(provider, eventType, webhookData),
        Future.delayed(const Duration(seconds: 30), () => throw Exception('Webhook processing timeout')),
      ]);

      // Mark as completed
      await event.snapshot.ref.update({
        'processed': true,
        'processing': false,
        'processedAt': ServerValue.timestamp,
        'processingDuration': DateTime.now().millisecondsSinceEpoch,
      });

      debugPrint('WebhookProcessor: Successfully processed ${provider.name} $eventType');

      // Send real-time notification to user
      await _sendProcessingNotification(provider, eventType, webhookData);

    } catch (error) {
      debugPrint('WebhookProcessor: Error processing ${provider.name} webhook: $error');

      try {
        final eventData = event.snapshot.value as Map<dynamic, dynamic>?;
        final currentRetryCount = eventData?['retryCount'] as int? ?? 0;
        await event.snapshot.ref.update({
          'processed': false,
          'processing': false,
          'error': error.toString(),
          'errorAt': ServerValue.timestamp,
          'retryCount': currentRetryCount + 1,
        });

        // Schedule retry for transient errors
        if (_shouldRetry(error.toString(), currentRetryCount)) {
          _scheduleRetry(event, provider);
        }
      } catch (updateError) {
        debugPrint('WebhookProcessor: Failed to update error status: $updateError');
      }
    } finally {
      _isProcessing = false;
    }
  }

  bool _shouldRetry(String error, int retryCount) {
    if (retryCount >= 3) return false;

    // Retry on network errors, timeouts, and temporary failures
    return error.contains('timeout') ||
           error.contains('network') ||
           error.contains('connection') ||
           error.contains('temporary');
  }

  void _scheduleRetry(DatabaseEvent event, PaymentProvider provider) {
    Future.delayed(const Duration(minutes: 1), () {
      if (!_isProcessing) {
        _processWebhookEvent(event, provider);
      }
    });
  }

  Future<void> _sendProcessingNotification(PaymentProvider provider, String eventType, Map<dynamic, dynamic> data) async {
    try {
      // Extract user ID from webhook data
      String? userId;

      switch (provider) {
        case PaymentProvider.lemonsqueezy:
          userId = data['meta']?['custom_data']?['user_id'];
          break;
        case PaymentProvider.stripe:
          userId = data['metadata']?['user_id'];
          break;
        case PaymentProvider.paypal:
          userId = data['custom_id'];
          break;
      }

      if (userId != null) {
        await _notificationService.sendPaymentConfirmationNotification(
          userId,
          '${provider.name} Payment',
          0.0, // Amount will be extracted from webhook data if needed
          {}, // Credits will be updated by subscription service
        );
      }
    } catch (e) {
      debugPrint('WebhookProcessor: Failed to send notification: $e');
    }
  }

  Future<void> _handleWebhookEvent(PaymentProvider provider, String eventType, Map<dynamic, dynamic> data) async {
    switch (provider) {
      case PaymentProvider.lemonsqueezy:
        await _handleLemonSqueezyEvent(eventType, data);
        break;
      case PaymentProvider.stripe:
        await _handleStripeEvent(eventType, data);
        break;
      case PaymentProvider.paypal:
        await _handlePayPalEvent(eventType, data);
        break;
    }
  }

  Future<void> _handleLemonSqueezyEvent(String eventType, Map<dynamic, dynamic> data) async {
    switch (eventType) {
      case 'subscription_created':
        await _handleSubscriptionCreated(data, PaymentProvider.lemonsqueezy);
        break;
      case 'subscription_updated':
        await _handleSubscriptionUpdated(data, PaymentProvider.lemonsqueezy);
        break;
      case 'subscription_cancelled':
        await _handleSubscriptionCancelled(data, PaymentProvider.lemonsqueezy);
        break;
      case 'subscription_resumed':
        await _handleSubscriptionResumed(data, PaymentProvider.lemonsqueezy);
        break;
      case 'subscription_expired':
        await _handleSubscriptionExpired(data, PaymentProvider.lemonsqueezy);
        break;
      case 'subscription_payment_success':
        await _handlePaymentSuccess(data, PaymentProvider.lemonsqueezy);
        break;
      case 'subscription_payment_failed':
        await _handlePaymentFailed(data, PaymentProvider.lemonsqueezy);
        break;
      default:
        debugPrint('WebhookProcessor: Unhandled LemonSqueezy event type: $eventType');
    }
  }

  Future<void> _handleStripeEvent(String eventType, Map<dynamic, dynamic> data) async {
    switch (eventType) {
      case 'customer.subscription.created':
        await _handleSubscriptionCreated(data, PaymentProvider.stripe);
        break;
      case 'customer.subscription.updated':
        await _handleSubscriptionUpdated(data, PaymentProvider.stripe);
        break;
      case 'customer.subscription.deleted':
        await _handleSubscriptionCancelled(data, PaymentProvider.stripe);
        break;
      case 'invoice.payment_succeeded':
        await _handlePaymentSuccess(data, PaymentProvider.stripe);
        break;
      case 'invoice.payment_failed':
        await _handlePaymentFailed(data, PaymentProvider.stripe);
        break;
      default:
        debugPrint('WebhookProcessor: Unhandled Stripe event type: $eventType');
    }
  }

  Future<void> _handlePayPalEvent(String eventType, Map<dynamic, dynamic> data) async {
    switch (eventType) {
      case 'BILLING.SUBSCRIPTION.CREATED':
        await _handleSubscriptionCreated(data, PaymentProvider.paypal);
        break;
      case 'BILLING.SUBSCRIPTION.ACTIVATED':
        await _handleSubscriptionUpdated(data, PaymentProvider.paypal);
        break;
      case 'BILLING.SUBSCRIPTION.CANCELLED':
        await _handleSubscriptionCancelled(data, PaymentProvider.paypal);
        break;
      case 'PAYMENT.SALE.COMPLETED':
        await _handlePaymentSuccess(data, PaymentProvider.paypal);
        break;
      default:
        debugPrint('WebhookProcessor: Unhandled PayPal event type: $eventType');
    }
  }

  Future<void> _handleSubscriptionCreated(Map<dynamic, dynamic> data, PaymentProvider provider) async {
    try {
      switch (provider) {
        case PaymentProvider.lemonsqueezy:
          await _handleLemonSqueezySubscriptionCreated(data);
          break;
        case PaymentProvider.stripe:
          await _handleStripeSubscriptionCreated(data);
          break;
        case PaymentProvider.paypal:
          await _handlePayPalSubscriptionCreated(data);
          break;
      }
    } catch (error) {
      debugPrint('WebhookProcessor: Error handling subscription_created for ${provider.name}: $error');
      rethrow;
    }
  }

  Future<void> _handleLemonSqueezySubscriptionCreated(Map<dynamic, dynamic> data) async {
    final attributes = data['data']?['attributes'] as Map<dynamic, dynamic>?;
    if (attributes == null) return;

    final customData = attributes['custom_data'] as Map<dynamic, dynamic>?;
    final userId = customData?['user_id'] as String?;
    final variantId = attributes['variant_id']?.toString();

    if (userId == null || variantId == null) {
      debugPrint('WebhookProcessor: Missing user_id or variant_id in LemonSqueezy subscription_created');
      return;
    }

    final plan = _mapVariantToPlan(variantId);
    if (plan == null) {
      debugPrint('WebhookProcessor: Unknown LemonSqueezy variant ID: $variantId');
      return;
    }

    await _updateUserSubscription(
      userId: userId,
      plan: plan,
      provider: PaymentProvider.lemonsqueezy,
      subscriptionId: data['data']?['id']?.toString(),
      customerId: attributes['customer_id']?.toString(),
      variantId: variantId,
    );

    final planCredits = _getPlanCredits(plan);
    final amount = attributes['total']?.toDouble() ?? 0.0;
    await _notificationService.sendPaymentConfirmationNotification(
      userId,
      plan.name.toUpperCase(),
      amount / 100,
      {
        'chat': planCredits['chat'] ?? 0,
        'voice': planCredits['voice'] ?? 0,
      },
    );

    debugPrint('WebhookProcessor: LemonSqueezy subscription created for user $userId with plan ${plan.name}');
  }

  Future<void> _handleStripeSubscriptionCreated(Map<dynamic, dynamic> data) async {
    final subscriptionId = data['id']?.toString();
    final customerId = data['customer']?.toString();
    final priceId = data['items']?['data']?[0]?['price']?['id']?.toString();

    if (subscriptionId == null || customerId == null || priceId == null) {
      debugPrint('WebhookProcessor: Missing required data in Stripe subscription_created');
      return;
    }

    final plan = _mapStripePriceToPlan(priceId);
    if (plan == null) {
      debugPrint('WebhookProcessor: Unknown Stripe price ID: $priceId');
      return;
    }

    // Find user by customer ID
    final userQuery = await _firestore
        .collection('users')
        .where('payment.stripe.customerId', isEqualTo: customerId)
        .limit(1)
        .get();

    if (userQuery.docs.isEmpty) {
      debugPrint('WebhookProcessor: User not found for Stripe customer: $customerId');
      return;
    }

    final userId = userQuery.docs.first.id;
    await _updateUserSubscription(
      userId: userId,
      plan: plan,
      provider: PaymentProvider.stripe,
      subscriptionId: subscriptionId,
      customerId: customerId,
      priceId: priceId,
    );

    debugPrint('WebhookProcessor: Stripe subscription created for user $userId with plan ${plan.name}');
  }

  Future<void> _handlePayPalSubscriptionCreated(Map<dynamic, dynamic> data) async {
    final subscriptionId = data['id']?.toString();
    final planId = data['plan_id']?.toString();

    if (subscriptionId == null || planId == null) {
      debugPrint('WebhookProcessor: Missing required data in PayPal subscription_created');
      return;
    }

    final plan = _mapPayPalPlanToPlan(planId);
    if (plan == null) {
      debugPrint('WebhookProcessor: Unknown PayPal plan ID: $planId');
      return;
    }

    // Find user by subscription ID (PayPal doesn't provide customer ID in webhook)
    final userQuery = await _firestore
        .collection('users')
        .where('payment.paypal.subscriptionId', isEqualTo: subscriptionId)
        .limit(1)
        .get();

    if (userQuery.docs.isEmpty) {
      debugPrint('WebhookProcessor: User not found for PayPal subscription: $subscriptionId');
      return;
    }

    final userId = userQuery.docs.first.id;
    await _updateUserSubscription(
      userId: userId,
      plan: plan,
      provider: PaymentProvider.paypal,
      subscriptionId: subscriptionId,
      planId: planId,
    );

    debugPrint('WebhookProcessor: PayPal subscription created for user $userId with plan ${plan.name}');
  }

  Future<void> _handleSubscriptionUpdated(Map<dynamic, dynamic> data, PaymentProvider provider) async {
    try {
      switch (provider) {
        case PaymentProvider.lemonsqueezy:
          await _handleLemonSqueezySubscriptionUpdated(data);
          break;
        case PaymentProvider.stripe:
          await _handleStripeSubscriptionUpdated(data);
          break;
        case PaymentProvider.paypal:
          await _handlePayPalSubscriptionUpdated(data);
          break;
      }
    } catch (error) {
      debugPrint('WebhookProcessor: Error handling subscription_updated for ${provider.name}: $error');
      rethrow;
    }
  }

  Future<void> _handleLemonSqueezySubscriptionUpdated(Map<dynamic, dynamic> data) async {
    final subscriptionId = data['data']?['id']?.toString();
    if (subscriptionId == null) return;

    final attributes = data['data']?['attributes'] as Map<dynamic, dynamic>?;
    if (attributes == null) return;

    final userQuery = await _firestore
        .collection('users')
        .where('payment.lemonsqueezy.subscriptionId', isEqualTo: subscriptionId)
        .limit(1)
        .get();

    if (userQuery.docs.isEmpty) {
      debugPrint('WebhookProcessor: User not found for LemonSqueezy subscription $subscriptionId');
      return;
    }

    final userId = userQuery.docs.first.id;
    final status = attributes['status']?.toString() ?? 'active';
    final variantId = attributes['variant_id']?.toString();

    Map<String, dynamic> updateData = {
      'subscription.status': status,
    };

    if (variantId != null) {
      final plan = _mapVariantToPlan(variantId);
      if (plan != null) {
        updateData.addAll({
          'subscription.plan': plan.name,
          'credits.chat.limit': _getPlanCredits(plan)['chat'],
          'credits.voice.limit': _getPlanCredits(plan)['voice'],
          'payment.lemonsqueezy.variantId': variantId,
        });
      }
    }

    await _firestore.collection('users').doc(userId).update(updateData);
    debugPrint('WebhookProcessor: LemonSqueezy subscription updated for user $userId');
  }

  Future<void> _handleStripeSubscriptionUpdated(Map<dynamic, dynamic> data) async {
    final subscriptionId = data['id']?.toString();
    if (subscriptionId == null) return;

    final userQuery = await _firestore
        .collection('users')
        .where('payment.stripe.subscriptionId', isEqualTo: subscriptionId)
        .limit(1)
        .get();

    if (userQuery.docs.isEmpty) {
      debugPrint('WebhookProcessor: User not found for Stripe subscription $subscriptionId');
      return;
    }

    final userId = userQuery.docs.first.id;
    final status = data['status']?.toString() ?? 'active';

    await _firestore.collection('users').doc(userId).update({
      'subscription.status': status,
    });

    debugPrint('WebhookProcessor: Stripe subscription updated for user $userId');
  }

  Future<void> _handlePayPalSubscriptionUpdated(Map<dynamic, dynamic> data) async {
    final subscriptionId = data['id']?.toString();
    if (subscriptionId == null) return;

    final userQuery = await _firestore
        .collection('users')
        .where('payment.paypal.subscriptionId', isEqualTo: subscriptionId)
        .limit(1)
        .get();

    if (userQuery.docs.isEmpty) {
      debugPrint('WebhookProcessor: User not found for PayPal subscription $subscriptionId');
      return;
    }

    final userId = userQuery.docs.first.id;
    final status = data['status']?.toString() ?? 'active';

    await _firestore.collection('users').doc(userId).update({
      'subscription.status': status,
    });

    debugPrint('WebhookProcessor: PayPal subscription updated for user $userId');
  }

  Future<void> _handleSubscriptionCancelled(Map<dynamic, dynamic> data, PaymentProvider provider) async {
    await _updateSubscriptionStatus(data, provider, 'cancelled', autoRenew: false);
  }

  Future<void> _handleSubscriptionResumed(Map<dynamic, dynamic> data, PaymentProvider provider) async {
    await _updateSubscriptionStatus(data, provider, 'active', autoRenew: true);
  }

  Future<void> _handleSubscriptionExpired(Map<dynamic, dynamic> data, PaymentProvider provider) async {
    await _updateSubscriptionStatus(data, provider, 'expired', autoRenew: false);
  }

  Future<void> _handlePaymentSuccess(Map<dynamic, dynamic> data, PaymentProvider provider) async {
    try {
      String? subscriptionId;
      String subscriptionField;

      // Extract subscription ID based on provider
      switch (provider) {
        case PaymentProvider.lemonsqueezy:
          subscriptionId = data['data']?['attributes']?['subscription_id']?.toString();
          subscriptionField = 'payment.lemonsqueezy.subscriptionId';
          break;
        case PaymentProvider.stripe:
          subscriptionId = data['subscription']?.toString();
          subscriptionField = 'payment.stripe.subscriptionId';
          break;
        case PaymentProvider.paypal:
          subscriptionId = data['billing_agreement_id']?.toString();
          subscriptionField = 'payment.paypal.subscriptionId';
          break;
      }

      if (subscriptionId == null) return;

      final userQuery = await _firestore
          .collection('users')
          .where(subscriptionField, isEqualTo: subscriptionId)
          .limit(1)
          .get();

      if (userQuery.docs.isEmpty) {
        debugPrint('WebhookProcessor: User not found for ${provider.name} payment success');
        return;
      }

      final userId = userQuery.docs.first.id;
      final userData = userQuery.docs.first.data();

      final currentEndDate = (userData['subscription']?['endDate'] as Timestamp?)?.toDate() ?? DateTime.now();
      final newEndDate = currentEndDate.add(const Duration(days: 30));

      await _firestore.collection('users').doc(userId).update({
        'subscription.endDate': Timestamp.fromDate(newEndDate),
        'subscription.status': 'active',
        'credits.resetDate': Timestamp.fromDate(newEndDate),
        'payment.lastPaymentAt': FieldValue.serverTimestamp(),
      });

      debugPrint('WebhookProcessor: ${provider.name} payment success - extended subscription for user $userId');

    } catch (error) {
      debugPrint('WebhookProcessor: Error handling ${provider.name} payment_success: $error');
      rethrow;
    }
  }

  Future<void> _handlePaymentFailed(Map<dynamic, dynamic> data, PaymentProvider provider) async {
    try {
      String? subscriptionId;
      String subscriptionField;

      // Extract subscription ID based on provider
      switch (provider) {
        case PaymentProvider.lemonsqueezy:
          subscriptionId = data['data']?['attributes']?['subscription_id']?.toString();
          subscriptionField = 'payment.lemonsqueezy.subscriptionId';
          break;
        case PaymentProvider.stripe:
          subscriptionId = data['subscription']?.toString();
          subscriptionField = 'payment.stripe.subscriptionId';
          break;
        case PaymentProvider.paypal:
          subscriptionId = data['billing_agreement_id']?.toString();
          subscriptionField = 'payment.paypal.subscriptionId';
          break;
      }

      if (subscriptionId == null) return;

      final userQuery = await _firestore
          .collection('users')
          .where(subscriptionField, isEqualTo: subscriptionId)
          .limit(1)
          .get();

      if (userQuery.docs.isEmpty) {
        debugPrint('WebhookProcessor: User not found for ${provider.name} payment failed');
        return;
      }

      final userId = userQuery.docs.first.id;

      await _firestore.collection('users').doc(userId).update({
        'subscription.status': 'past_due',
        'payment.lastFailedAt': FieldValue.serverTimestamp(),
      });

      debugPrint('WebhookProcessor: ${provider.name} payment failed for user $userId');

    } catch (error) {
      debugPrint('WebhookProcessor: Error handling ${provider.name} payment_failed: $error');
      rethrow;
    }
  }

  Future<void> _updateSubscriptionStatus(Map<dynamic, dynamic> data, PaymentProvider provider, String status, {bool? autoRenew}) async {
    try {
      String? subscriptionId;
      String subscriptionField;

      // Extract subscription ID based on provider
      switch (provider) {
        case PaymentProvider.lemonsqueezy:
          subscriptionId = data['data']?['id']?.toString();
          subscriptionField = 'payment.lemonsqueezy.subscriptionId';
          break;
        case PaymentProvider.stripe:
          subscriptionId = data['id']?.toString();
          subscriptionField = 'payment.stripe.subscriptionId';
          break;
        case PaymentProvider.paypal:
          subscriptionId = data['id']?.toString();
          subscriptionField = 'payment.paypal.subscriptionId';
          break;
      }

      if (subscriptionId == null) return;

      final userQuery = await _firestore
          .collection('users')
          .where(subscriptionField, isEqualTo: subscriptionId)
          .limit(1)
          .get();

      if (userQuery.docs.isEmpty) {
        debugPrint('WebhookProcessor: User not found for ${provider.name} subscription $subscriptionId');
        return;
      }

      final userId = userQuery.docs.first.id;

      Map<String, dynamic> updateData = {
        'subscription.status': status,
      };

      if (autoRenew != null) {
        updateData['subscription.autoRenew'] = autoRenew;
      }

      await _firestore.collection('users').doc(userId).update(updateData);
      debugPrint('WebhookProcessor: Updated ${provider.name} subscription status to $status for user $userId');

    } catch (error) {
      debugPrint('WebhookProcessor: Error updating ${provider.name} subscription status: $error');
      rethrow;
    }
  }

  /// Helper method to update user subscription data in Firestore
  Future<void> _updateUserSubscription({
    required String userId,
    required SubscriptionPlan plan,
    required PaymentProvider provider,
    String? subscriptionId,
    String? customerId,
    String? variantId,
    String? priceId,
    String? planId,
  }) async {
    final now = DateTime.now();
    final endDate = now.add(const Duration(days: 30));

    Map<String, dynamic> updateData = {
      'subscription.plan': plan.name,
      'subscription.status': 'active',
      'subscription.startDate': Timestamp.fromDate(now),
      'subscription.endDate': Timestamp.fromDate(endDate),
      'subscription.paymentPlatform': provider.name,
      'subscription.autoRenew': true,
      'credits.chat.limit': _getPlanCredits(plan)['chat'],
      'credits.chat.used': 0,
      'credits.voice.limit': _getPlanCredits(plan)['voice'],
      'credits.voice.used': 0,
      'credits.resetDate': Timestamp.fromDate(endDate),
    };

    // Add provider-specific data
    switch (provider) {
      case PaymentProvider.lemonsqueezy:
        updateData.addAll({
          'payment.lemonsqueezy.customerId': customerId,
          'payment.lemonsqueezy.subscriptionId': subscriptionId,
          'payment.lemonsqueezy.variantId': variantId,
        });
        break;
      case PaymentProvider.stripe:
        updateData.addAll({
          'payment.stripe.customerId': customerId,
          'payment.stripe.subscriptionId': subscriptionId,
          'payment.stripe.priceId': priceId,
        });
        break;
      case PaymentProvider.paypal:
        updateData.addAll({
          'payment.paypal.subscriptionId': subscriptionId,
          'payment.paypal.planId': planId,
        });
        break;
    }

    await _firestore.collection('users').doc(userId).update(updateData);
  }

  SubscriptionPlan? _mapVariantToPlan(String variantId) {
    const variantMapping = {
      '587344': SubscriptionPlan.basic,
      '587351': SubscriptionPlan.premium,
    };

    return variantMapping[variantId];
  }

  SubscriptionPlan? _mapStripePriceToPlan(String priceId) {
    // Get Stripe price mappings from PaymentManager configuration
    final stripeConfig = PaymentManager.instance.getConfig(PaymentProvider.stripe);
    if (stripeConfig == null) return null;

    for (final entry in stripeConfig.planIds.entries) {
      if (entry.value == priceId) {
        return entry.key;
      }
    }
    return null;
  }

  SubscriptionPlan? _mapPayPalPlanToPlan(String planId) {
    // Get PayPal plan mappings from PaymentManager configuration
    final paypalConfig = PaymentManager.instance.getConfig(PaymentProvider.paypal);
    if (paypalConfig == null) return null;

    for (final entry in paypalConfig.planIds.entries) {
      if (entry.value == planId) {
        return entry.key;
      }
    }
    return null;
  }

  Map<String, int> _getPlanCredits(SubscriptionPlan plan) {
    switch (plan) {
      case SubscriptionPlan.basic:
        return {'chat': 13000, 'voice': 80};
      case SubscriptionPlan.premium:
        return {'chat': 20000, 'voice': 300};
      case SubscriptionPlan.free:
        return {'chat': 25, 'voice': 5};
    }
  }

  Future<void> dispose() async {
    await _lemonSqueezySubscription?.cancel();
    await _stripeSubscription?.cancel();
    await _paypalSubscription?.cancel();

    _lemonSqueezySubscription = null;
    _stripeSubscription = null;
    _paypalSubscription = null;

    debugPrint('WebhookProcessor: Disposed all webhook listeners');
  }
}
